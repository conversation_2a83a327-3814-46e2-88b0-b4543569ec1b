import{i as N,j as e,H as f,B as x,L as v,C as n,a as r,b as i,c as m}from"./app-DL-qYY5V.js";import{n as b}from"./navigation-DAA2N51J.js";import{A as _,b as w,c as k,d as C,B as a}from"./app-layout-YqstQnqE.js";import{A as h}from"./arrow-left-DCW23wrL.js";import{C as D}from"./calendar-Bzuvt9Ns.js";import{C as o}from"./clock-4mJquAMZ.js";import{U as p}from"./users-DNGXY-sJ.js";import"./index-1tPPImJd.js";import"./index-BTzg1GwG.js";import"./app-logo-icon-BnXlkpcX.js";import"./bus-CKTBKI0T.js";import"./dollar-sign-Cx0-nQIX.js";import"./clipboard-list-CxRumT8v.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const T=[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]],E=N("Building",T);function V({driver:t,history:l,userRole:j}){const c=[{title:"Dashboard",href:"/dashboard"},{title:j==="minibus owner"?"My Drivers":"Driver Management",href:"/drivers"},{title:`${t.first_name} ${t.last_name}`,href:`/drivers/${t.id}`},{title:"Employment History",href:"#"}],g=s=>{switch(s){case"hired":return e.jsx(p,{className:"h-4 w-4 text-green-500"});case"cleared":return e.jsx(E,{className:"h-4 w-4 text-red-500"});case"rehired":return e.jsx(p,{className:"h-4 w-4 text-blue-500"});default:return e.jsx(o,{className:"h-4 w-4 text-gray-500"})}},u=s=>{switch(s){case"hired":return e.jsx(a,{variant:"default",className:"bg-green-600",children:"Hired"});case"cleared":return e.jsx(a,{variant:"destructive",children:"Cleared"});case"rehired":return e.jsx(a,{variant:"default",className:"bg-blue-600",children:"Rehired"});default:return e.jsx(a,{variant:"outline",children:"Unknown"})}},d=s=>s?new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}):"N/A";return e.jsxs(_,{breadcrumbs:c,children:[e.jsx(f,{title:`Employment History - ${t.first_name} ${t.last_name}`}),e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx("div",{className:"max-w-4xl mx-auto",children:e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 items-center sm:justify-between mb-8",children:[e.jsxs(x,{variant:"outline",onClick:()=>b(c),className:"w-full sm:w-auto",children:[e.jsx("span",{className:"block sm:hidden",children:e.jsxs(w,{children:[e.jsx(k,{asChild:!0,children:e.jsx("span",{children:e.jsx(h,{className:"h-4 w-4"})})}),e.jsx(C,{children:"Back"})]})}),e.jsxs("span",{className:"hidden sm:flex items-center",children:[e.jsx(h,{className:"mr-2 h-4 w-4"}),"Back"]})]}),e.jsx("h1",{className:"text-2xl font-bold text-center sm:text-left",children:"Driver Employment History"}),e.jsx("span",{className:"text-muted-foreground text-base font-medium",children:"View the full employment history for this driver."})]}),e.jsx(v,{href:route("drivers.show",t.id),children:e.jsx(x,{variant:"outline",size:"sm",className:"mb-8",children:"View Driver Details"})}),e.jsxs(n,{className:"mb-8",children:[e.jsx(r,{children:e.jsx(i,{children:"Driver Information"})}),e.jsx(m,{children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Driver Name"}),e.jsxs("p",{className:"text-lg font-medium",children:[t.first_name," ",t.last_name]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Phone Number"}),e.jsx("p",{className:"text-lg",children:t.phone_number})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Current Status"}),e.jsx("p",{className:"text-lg",children:t.archived?e.jsx(a,{variant:"destructive",children:"Archived"}):e.jsx(a,{variant:"default",children:"Active"})})]})]})})]}),e.jsxs(n,{children:[e.jsx(r,{children:e.jsxs(i,{className:"flex items-center gap-2",children:[e.jsx(D,{className:"h-5 w-5"}),"Employment Timeline"]})}),e.jsx(m,{children:l.length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(o,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),e.jsx("div",{className:"text-lg font-medium text-gray-700 mb-1",children:"No employment history found."}),e.jsx("div",{className:"text-gray-500 text-sm",children:"Employment records will appear here as the driver's employment changes."})]}):e.jsx("div",{className:"space-y-6",children:l.map((s,y)=>e.jsxs("div",{className:"relative",children:[y<l.length-1&&e.jsx("div",{className:"absolute left-6 top-12 w-0.5 h-16 bg-gray-200"}),e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"flex-shrink-0 w-12 h-12 rounded-full bg-gray-100 border-2 border-gray-200 flex items-center justify-center",children:g(s.employment_change_type)}),e.jsx("div",{className:"flex-1 min-w-0",children:e.jsxs(n,{children:[e.jsx(r,{className:"pb-3",children:e.jsxs(i,{className:"flex items-center justify-between text-base",children:[e.jsxs("span",{className:"flex items-center gap-2",children:[u(s.employment_change_type),e.jsxs("span",{children:[s.employment_change_type==="hired"&&"Driver Hired",s.employment_change_type==="cleared"&&"Driver Cleared",s.employment_change_type==="rehired"&&"Driver Rehired"]})]}),e.jsx("span",{className:"text-sm text-gray-500",children:d(s.created_at)})]})}),e.jsx(m,{className:"pt-0",children:e.jsxs("div",{className:"space-y-3",children:[s.previous_owner&&e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Previous Owner"}),e.jsxs("p",{className:"text-sm",children:[s.previous_owner.first_name," ",s.previous_owner.last_name,e.jsxs("span",{className:"text-gray-500 ml-2",children:["(",s.previous_owner.phone_number,")"]})]})]}),s.new_owner&&e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"New Owner"}),e.jsxs("p",{className:"text-sm",children:[s.new_owner.first_name," ",s.new_owner.last_name,e.jsxs("span",{className:"text-gray-500 ml-2",children:["(",s.new_owner.phone_number,")"]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.employment_start_date&&e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Employment Start"}),e.jsx("p",{className:"text-sm",children:d(s.employment_start_date)})]}),s.employment_end_date&&e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Employment End"}),e.jsx("p",{className:"text-sm",children:d(s.employment_end_date)})]})]}),s.reason&&e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Reason"}),e.jsx("p",{className:"text-sm p-2 bg-gray-50 rounded",children:s.reason})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Status"}),e.jsx("p",{className:"text-sm",children:s.status==="completed"?e.jsx(a,{variant:"default",className:"bg-green-600 text-xs",children:"Completed"}):s.status==="pending"?e.jsx(a,{variant:"outline",className:"text-amber-600 border-amber-600 text-xs",children:"Pending"}):e.jsx(a,{variant:"outline",className:"text-xs",children:s.status})})]})]})})]})})]})]},s.id))})})]}),l.length>0&&e.jsxs(n,{className:"mt-6",children:[e.jsx(r,{children:e.jsx(i,{children:"Employment Summary"})}),e.jsx(m,{children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:l.length}),e.jsx("div",{className:"text-sm text-gray-600",children:"Total Records"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-green-600",children:l.filter(s=>s.employment_change_type==="hired").length}),e.jsx("div",{className:"text-sm text-gray-600",children:"Times Hired"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-red-600",children:l.filter(s=>s.employment_change_type==="cleared").length}),e.jsx("div",{className:"text-sm text-gray-600",children:"Times Cleared"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-blue-600",children:l.filter(s=>s.employment_change_type==="rehired").length}),e.jsx("div",{className:"text-sm text-gray-600",children:"Times Rehired"})]})]})})]})]})})})]})}export{V as default};
