import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { User, Users } from 'lucide-react';

export default function AdminDashboard({ user, users = [] }) {
    // Count users by role
    const roleCounts = users.reduce((acc, u) => {
        const role = u.roles && u.roles.length > 0 ? u.roles[0].name : 'Unknown';
        acc[role] = (acc[role] || 0) + 1;
        return acc;
    }, {});

    return (
        <div className="space-y-8 p-4">
            {/* Personal Details */}
            <Card className="shadow-md">
                <CardHeader>
                    <CardTitle className="flex items-center gap-2"><User className="h-5 w-5 text-blue-600" /> Personal Details</CardTitle>
                </CardHeader>
                <CardContent>
                    <p className="text-gray-500 mb-2">Your basic profile information.</p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div><span className="font-semibold">Name:</span> {user.first_name} {user.last_name}</div>
                        <div><span className="font-semibold">Email:</span> {user.email}</div>
                        <div><span className="font-semibold">Phone:</span> {user.phone_number}</div>
                        <div><span className="font-semibold">District:</span> {user.district}</div>
                    </div>
                </CardContent>
            </Card>
            {/* User Statistics */}
            <Card className="shadow-md">
                <CardHeader>
                    <CardTitle className="flex items-center gap-2"><Users className="h-5 w-5 text-green-600" /> User Statistics</CardTitle>
                </CardHeader>
                <CardContent>
                    <p className="text-gray-500 mb-2">Number of users by role in the system.</p>
                    <div className="flex flex-wrap gap-8">
                        {Object.entries(roleCounts).map(([role, count]) => (
                            <div key={role} className="text-center">
                                <div className="text-2xl font-bold text-green-700">{count}</div>
                                <div className="text-gray-600 capitalize">{role}</div>
                            </div>
                        ))}
                    </div>
                </CardContent>
            </Card>
        </div>
    );
} 