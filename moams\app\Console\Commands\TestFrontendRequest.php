<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use App\Models\User;
use App\Models\Membership;
use App\Models\FeeSetting;

class TestFrontendRequest extends Command
{
    protected $signature = 'test:frontend-request';
    protected $description = 'Test payment request exactly as frontend sends it';

    public function handle()
    {
        $this->info('Testing payment request exactly as frontend sends it...');

        try {
            // Get test data
            $user = User::first();
            $membership = Membership::first();
            
            if (!$user || !$membership) {
                $this->error('No user or membership found for testing');
                return;
            }

            // Get current fees exactly as frontend would
            $registrationFee = FeeSetting::where('fee_type', 'registration')->where('is_active', true)->first();
            $affiliationFee = FeeSetting::where('fee_type', 'affiliation')->where('is_active', true)->first();

            $currentFees = [
                'registration' => $registrationFee ? $registrationFee->amount : 0,
                'affiliation' => $affiliationFee ? $affiliationFee->amount : 0,
            ];

            $this->info("Current fees: " . json_encode($currentFees));
            $this->info("Membership type: " . $membership->type);

            // Calculate amount exactly as frontend does
            $feeType = strtolower($membership->type);
            $amount = $feeType === 'registration' ? $currentFees['registration'] : $currentFees['affiliation'];

            $this->info("Fee type: {$feeType}");
            $this->info("Amount: {$amount}");

            // Test payment data exactly as frontend sends
            $paymentData = [
                'user_id' => $user->id,
                'membership_id' => $membership->id,
                'fee_type' => $feeType,
                'amount' => $amount,
            ];

            $this->info('Payment data (as frontend sends): ' . json_encode($paymentData));

            // Make request exactly as frontend does (JSON)
            $this->info('Making JSON request (as frontend does)...');
            
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'X-CSRF-TOKEN' => csrf_token(),
            ])->post(route('payments.ctechpay.initiate'), $paymentData);

            $this->info('Response status: ' . $response->status());
            $this->info('Response body: ' . $response->body());

            if ($response->successful()) {
                $data = $response->json();
                $this->info('Response data: ' . json_encode($data, JSON_PRETTY_PRINT));
                
                if (isset($data['payment_page_url']) && $data['payment_page_url']) {
                    $this->info('✅ SUCCESS: Payment URL received: ' . $data['payment_page_url']);
                } else {
                    $this->error('❌ FAILED: No payment URL in response');
                    if (isset($data['error'])) {
                        $this->error('Error: ' . $data['error']);
                    }
                }
            } else {
                $this->error('❌ FAILED: HTTP error ' . $response->status());
                $this->error('Error body: ' . $response->body());
            }

        } catch (\Exception $e) {
            $this->error('❌ EXCEPTION: ' . $e->getMessage());
            $this->error('Stack trace: ' . $e->getTraceAsString());
        }
    }
}
