import{d as S,r as g,j as e,B as t,e as l,L as c,C as f,a as u,b as j,c as b}from"./app-DL-qYY5V.js";import{A as k,C as A}from"./app-layout-YqstQnqE.js";import{n as F}from"./navigation-DAA2N51J.js";import{I as M}from"./input-Dm4SEXxy.js";import{L as N}from"./label-e3QxUH-L.js";import{C as v}from"./chevron-left-DFeVEtK7.js";import{C as L}from"./chart-column-DHsDidJk.js";import{P}from"./plus-CU6rIcI2.js";import{S as y}from"./search-B4sum6Qx.js";import{F as T}from"./filter-DPFCjsfq.js";import{U as w}from"./users-DNGXY-sJ.js";import"./index-1tPPImJd.js";import"./index-BTzg1GwG.js";import"./app-logo-icon-BnXlkpcX.js";import"./bus-CKTBKI0T.js";import"./dollar-sign-Cx0-nQIX.js";import"./clipboard-list-CxRumT8v.js";function Z({minibusOwners:r}){const{userRoles:o,flash:z}=S().props,i=o&&o.includes("association clerk"),[n,x]=g.useState(""),[m,h]=g.useState("all"),d=(r==null?void 0:r.data)||r||[],a=r!=null&&r.data?r:null,C=s=>{x(s),l.get("/membership-management",{search:s,status:m},{preserveState:!0,preserveScroll:!0})},_=s=>{h(s),l.get("/membership-management",{search:n,status:s},{preserveState:!0,preserveScroll:!0})},p=[{title:"Dashboard",href:"/dashboard"},{title:"Membership Management",href:"/membership-management"}];return e.jsx(k,{breadcrumbs:p,children:e.jsx("div",{className:"w-full max-w-full overflow-hidden",children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex flex-col gap-4 mb-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4",children:[e.jsxs("button",{className:"flex items-center px-3 py-1 rounded border w-fit",onClick:()=>F(p),children:[e.jsx(v,{className:"h-4 w-4 mr-1"})," Back"]}),e.jsx("div",{className:"flex-1 sm:flex-none",children:e.jsx("span",{className:"text-muted-foreground text-sm sm:text-base font-medium break-words",children:"Browse all minibus owners and view their membership records."})})]}),e.jsxs("div",{className:"flex flex-wrap items-center gap-2 justify-start sm:justify-end",children:[i&&e.jsxs(t,{variant:"outline",className:"bg-green-100 text-green-800 border-green-300 hover:bg-green-200 flex items-center gap-2",onClick:()=>l.visit("/payment-analytics"),children:[e.jsx(L,{className:"h-4 w-4"}),"Payment Analytics"]}),i&&e.jsx(c,{href:"/membership-management/create",children:e.jsxs(t,{className:"w-fit flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white px-4 py-2",children:[e.jsx(P,{className:"h-4 w-4"}),"Add New Member"]})})]})]}),e.jsxs(f,{className:"mb-6 w-full",children:[e.jsx(u,{children:e.jsxs(j,{className:"flex items-center gap-2",children:[e.jsx(y,{className:"h-5 w-5"}),"Search & Filter"]})}),e.jsx(b,{children:e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx(N,{htmlFor:"search",children:"Search Members"}),e.jsxs("div",{className:"relative",children:[e.jsx(y,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),e.jsx(M,{id:"search",placeholder:"Search by name, email...",value:n,onChange:s=>C(s.target.value),className:"pl-10"})]})]}),e.jsxs("div",{children:[e.jsx(N,{htmlFor:"status-filter",className:"text-sm mb-1 block",children:"Status"}),e.jsxs("select",{id:"status-filter",value:m,onChange:s=>_(s.target.value),className:"w-full px-3 py-2 text-base border border-gray-300 rounded-md",children:[e.jsx("option",{value:"all",children:"All Members"}),e.jsx("option",{value:"active",children:"Active"}),e.jsx("option",{value:"archived",children:"Archived"})]})]}),e.jsx("div",{className:"flex items-end",children:e.jsxs(t,{variant:"outline",onClick:()=>{x(""),h("all"),l.get("/membership-management")},className:"w-full px-3 py-2 text-base border border-gray-300 rounded-md",children:[e.jsx(T,{className:"h-4 w-4 mr-2"}),"Clear Filters"]})})]})})]}),e.jsx("div",{className:"w-full",style:{maxWidth:"calc(100vw - 2rem)",overflow:"hidden"},children:e.jsxs(f,{className:"w-full border-0 shadow-none",children:[e.jsx(u,{className:"px-0",children:e.jsxs(j,{className:"flex items-center gap-2",children:[e.jsx(w,{className:"h-5 w-5"}),"Minibus Owners (",(a==null?void 0:a.total)||d.length,")"]})}),e.jsx(b,{className:"p-0",children:d.length===0?e.jsxs("div",{className:"text-center py-8 px-6",children:[e.jsx(w,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),e.jsx("h3",{className:"text-lg font-medium mb-2",children:"No minibus owners found"}),e.jsx("p",{className:"text-muted-foreground mb-4",children:n||m!=="all"?"Try adjusting your search or filter criteria.":"No minibus owners have been registered yet."})]}):e.jsx("div",{className:"overflow-x-auto mb-8",children:e.jsxs("table",{className:"min-w-full w-full bg-white border border-gray-200 rounded-lg",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"bg-gray-100 text-gray-700",children:[e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Name"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Email"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Phone"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Joined"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Actions"})]})}),e.jsx("tbody",{children:d.map(s=>e.jsxs("tr",{className:"border-b hover:bg-gray-50",children:[e.jsxs("td",{className:"px-4 py-3 font-medium",children:[s.first_name," ",s.last_name]}),e.jsx("td",{className:"px-4 py-3",children:s.email}),e.jsx("td",{className:"px-4 py-3",children:s.phone_number}),e.jsx("td",{className:"px-4 py-3",children:s.joining_date?new Date(s.joining_date).toLocaleDateString():"N/A"}),e.jsx("td",{className:"px-4 py-3",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(c,{href:`/membership-management/${s.id}`,children:e.jsxs(t,{variant:"outline",size:"sm",className:"bg-green-100 text-green-800 border-green-300 hover:bg-green-200 flex items-center gap-2",children:["View Memberships",s.unpaid_memberships_count>0&&e.jsx("span",{className:"bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full",children:s.unpaid_memberships_count})]})}),i&&e.jsx(c,{href:`/membership-management/${s.id}/edit`,children:e.jsx(t,{variant:"secondary",size:"sm",className:"bg-green-100 text-green-800 border-green-300 hover:bg-green-200",children:"Edit Member"})})]})})]},s.id))})]})})})]})}),a&&a.total>0&&e.jsx("div",{className:"mt-6 w-full",children:e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 px-4 py-4 border-t bg-gray-50/50 rounded-b-lg",children:[e.jsxs("div",{className:"text-sm text-gray-700 text-center sm:text-left",children:["Showing ",a.from," to ",a.to," of ",a.total," results"]}),a.last_page>1&&e.jsxs("div",{className:"flex items-center justify-center sm:justify-end space-x-2",children:[e.jsxs(t,{variant:"outline",size:"sm",onClick:()=>l.get(a.prev_page_url),disabled:!a.prev_page_url,className:"flex items-center gap-1 bg-green-100 text-green-800 border-green-300 hover:bg-green-200 disabled:bg-gray-100 disabled:text-gray-400 disabled:border-gray-300",children:[e.jsx(v,{className:"h-4 w-4"}),e.jsx("span",{className:"hidden sm:inline",children:"Previous"}),e.jsx("span",{className:"sm:hidden",children:"Prev"})]}),e.jsxs("span",{className:"text-sm text-gray-600 px-2",children:[a.current_page," of ",a.last_page]}),e.jsxs(t,{variant:"outline",size:"sm",onClick:()=>l.get(a.next_page_url),disabled:!a.next_page_url,className:"flex items-center gap-1 bg-green-100 text-green-800 border-green-300 hover:bg-green-200 disabled:bg-gray-100 disabled:text-gray-400 disabled:border-gray-300",children:[e.jsx("span",{className:"hidden sm:inline",children:"Next"}),e.jsx("span",{className:"sm:hidden",children:"Next"}),e.jsx(A,{className:"h-4 w-4"})]})]})]})})]})})})}export{Z as default};
