import React from 'react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { XCircle, CreditCard, RefreshCw, ArrowLeft } from 'lucide-react';
import { Link } from '@inertiajs/react';
import { route } from 'ziggy-js';

export default function PaymentCancelled({ payment, associationClerk }) {
    // Define breadcrumbs for payment cancelled
    const breadcrumbs = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'My Membership', href: '/my-membership' },
        { title: 'Payment Cancelled', href: '#' },
    ];

    return (
        <AppLayout title="Payment Cancelled" breadcrumbs={breadcrumbs}>
            <div className="max-w-2xl mx-auto py-8 px-4">
                {/* Cancelled Header */}
                <div className="text-center mb-8">
                    <div className="flex justify-center mb-4">
                        <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center">
                            <XCircle className="h-10 w-10 text-yellow-600" />
                        </div>
                    </div>
                    <h1 className="text-3xl font-bold text-yellow-700 mb-2">
                        Payment Cancelled
                    </h1>
                    <p className="text-gray-600">
                        You cancelled the payment process
                    </p>
                </div>

                {/* Payment Details Card */}
                <Card className="mb-6 border-yellow-200 bg-yellow-50">
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2 text-yellow-800">
                            <CreditCard className="h-5 w-5" />
                            Payment Details
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <div className="text-sm text-gray-600">Amount</div>
                                <div className="font-semibold text-yellow-800">
                                    MWK {parseFloat(payment.amount).toLocaleString()}
                                </div>
                            </div>

                            <div>
                                <div className="text-sm text-gray-600">Fee Type</div>
                                <div className="font-semibold text-yellow-800 capitalize">
                                    {payment.fee_type}
                                </div>
                            </div>

                            <div>
                                <div className="text-sm text-gray-600">Payment Method</div>
                                <div className="font-semibold text-yellow-800">
                                    {payment.payment_method === 'ctechpay_card' ? 'Card Payment' : payment.payment_method}
                                </div>
                            </div>

                            <div>
                                <div className="text-sm text-gray-600">Cancelled At</div>
                                <div className="font-semibold text-yellow-800">
                                    {new Date(payment.updated_at || payment.created_at).toLocaleString()}
                                </div>
                            </div>
                        </div>

                        {/* Order Reference if available */}
                        {payment.ctechpay_order_reference && (
                            <div className="pt-4 border-t border-yellow-200">
                                <div className="text-sm text-gray-600 mb-2">Order Reference</div>
                                <div className="font-mono text-sm text-yellow-800">
                                    {payment.ctechpay_order_reference}
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Information Card */}
                <Card className="mb-6">
                    <CardHeader>
                        <CardTitle>What Happened?</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            <p className="text-gray-700">
                                You chose to cancel the payment process before it was completed.
                                No charges have been made to your account or payment method.
                            </p>

                            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <div className="flex items-start gap-3">
                                    <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                        <span className="text-blue-600 text-sm">ℹ</span>
                                    </div>
                                    <div>
                                        <div className="font-medium text-blue-800">No Payment Processed</div>
                                        <div className="text-sm text-blue-700 mt-1">
                                            Your membership status remains unchanged, and you can try the payment again at any time.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Next Steps */}
                <Card className="mb-6">
                    <CardHeader>
                        <CardTitle>What's Next?</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-3">
                            <div className="flex items-start gap-3">
                                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                    <span className="text-blue-600 text-sm font-semibold">1</span>
                                </div>
                                <div>
                                    <div className="font-medium">Try Payment Again</div>
                                    <div className="text-sm text-gray-600">
                                        You can retry the payment process with the same or different payment method
                                    </div>
                                </div>
                            </div>

                            <div className="flex items-start gap-3">
                                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                    <span className="text-blue-600 text-sm font-semibold">2</span>
                                </div>
                                <div>
                                    <div className="font-medium">Alternative Payment Methods</div>
                                    <div className="text-sm text-gray-600">
                                        Consider using a different payment method if you encountered issues
                                    </div>
                                </div>
                            </div>

                            <div className="flex items-start gap-3">
                                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                    <span className="text-blue-600 text-sm font-semibold">3</span>
                                </div>
                                <div>
                                    <div className="font-medium">Contact Support</div>
                                    <div className="text-sm text-gray-600">
                                        If you need assistance or have questions about the payment process
                                    </div>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-3 justify-center">
                    <Button asChild variant="outline">
                        <Link href={route('dashboard')}>
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Return to Dashboard
                        </Link>
                    </Button>

                    <Button asChild className="bg-blue-600 hover:bg-blue-700">
                        <Link href={route('my.membership')}>
                            <RefreshCw className="h-4 w-4 mr-2" />
                            Try Payment Again
                        </Link>
                    </Button>
                </div>

                {/* Support Information */}
                <div className="mt-8 text-center text-sm text-gray-500">
                    {associationClerk ? (
                        <p>
                            Need help with payments? Contact {associationClerk.first_name} {associationClerk.last_name} at{' '}
                            <a href={`mailto:${associationClerk.email}`} className="text-blue-600 hover:underline">
                                {associationClerk.email}
                            </a>{' '}
                            or call {associationClerk.phone_number}
                        </p>
                    ) : (
                        <p>
                            Need help with payments? Contact MOAM support for assistance.
                        </p>
                    )}
                </div>
            </div>
        </AppLayout>
    );
}
