import{u as O,r as p,T as r,j as s,H as E,B as t,C as a,a as l,b as n,c as d,f as y,e as g}from"./app-DL-qYY5V.js";import{n as w}from"./navigation-DAA2N51J.js";import{L as H}from"./label-e3QxUH-L.js";import{T as V}from"./textarea-SHrtPYpi.js";import{A as $,b as M,c as F,d as U,U as Q,B as v}from"./app-layout-YqstQnqE.js";import{I as X}from"./input-error-C6jcuIY6.js";import{C as z}from"./confirm-dialog-B1e93Onq.js";import{A as C}from"./arrow-left-DCW23wrL.js";import{C as _}from"./circle-x-DliR3-rL.js";import{T as G}from"./trash-2-B_fK6WNg.js";import{C as J}from"./clock-4mJquAMZ.js";import"./index-BTzg1GwG.js";import"./index-1tPPImJd.js";import"./app-logo-icon-BnXlkpcX.js";import"./bus-CKTBKI0T.js";import"./dollar-sign-Cx0-nQIX.js";import"./users-DNGXY-sJ.js";import"./clipboard-list-CxRumT8v.js";import"./dialog-CXtul0Wy.js";function js({clearanceRequest:e,userRole:c,auth:m}){var b;const{data:f,setData:o,patch:D,processing:x,errors:k}=O({action:"",admin_notes:""}),[T,h]=p.useState(!1),[A,u]=p.useState(!1),[N,K]=p.useState(null),S=()=>{h(!0)},B=()=>h(!1),I=async()=>{u(!0),await window.axios.delete(r("drivers.clearance-requests.destroy",e.id)).then(()=>g.visit(r("drivers.show",e.driver.id))),u(!1),h(!1)},j=[{title:"Dashboard",href:"/dashboard"},{title:c==="minibus owner"?"My Drivers":"Driver Management",href:"/drivers"},{title:"Clearance Requests",href:r("drivers.clearance-requests.index")},{title:`Request #${e.id}`,href:r("drivers.clearance-requests.show",e.id)}],P=i=>{switch(i){case"pending":return s.jsx(J,{className:"h-5 w-5 text-amber-500"});case"approved":return s.jsx(y,{className:"h-5 w-5 text-green-500"});case"rejected":return s.jsx(_,{className:"h-5 w-5 text-red-500"});default:return null}},L=i=>{switch(i){case"pending":return s.jsx(v,{variant:"outline",className:"text-amber-600 border-amber-600",children:"Pending Review"});case"approved":return s.jsx(v,{variant:"default",className:"bg-green-600",children:"Approved"});case"rejected":return s.jsx(v,{variant:"destructive",children:"Rejected"});default:return null}};return s.jsxs($,{breadcrumbs:j,children:[s.jsx(E,{title:`Clearance Request #${e.id}`}),s.jsx("div",{className:"container mx-auto px-4 py-8",children:s.jsxs("div",{className:"max-w-4xl mx-auto",children:[s.jsxs("div",{className:"flex flex-col sm:flex-row gap-2 sm:gap-4 items-center sm:items-center sm:justify-between mb-6",children:[s.jsxs(t,{variant:"outline",onClick:()=>w(j),className:"w-full sm:w-auto",children:[s.jsx("span",{className:"block sm:hidden",children:s.jsxs(M,{children:[s.jsx(F,{asChild:!0,children:s.jsx("span",{children:s.jsx(C,{className:"h-4 w-4"})})}),s.jsx(U,{children:"Back"})]})}),s.jsxs("span",{className:"hidden sm:flex items-center",children:[s.jsx(C,{className:"mr-2 h-4 w-4"}),"Back"]})]}),s.jsx("h1",{className:"text-2xl font-bold text-center sm:text-left",children:"Clearance Request Details"})]}),s.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[s.jsxs("div",{className:"lg:col-span-2 space-y-6",children:[s.jsxs(a,{children:[s.jsx(l,{children:s.jsxs(n,{className:"flex items-center gap-2",children:[s.jsx(Q,{className:"h-5 w-5"}),"Request Information"]})}),s.jsxs(d,{className:"space-y-4",children:[s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Request ID"}),s.jsxs("p",{className:"text-lg font-mono",children:["#",e.id]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Status"}),s.jsxs("div",{className:"flex items-center gap-2",children:[P(e.status),L(e.status)]})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Reason for Clearance"}),s.jsx("p",{className:"text-lg mt-1 p-3 bg-gray-50 rounded-md",children:e.reason})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Submitted On"}),s.jsx("p",{className:"text-lg",children:new Date(e.created_at).toLocaleString()})]}),e.processed_at&&s.jsxs("div",{children:[s.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Processed On"}),s.jsx("p",{className:"text-lg",children:new Date(e.processed_at).toLocaleString()})]})]})]})]}),s.jsxs(a,{children:[s.jsx(l,{children:s.jsx(n,{children:"Driver Information"})}),s.jsxs(d,{className:"space-y-4",children:[s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Driver Name"}),s.jsxs("p",{className:"text-lg font-medium",children:[e.driver.first_name," ",e.driver.last_name]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Phone Number"}),s.jsx("p",{className:"text-lg",children:e.driver.phone_number})]})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"text-sm font-medium text-gray-500",children:"District"}),s.jsx("p",{className:"text-lg",children:e.driver.district})]}),s.jsxs("div",{children:[s.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Village/Town"}),s.jsx("p",{className:"text-lg",children:e.driver.village_town})]})]})]})]}),s.jsxs(a,{children:[s.jsx(l,{children:s.jsx(n,{children:"Minibus Owner Information"})}),s.jsxs(d,{className:"space-y-4",children:[s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Owner Name"}),s.jsxs("p",{className:"text-lg font-medium",children:[e.owner.first_name," ",e.owner.last_name]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Phone Number"}),s.jsx("p",{className:"text-lg",children:e.owner.phone_number})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Email"}),s.jsx("p",{className:"text-lg",children:e.owner.email})]})]})]}),e.status==="pending"&&s.jsxs(a,{children:[s.jsx(l,{children:s.jsx(n,{children:"Process Request"})}),s.jsx(d,{children:s.jsxs("form",{onSubmit:i=>{i.preventDefault(),D(r("drivers.clearance-requests.process",e.id))},className:"space-y-4",children:[s.jsx("input",{type:"hidden",name:"action",value:f.action}),s.jsxs("div",{children:[s.jsx(H,{htmlFor:"admin_notes",children:"Administrative Notes (Optional)"}),s.jsx(V,{id:"admin_notes",value:f.admin_notes,onChange:i=>o("admin_notes",i.target.value),placeholder:"Add any notes about your decision...",className:"mt-1"}),s.jsx(X,{message:k.admin_notes,className:"mt-1"})]}),s.jsxs("div",{className:"flex justify-end gap-4 pt-4",children:[s.jsx(t,{type:"button",variant:"outline",onClick:()=>w(j),disabled:x,children:"Cancel"}),s.jsxs(t,{type:"submit",variant:"destructive",disabled:x,onClick:()=>o("action","reject"),children:[s.jsx(_,{className:"h-4 w-4 mr-2"}),N==="reject"?"Rejecting...":"Reject Request"]}),s.jsxs(t,{type:"submit",disabled:x,onClick:()=>o("action","approve"),children:[s.jsx(y,{className:"h-4 w-4 mr-2"}),N==="approve"?"Approving...":"Approve Request"]})]})]})})]}),e.status!=="pending"&&s.jsxs(a,{children:[s.jsx(l,{children:s.jsx(n,{children:"Processing Information"})}),s.jsxs(d,{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Processed By"}),s.jsx("p",{className:"text-lg",children:e.processed_by?`${e.processed_by.first_name} ${e.processed_by.last_name}`:"System"})]}),e.admin_notes&&s.jsxs("div",{children:[s.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Administrative Notes"}),s.jsx("p",{className:"text-lg mt-1 p-3 bg-gray-50 rounded-md",children:e.admin_notes})]})]})]})]}),s.jsxs("div",{className:"space-y-6",children:[s.jsxs(a,{children:[s.jsx(l,{children:s.jsx(n,{children:"Quick Actions"})}),s.jsxs(d,{className:"space-y-3",children:[s.jsx(t,{variant:"outline",className:"w-full justify-start",onClick:()=>g.visit(r("drivers.show",e.driver.id)),children:"View Driver Details"}),s.jsx(t,{variant:"outline",className:"w-full justify-start",onClick:()=>g.visit(r("drivers.history",e.driver.id)),children:"View Employment History"}),(c==="association clerk"||c==="minibus owner"&&((b=m==null?void 0:m.user)==null?void 0:b.id)===e.owner_id&&e.status==="pending")&&s.jsxs(t,{variant:"destructive",className:"w-full justify-start",onClick:S,children:[s.jsx(G,{className:"mr-2 h-4 w-4"}),"Delete Request"]})]})]}),s.jsxs(a,{children:[s.jsx(l,{children:s.jsx(n,{children:"Important Notes"})}),s.jsx(d,{className:"space-y-2",children:s.jsxs("div",{className:"text-sm text-gray-600",children:[s.jsx("p",{children:"• Approving will archive the driver"}),s.jsx("p",{children:"• The owner will be notified of your decision"}),s.jsx("p",{children:"• Employment history will be updated"}),s.jsx("p",{children:"• Driver can be unarchived later if needed"})]})})]})]})]})]})}),s.jsx(z,{open:T,title:"Delete Clearance Request?",description:"This action cannot be undone and will permanently remove the clearance request.",confirmText:"Delete",confirmVariant:"destructive",loading:A,onCancel:B,onConfirm:I})]})}export{js as default};
