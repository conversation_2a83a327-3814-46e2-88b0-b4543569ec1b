import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { <PERSON><PERSON><PERSON>riangle, Refresh<PERSON>w, Home, Bug, Co<PERSON>, CheckCircle } from 'lucide-react';

class ErrorBoundary extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            hasError: false,
            error: null,
            errorInfo: null,
            errorId: null,
            retryCount: 0,
            showDetails: false,
            copied: false,
        };
    }

    static getDerivedStateFromError(error) {
        // Update state so the next render will show the fallback UI
        return { hasError: true };
    }

    componentDidCatch(error, errorInfo) {
        // Generate error ID for tracking
        const errorId = `js_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        // Log error details
        console.error('React Error Boundary caught an error:', error, errorInfo);
        
        // Send error to monitoring service
        this.logErrorToService(error, errorInfo, errorId);
        
        this.setState({
            error,
            errorInfo,
            errorId,
        });
    }

    logErrorToService = async (error, errorInfo, errorId) => {
        try {
            const errorData = {
                error_id: errorId,
                type: 'javascript_error',
                message: error.message,
                stack: error.stack,
                component_stack: errorInfo.componentStack,
                url: window.location.href,
                user_agent: navigator.userAgent,
                timestamp: new Date().toISOString(),
                props: this.props.errorContext || {},
            };

            // Send to backend error monitoring endpoint
            await fetch('/api/errors/javascript', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'),
                },
                body: JSON.stringify(errorData),
            });
        } catch (logError) {
            console.error('Failed to log error to service:', logError);
        }
    };

    handleRetry = () => {
        this.setState(prevState => ({
            hasError: false,
            error: null,
            errorInfo: null,
            retryCount: prevState.retryCount + 1,
            showDetails: false,
            copied: false,
        }));
    };

    handleReload = () => {
        window.location.reload();
    };

    handleGoHome = () => {
        window.location.href = '/dashboard';
    };

    toggleDetails = () => {
        this.setState(prevState => ({
            showDetails: !prevState.showDetails,
        }));
    };

    copyErrorDetails = async () => {
        const errorDetails = `
Error ID: ${this.state.errorId}
Error: ${this.state.error?.message}
Component Stack: ${this.state.errorInfo?.componentStack}
Stack Trace: ${this.state.error?.stack}
URL: ${window.location.href}
Timestamp: ${new Date().toISOString()}
        `.trim();

        try {
            await navigator.clipboard.writeText(errorDetails);
            this.setState({ copied: true });
            setTimeout(() => this.setState({ copied: false }), 2000);
        } catch (err) {
            console.error('Failed to copy error details:', err);
        }
    };

    render() {
        if (this.state.hasError) {
            const { error, errorInfo, errorId, retryCount, showDetails, copied } = this.state;
            const { fallback: CustomFallback, showRetry = true, showReload = true } = this.props;

            // If a custom fallback is provided, use it
            if (CustomFallback) {
                return (
                    <CustomFallback
                        error={error}
                        errorInfo={errorInfo}
                        errorId={errorId}
                        onRetry={this.handleRetry}
                        onReload={this.handleReload}
                        retryCount={retryCount}
                    />
                );
            }

            // Default error boundary UI
            return (
                <div className="min-h-screen flex items-center justify-center p-4 bg-gray-50">
                    <Card className="max-w-2xl w-full">
                        <CardHeader className="text-center">
                            <div className="flex justify-center mb-4">
                                <AlertTriangle className="w-16 h-16 text-red-500" />
                            </div>
                            <CardTitle className="text-2xl font-bold text-red-600 mb-2">
                                Something went wrong
                            </CardTitle>
                            <p className="text-gray-600">
                                A JavaScript error occurred while rendering this page. 
                                Don't worry, this has been automatically reported to our team.
                            </p>
                        </CardHeader>
                        
                        <CardContent className="space-y-6">
                            {/* Error ID for reference */}
                            <div className="text-center">
                                <p className="text-sm text-gray-500">
                                    Error ID: <code className="bg-gray-100 px-2 py-1 rounded text-xs">{errorId}</code>
                                </p>
                            </div>

                            {/* Action buttons */}
                            <div className="space-y-3">
                                {showRetry && (
                                    <Button
                                        onClick={this.handleRetry}
                                        className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3"
                                    >
                                        <RefreshCw className="w-4 h-4 mr-2" />
                                        Try Again {retryCount > 0 && `(${retryCount})`}
                                    </Button>
                                )}

                                {showReload && (
                                    <Button
                                        onClick={this.handleReload}
                                        variant="outline"
                                        className="w-full"
                                    >
                                        <RefreshCw className="w-4 h-4 mr-2" />
                                        Reload Page
                                    </Button>
                                )}

                                <Button
                                    onClick={this.handleGoHome}
                                    variant="outline"
                                    className="w-full"
                                >
                                    <Home className="w-4 h-4 mr-2" />
                                    Go to Dashboard
                                </Button>
                            </div>

                            {/* Error details toggle */}
                            <div className="border-t pt-4">
                                <Button
                                    onClick={this.toggleDetails}
                                    variant="ghost"
                                    size="sm"
                                    className="w-full"
                                >
                                    <Bug className="w-4 h-4 mr-2" />
                                    {showDetails ? 'Hide' : 'Show'} Technical Details
                                </Button>

                                {showDetails && (
                                    <div className="mt-4 space-y-4">
                                        <Alert className="border-red-200 bg-red-50">
                                            <AlertTriangle className="h-4 w-4 text-red-600" />
                                            <AlertDescription className="text-red-800">
                                                <div className="space-y-2">
                                                    <p><strong>Error:</strong> {error?.message}</p>
                                                    {error?.stack && (
                                                        <details className="mt-2">
                                                            <summary className="cursor-pointer font-medium">Stack Trace</summary>
                                                            <pre className="mt-2 text-xs bg-red-100 p-2 rounded overflow-auto max-h-40">
                                                                {error.stack}
                                                            </pre>
                                                        </details>
                                                    )}
                                                    {errorInfo?.componentStack && (
                                                        <details className="mt-2">
                                                            <summary className="cursor-pointer font-medium">Component Stack</summary>
                                                            <pre className="mt-2 text-xs bg-red-100 p-2 rounded overflow-auto max-h-40">
                                                                {errorInfo.componentStack}
                                                            </pre>
                                                        </details>
                                                    )}
                                                </div>
                                            </AlertDescription>
                                        </Alert>

                                        <Button
                                            onClick={this.copyErrorDetails}
                                            variant="outline"
                                            size="sm"
                                            className="w-full"
                                        >
                                            {copied ? (
                                                <>
                                                    <CheckCircle className="w-4 h-4 mr-2 text-green-600" />
                                                    Copied!
                                                </>
                                            ) : (
                                                <>
                                                    <Copy className="w-4 h-4 mr-2" />
                                                    Copy Error Details
                                                </>
                                            )}
                                        </Button>
                                    </div>
                                )}
                            </div>

                            {/* Help text */}
                            <Alert className="border-blue-200 bg-blue-50">
                                <AlertDescription className="text-blue-800">
                                    If this problem persists, please contact <NAME_EMAIL> 
                                    and include the Error ID above.
                                </AlertDescription>
                            </Alert>
                        </CardContent>
                    </Card>
                </div>
            );
        }

        return this.props.children;
    }
}

export default ErrorBoundary;
