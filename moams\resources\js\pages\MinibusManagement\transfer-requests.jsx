import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { Bus, Eye, Search, Filter, ChevronLeft, ChevronRight } from 'lucide-react';
import { Head, router } from '@inertiajs/react';
import { navigateToLogicalParent } from '@/utils/navigation';
import { route } from 'ziggy-js';

export default function TransferRequests({ transferRequests, breadcrumbs, filters }) {
    const [searchTerm, setSearchTerm] = useState(filters?.search || '');
    const [statusFilter, setStatusFilter] = useState(filters?.status || 'all');

    // Get requests data (now paginated)
    const requestsData = transferRequests?.data || [];

    // Handle search and filter changes
    const handleSearch = (value) => {
        setSearchTerm(value);
        router.get('/minibuses/transfer-requests', {
            search: value,
            status: statusFilter,
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleStatusFilter = (value) => {
        setStatusFilter(value);
        router.get('/minibuses/transfer-requests', {
            search: searchTerm,
            status: value,
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleClearFilters = () => {
        setSearchTerm('');
        setStatusFilter('all');
        router.get('/minibuses/transfer-requests', {}, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const getStatusBadge = (status) => {
        switch (status) {
            case 'pending':
                return <Badge variant="outline" className="text-amber-600 border-amber-600">Pending</Badge>;
            case 'transferred':
                return <Badge variant="default" className="bg-green-600">Transferred</Badge>;
            default:
                return <Badge variant="secondary">{status}</Badge>;
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Minibus Transfer Requests" />
            <div className="container mx-auto px-4 py-8">
                <div className="flex items-center gap-4 mb-6">
                    <Button variant="outline" onClick={() => navigateToLogicalParent(breadcrumbs)}>
                        Back
                    </Button>
                    <span className="text-muted-foreground text-base font-medium">View and manage minibus transfer requests.</span>
                </div>

                {/* Search and Filter section */}
                <Card className="mb-6 w-full">
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Search className="h-5 w-5" />
                            Search & Filter
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                            {/* Search Input */}
                            <div className="md:col-span-2">
                                <Label htmlFor="search">Search</Label>
                                <div className="relative">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                                    <Input
                                        id="search"
                                        placeholder="Search by minibus, owner, reason, or type..."
                                        value={searchTerm}
                                        onChange={(e) => handleSearch(e.target.value)}
                                        className="pl-10"
                                    />
                                </div>
                            </div>

                            {/* Status Filter */}
                            <div>
                                <Label htmlFor="status">Status</Label>
                                <Select value={statusFilter} onValueChange={handleStatusFilter}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="All Requests" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Requests</SelectItem>
                                        <SelectItem value="pending">Pending</SelectItem>
                                        <SelectItem value="transferred">Transferred</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>

                            {/* Clear Filters Button */}
                            <div className="flex items-end">
                                <Button
                                    variant="outline"
                                    onClick={handleClearFilters}
                                    className="w-full"
                                >
                                    <Filter className="h-4 w-4 mr-2" />
                                    Clear Filters
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>
                {/* Requests Table */}
                <Card className="w-full border-0 shadow-none">
                    <CardHeader className="px-0">
                        <CardTitle className="flex items-center gap-2">
                            <Bus className="h-5 w-5" />
                            Transfer Requests ({transferRequests?.total || requestsData.length})
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="p-0">
                        {requestsData.length > 0 ? (
                            <div className="overflow-x-auto mb-8">
                                <table className="min-w-full w-full bg-white border border-gray-200 rounded-lg">
                                    <thead>
                                        <tr className="bg-gray-100 text-gray-700">
                                            <th className="px-4 py-3 text-left font-medium">Minibus</th>
                                            <th className="px-4 py-3 text-left font-medium">Owner</th>
                                            <th className="px-4 py-3 text-left font-medium">Transfer Type</th>
                                            <th className="px-4 py-3 text-left font-medium">Reason</th>
                                            <th className="px-4 py-3 text-left font-medium">Status</th>
                                            <th className="px-4 py-3 text-left font-medium">Submitted Date</th>
                                            <th className="px-4 py-3 text-left font-medium">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {requestsData.map(request => (
                                            <tr key={request.id} className="border-b hover:bg-gray-50">
                                                <td className="px-4 py-3 font-medium">
                                                    {request.minibus?.number_plate || 'N/A'}
                                                </td>
                                                <td className="px-4 py-3">
                                                    {request.owner ? `${request.owner.first_name} ${request.owner.last_name}` : 'N/A'}
                                                </td>
                                                <td className="px-4 py-3">
                                                    <Badge variant="outline" className="capitalize">
                                                        {request.transfer_type || 'N/A'}
                                                    </Badge>
                                                </td>
                                                <td className="px-4 py-3 max-w-24">
                                                    <div className="truncate" title={request.reason || 'N/A'}>
                                                        {request.reason || 'N/A'}
                                                    </div>
                                                </td>
                                                <td className="px-4 py-3">
                                                    {getStatusBadge(request.status)}
                                                </td>
                                                <td className="px-4 py-3">
                                                    {request.created_at ? new Date(request.created_at).toLocaleDateString() : 'N/A'}
                                                </td>
                                                <td className="px-4 py-3">
                                                    <Button
                                                        variant="outline"
                                                        size="sm"
                                                        onClick={() => router.visit(route('minibuses.transfer-requests.show', request.id))}
                                                    >
                                                        <Eye className="h-4 w-4 mr-2" />
                                                        {request.status === 'pending' ? 'Review' : 'View Details'}
                                                    </Button>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        ) : (
                            <div className="text-center py-8 px-6">
                                <Bus className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                                <h3 className="text-lg font-medium mb-2">No transfer requests found</h3>
                                <p className="text-muted-foreground mb-4">
                                    {searchTerm || statusFilter !== 'all'
                                        ? 'Try adjusting your search or filter criteria.'
                                        : 'No transfer requests have been submitted yet.'}
                                </p>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Pagination */}
                {transferRequests && transferRequests.total > 0 && (
                    <div className="mt-6 w-full">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 px-4 py-4 border-t bg-gray-50/50 rounded-b-lg">
                            <div className="text-sm text-gray-700 text-center sm:text-left">
                                Showing {transferRequests.from} to {transferRequests.to} of {transferRequests.total} results
                            </div>
                            {transferRequests.last_page > 1 && (
                                <div className="flex items-center justify-center sm:justify-end space-x-2">
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => router.get(transferRequests.prev_page_url)}
                                        disabled={!transferRequests.prev_page_url}
                                        className="flex items-center gap-1"
                                    >
                                        <ChevronLeft className="h-4 w-4" />
                                        <span className="hidden sm:inline">Previous</span>
                                        <span className="sm:hidden">Prev</span>
                                    </Button>

                                    <span className="text-sm text-gray-600 px-2">
                                        {transferRequests.current_page} of {transferRequests.last_page}
                                    </span>

                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => router.get(transferRequests.next_page_url)}
                                        disabled={!transferRequests.next_page_url}
                                        className="flex items-center gap-1"
                                    >
                                        <span className="hidden sm:inline">Next</span>
                                        <span className="sm:hidden">Next</span>
                                        <ChevronRight className="h-4 w-4" />
                                    </Button>
                                </div>
                            )}
                        </div>
                    </div>
                )}
            </div>
        </AppLayout>
    );
} 