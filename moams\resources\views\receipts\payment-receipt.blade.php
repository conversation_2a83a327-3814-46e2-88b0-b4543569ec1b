<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Receipt - {{ $receipt_number }}</title>
    <style>
        body {
            font-family: 'DejaVu Sans', sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
            line-height: 1.6;
        }
        
        .receipt-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .receipt-header {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .receipt-header h1 {
            margin: 0 0 10px 0;
            font-size: 28px;
            font-weight: bold;
        }
        
        .receipt-header .subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin: 0;
        }
        
        .receipt-number {
            background: rgba(255, 255, 255, 0.2);
            padding: 10px 20px;
            border-radius: 20px;
            display: inline-block;
            margin-top: 15px;
            font-weight: bold;
            font-size: 18px;
        }
        
        .receipt-body {
            padding: 30px;
        }
        
        .company-info {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .company-info h2 {
            color: #1e40af;
            margin: 0 0 10px 0;
            font-size: 24px;
        }
        
        .company-info p {
            margin: 5px 0;
            color: #6b7280;
        }
        
        .receipt-details {
            display: table;
            width: 100%;
            margin-bottom: 30px;
        }
        
        .detail-section {
            display: table-cell;
            width: 50%;
            vertical-align: top;
            padding: 0 15px;
        }
        
        .detail-section h3 {
            color: #1e40af;
            margin: 0 0 15px 0;
            font-size: 18px;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 5px;
        }
        
        .detail-item {
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
        }
        
        .detail-label {
            font-weight: bold;
            color: #374151;
        }
        
        .detail-value {
            color: #6b7280;
        }
        
        .payment-summary {
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin: 30px 0;
        }
        
        .payment-summary h3 {
            color: #1e40af;
            margin: 0 0 15px 0;
            text-align: center;
        }
        
        .amount-display {
            text-align: center;
            font-size: 32px;
            font-weight: bold;
            color: #059669;
            margin: 15px 0;
        }
        
        .payment-status {
            text-align: center;
            padding: 10px;
            border-radius: 20px;
            background: #d1fae5;
            color: #065f46;
            font-weight: bold;
            margin-top: 15px;
        }
        
        .receipt-footer {
            background: #f8fafc;
            padding: 20px 30px;
            border-top: 1px solid #e5e7eb;
            text-align: center;
            color: #6b7280;
            font-size: 14px;
        }
        
        .footer-note {
            margin: 10px 0;
        }
        
        .verification-code {
            font-family: 'Courier New', monospace;
            background: #e5e7eb;
            padding: 5px 10px;
            border-radius: 4px;
            display: inline-block;
            margin-top: 10px;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 0;
            }
            
            .receipt-container {
                border: none;
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="receipt-container">
        <!-- Receipt Header -->
        <div class="receipt-header">
            <h1>OFFICIAL RECEIPT</h1>
            <p class="subtitle">Minibus Owners Association of Malawi</p>
            <div class="receipt-number">{{ $receipt_number }}</div>
        </div>
        
        <!-- Receipt Body -->
        <div class="receipt-body">
            <!-- Company Information -->
            <div class="company-info">
                <h2>{{ $company_info['name'] }}</h2>
                <p>{{ $company_info['address'] }}</p>
                <p>Phone: {{ $company_info['phone'] }} | Email: {{ $company_info['email'] }}</p>
                <p>Website: {{ $company_info['website'] }}</p>
            </div>
            
            <!-- Receipt Details -->
            <div class="receipt-details">
                <div class="detail-section">
                    <h3>Payment Information</h3>
                    <div class="detail-item">
                        <span class="detail-label">Receipt Number:</span>
                        <span class="detail-value">{{ $payment_details['transaction_id'] }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Payment Date:</span>
                        <span class="detail-value">{{ $payment_details['payment_date'] }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Payment Method:</span>
                        <span class="detail-value">{{ $payment_details['payment_method'] }}</span>
                    </div>
                    @if($payment_details['order_reference'])
                    <div class="detail-item">
                        <span class="detail-label">Order Reference:</span>
                        <span class="detail-value">{{ $payment_details['order_reference'] }}</span>
                    </div>
                    @endif
                </div>
                
                <div class="detail-section">
                    <h3>Member Information</h3>
                    <div class="detail-item">
                        <span class="detail-label">Name:</span>
                        <span class="detail-value">{{ $member_details['name'] }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Email:</span>
                        <span class="detail-value">{{ $member_details['email'] }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Phone:</span>
                        <span class="detail-value">{{ $member_details['phone'] }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Membership Type:</span>
                        <span class="detail-value">{{ $member_details['membership_type'] }}</span>
                    </div>
                </div>
            </div>
            
            <!-- Payment Summary -->
            <div class="payment-summary">
                <h3>Payment Summary</h3>
                <div class="detail-item">
                    <span class="detail-label">Service:</span>
                    <span class="detail-value">{{ $payment_details['fee_type'] }} Fee</span>
                </div>
                <div class="amount-display">{{ $payment_details['amount'] }}</div>
                <div class="payment-status">✓ PAYMENT COMPLETED</div>
            </div>
        </div>
        
        <!-- Receipt Footer -->
        <div class="receipt-footer">
            <div class="footer-note">
                <strong>Thank you for your payment!</strong>
            </div>
            <div class="footer-note">
                This is an official receipt generated by the MOAMS system on {{ $generated_at->format('F j, Y \a\t g:i A') }}.
            </div>
            <div class="footer-note">
                For verification purposes, please quote receipt number: <span class="verification-code">{{ $receipt_number }}</span>
            </div>
            <div class="footer-note">
                If you have any questions about this receipt, please contact us at {{ $company_info['email'] }} or {{ $company_info['phone'] }}.
            </div>
        </div>
    </div>
</body>
</html>
