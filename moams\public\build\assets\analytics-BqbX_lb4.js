import{r as h,j as e,g as T,H as K,L as Q,e as S,B as k,h as X,f as B,C as g,a as b,b as v,c as j}from"./app-DL-qYY5V.js";import{T as E,a as F,b as C,c as p,d as P,e as f}from"./table-BKlRDaCi.js";import{A as Y,B as N}from"./app-layout-YqstQnqE.js";import{S as V,a as $,b as H,c as U,d as I}from"./select-Cp8NjZe8.js";import{C as Z}from"./chevron-left-DFeVEtK7.js";import{C as ee}from"./calendar-Bzuvt9Ns.js";import{C as se}from"./clock-4mJquAMZ.js";import{F as W}from"./file-text-BcxQqvSb.js";import{a as A}from"./users-DNGXY-sJ.js";import{T as G}from"./trending-up-CrkLNaAi.js";import{C as _}from"./chart-column-DHsDidJk.js";import"./index-1tPPImJd.js";import"./index-BTzg1GwG.js";import"./app-logo-icon-BnXlkpcX.js";import"./bus-CKTBKI0T.js";import"./dollar-sign-Cx0-nQIX.js";import"./clipboard-list-CxRumT8v.js";import"./index-uC6ZAdKJ.js";const R=h.createContext(),ae=({value:t,onValueChange:n,className:c,children:o,...x})=>e.jsx(R.Provider,{value:{value:t,onValueChange:n},children:e.jsx("div",{className:T("",c),...x,children:o})}),O=h.forwardRef(({className:t,children:n,...c},o)=>e.jsx("div",{ref:o,className:T("inline-flex h-9 items-center justify-center rounded-lg bg-gray-100 p-1 text-gray-500",t),...c,children:n}));O.displayName="TabsList";const L=h.forwardRef(({className:t,value:n,children:c,...o},x)=>{const l=h.useContext(R),d=(l==null?void 0:l.value)===n;return e.jsx("button",{ref:x,className:T("inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",d?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900",t),onClick:()=>{var i;return(i=l==null?void 0:l.onValueChange)==null?void 0:i.call(l,n)},...o,children:c})});L.displayName="TabsTrigger";const le=h.forwardRef(({className:t,value:n,children:c,...o},x)=>{const l=h.useContext(R);return(l==null?void 0:l.value)!==n?null:e.jsx("div",{ref:x,className:T("mt-2 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2",t),...o,children:c})});le.displayName="TabsContent";function ye({monthlyStats:t,trends:n,topOffenders:c,severityBreakdown:o,resolutionBreakdown:x,routeStats:l,selectedMonth:d,viewType:i="monthly"}){const[y,D]=h.useState(!1),M=async()=>{try{D(!0);const s=document.createElement("a"),a=new URLSearchParams({view:i,...i==="monthly"?{month:d}:{}});s.href=`/misconduct-analytics/report?${a.toString()}`,s.download=i==="monthly"?`misconduct-report-${d}.pdf`:"misconduct-report-alltime.pdf",document.body.appendChild(s),s.click(),document.body.removeChild(s)}catch(s){console.error("Error downloading PDF:",s)}finally{D(!1)}},q=[{title:"Dashboard",href:"/dashboard"},{title:"Misconduct Management",href:"/misconducts"},{title:"Analytics",href:"/misconduct-analytics"}],w=({title:s,value:a,icon:r,color:m="blue",subtitle:u})=>e.jsx(g,{children:e.jsx(j,{className:"p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:s}),e.jsx("p",{className:`text-2xl font-bold text-${m}-600`,children:a}),u&&e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:u})]}),e.jsx(r,{className:`h-8 w-8 text-${m}-600`})]})})}),z=s=>({low:"bg-green-100 text-green-800",medium:"bg-orange-100 text-orange-800",high:"bg-red-100 text-red-800"})[s==null?void 0:s.toLowerCase()]||"bg-gray-100 text-gray-800",J=s=>s>=80?"bg-green-100 text-green-800":s>=60?"bg-orange-100 text-orange-800":"bg-red-100 text-red-800";return e.jsxs(Y,{breadcrumbs:q,children:[e.jsx(K,{title:"Misconduct Analytics"}),e.jsx("div",{className:"w-full max-w-full overflow-hidden",children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex flex-col gap-4 mb-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center gap-4",children:[e.jsxs("div",{className:"flex-1 flex items-center gap-2",children:[e.jsxs(Q,{href:"/misconducts",className:"flex items-center px-3 py-1 rounded hover:bg-gray-100 border border-gray-200",children:[e.jsx(Z,{className:"h-4 w-4 mr-1"})," Back"]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(ae,{value:i,onValueChange:s=>S.get("/misconduct-analytics",{view:s,...s==="monthly"?{month:d}:{}}),children:e.jsxs(O,{className:"grid w-fit grid-cols-2",children:[e.jsxs(L,{value:"monthly",className:"flex items-center gap-2",children:[e.jsx(ee,{className:"h-4 w-4"}),"Monthly"]}),e.jsxs(L,{value:"alltime",className:"flex items-center gap-2",children:[e.jsx(se,{className:"h-4 w-4"}),"All-Time"]})]})}),i==="monthly"&&e.jsx("div",{className:"hidden sm:flex items-center gap-2",children:e.jsxs(V,{value:d,onValueChange:s=>S.get("/misconduct-analytics",{view:"monthly",month:s}),children:[e.jsx($,{className:"w-40",children:e.jsx(H,{})}),e.jsx(U,{children:Array.from({length:6},(s,a)=>{const r=new Date;r.setMonth(r.getMonth()-a);const m=r.toISOString().slice(0,7),u=r.toLocaleDateString("en-US",{year:"numeric",month:"long"});return e.jsx(I,{value:m,children:u},m)})})]})})]})]}),e.jsxs(k,{variant:"outline",onClick:M,disabled:y,className:"hidden sm:flex items-center gap-2 border-green-600 text-green-600 hover:bg-green-50 disabled:opacity-50",children:[e.jsx(W,{className:"h-4 w-4"}),y?"Generating PDF...":"Export PDF Report"]})]}),e.jsxs("div",{className:"flex sm:hidden items-center justify-between gap-4",children:[i==="monthly"&&e.jsxs(V,{value:d,onValueChange:s=>S.get("/misconduct-analytics",{view:"monthly",month:s}),children:[e.jsx($,{className:"w-40",children:e.jsx(H,{})}),e.jsx(U,{children:Array.from({length:6},(s,a)=>{const r=new Date;r.setMonth(r.getMonth()-a);const m=r.toISOString().slice(0,7),u=r.toLocaleDateString("en-US",{year:"numeric",month:"short"});return e.jsx(I,{value:m,children:u},m)})})]}),e.jsxs(k,{variant:"outline",onClick:M,disabled:y,className:"flex items-center gap-2 border-green-600 text-green-600 hover:bg-green-50 disabled:opacity-50",children:[e.jsx(W,{className:"h-4 w-4"}),e.jsx("span",{className:"hidden xs:inline",children:y?"Generating...":"Export"}),e.jsx("span",{className:"xs:hidden",children:"PDF"})]})]})]}),e.jsx("p",{className:"text-gray-600 mb-6",children:i==="monthly"?`Monthly insights and trends for ${d}`:"All-time insights and trends"}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8",children:[e.jsx(w,{title:"Total Misconducts",value:t.total_misconducts,icon:X,color:"red",subtitle:"This month"}),e.jsx(w,{title:"Affected Drivers",value:t.unique_drivers,icon:A,color:"blue",subtitle:"With misconducts"}),e.jsx(w,{title:"Resolution Rate",value:`${t.resolution_rate}%`,icon:B,color:"green",subtitle:"Cases resolved"}),e.jsx(w,{title:"Avg Trust Score",value:t.average_trust_score,icon:G,color:"purple",subtitle:"Affected drivers"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8",children:[e.jsxs(g,{className:"w-full",children:[e.jsx(b,{children:e.jsxs(v,{className:"flex items-center gap-2",children:[e.jsx(_,{className:"h-5 w-5"}),"Severity Breakdown"]})}),e.jsx(j,{children:e.jsx("div",{className:"space-y-4",children:o.map((s,a)=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"flex items-center gap-3",children:e.jsx(N,{className:z(s.severity),children:s.severity})}),e.jsx("span",{className:"font-semibold",children:s.count})]},a))})})]}),e.jsxs(g,{className:"w-full",children:[e.jsx(b,{children:e.jsxs(v,{className:"flex items-center gap-2",children:[e.jsx(B,{className:"h-5 w-5"}),"Resolution Status"]})}),e.jsx(j,{children:e.jsx("div",{className:"space-y-4",children:x.map((s,a)=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"flex items-center gap-3",children:e.jsx(N,{className:s.status==="Resolved"?"bg-green-100 text-green-800":"bg-orange-100 text-orange-800",children:s.status})}),e.jsx("span",{className:"font-semibold",children:s.count})]},a))})})]})]}),e.jsx("div",{className:"w-full border-2 border-blue-200 rounded-lg mt-8",style:{maxWidth:"calc(100vw - 2rem)",overflow:"hidden"},children:e.jsxs(g,{className:"w-full border-0",children:[e.jsxs(b,{children:[e.jsxs(v,{className:"flex items-center gap-2",children:[e.jsx(A,{className:"h-5 w-5"}),"Top 10 Offending Drivers"]}),e.jsx("p",{className:"text-sm text-gray-600",children:"Misconduct count for selected month • Trust score reflects all-time performance"})]}),e.jsx(j,{className:"p-0",children:c.length===0?e.jsxs("div",{className:"text-center py-8 px-6",children:[e.jsx(A,{className:"h-12 w-12 mx-auto text-gray-400 mb-4"}),e.jsx("p",{className:"text-gray-500",children:"No driver misconducts recorded this month."})]}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(E,{className:"w-full min-w-[600px]",children:[e.jsx(F,{children:e.jsxs(C,{children:[e.jsx(p,{className:"px-2 py-1",children:"Driver Name"}),e.jsx(p,{className:"px-2 py-1",children:"Trust Score"}),e.jsx(p,{className:"px-2 py-1",children:"Misconduct Count"}),e.jsx(p,{className:"px-2 py-1",children:"Minibus"})]})}),e.jsx(P,{children:c.map((s,a)=>e.jsxs(C,{children:[e.jsx(f,{className:"font-medium px-2 py-1",children:s.name}),e.jsx(f,{className:"px-2 py-1",children:e.jsxs(N,{className:J(s.trust_score),children:[s.trust_score,"%"]})}),e.jsx(f,{className:"px-2 py-1",children:e.jsx(N,{variant:"outline",children:s.misconduct_count})}),e.jsx(f,{className:"px-2 py-1",children:s.minibus})]},a))})]})})})]})}),e.jsx("div",{className:"w-full border-2 border-blue-200 rounded-lg mt-8",style:{maxWidth:"calc(100vw - 2rem)",overflow:"hidden"},children:e.jsxs(g,{className:"w-full border-0",children:[e.jsx(b,{children:e.jsxs(v,{className:"flex items-center gap-2",children:[e.jsx(_,{className:"h-5 w-5"}),"Misconducts by Route"]})}),e.jsx(j,{className:"p-0",children:l.length===0?e.jsxs("div",{className:"text-center py-8 px-6",children:[e.jsx(_,{className:"h-12 w-12 mx-auto text-gray-400 mb-4"}),e.jsx("p",{className:"text-gray-500",children:"No route-specific data available."})]}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(E,{className:"w-full min-w-[400px]",children:[e.jsx(F,{children:e.jsxs(C,{children:[e.jsx(p,{className:"px-2 py-1",children:"Route Name"}),e.jsx(p,{className:"px-2 py-1",children:"Misconduct Count"})]})}),e.jsx(P,{children:l.map((s,a)=>e.jsxs(C,{children:[e.jsx(f,{className:"font-medium px-2 py-1",children:s.route_name}),e.jsx(f,{className:"px-2 py-1",children:e.jsx(N,{variant:"outline",children:s.misconduct_count})})]},a))})]})})})]})}),e.jsxs(g,{className:"mt-8 w-full",children:[e.jsx(b,{children:e.jsxs(v,{className:"flex items-center gap-2",children:[e.jsx(G,{className:"h-5 w-5"}),"6-Month Trend"]})}),e.jsx(j,{children:e.jsx("div",{className:"space-y-4",children:n.map((s,a)=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsx("span",{className:"font-medium",children:s.month}),e.jsxs(N,{variant:"outline",children:[s.count," misconducts"]})]},a))})})]})]})})]})}export{ye as default};
