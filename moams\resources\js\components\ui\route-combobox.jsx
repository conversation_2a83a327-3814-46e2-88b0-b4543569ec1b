import * as React from "react";
import { Button } from "@/components/ui/button";
import {
    Command,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
    CommandList,
} from "@/components/ui/command";
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@/components/ui/popover";
import { ChevronsUpDown, Check, Loader2 } from "lucide-react";

function useDebounce(value, delay) {
    const [debounced, setDebounced] = React.useState(value);
    React.useEffect(() => {
        const handler = setTimeout(() => setDebounced(value), delay);
        return () => clearTimeout(handler);
    }, [value, delay]);
    return debounced;
}

export function RouteCombobox({
    value,
    onChange,
    placeholder = "Select route...",
    fetchRoutes, // async function: (search) => Promise<routes[]>
}) {
    const [open, setOpen] = React.useState(false);
    const [search, setSearch] = React.useState("");
    const [loading, setLoading] = React.useState(false);
    const [options, setOptions] = React.useState([]);
    const debouncedSearch = useDebounce(search, 300);

    React.useEffect(() => {
        if (open && search === "") {
            setLoading(true);
            fetchRoutes("").then(results => {
                const routesWithStringId = results.map(r => ({ ...r, id: String(r.id) }));
                const uniqueRoutes = [];
                const seenIds = new Set();
                for (const route of routesWithStringId) {
                    if (!seenIds.has(route.id)) {
                        uniqueRoutes.push(route);
                        seenIds.add(route.id);
                    }
                }
                setOptions(uniqueRoutes);
                setLoading(false);
            });
        }
    }, [open]);

    React.useEffect(() => {
        if (debouncedSearch !== "") {
            setLoading(true);
            fetchRoutes(debouncedSearch).then(results => {
                const routesWithStringId = results.map(r => ({ ...r, id: String(r.id) }));
                const uniqueRoutes = [];
                const seenIds = new Set();
                for (const route of routesWithStringId) {
                    if (!seenIds.has(route.id)) {
                        uniqueRoutes.push(route);
                        seenIds.add(route.id);
                    }
                }
                setOptions(uniqueRoutes);
                setLoading(false);
            });
        }
    }, [debouncedSearch, fetchRoutes]);

    return (
        <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
                <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={open}
                    className="w-full justify-between h-9 px-3 py-1 text-base rounded-md border border-input bg-transparent font-normal"
                >
                    {value
                        ? value.name || placeholder
                        : <span className="text-muted-foreground">{placeholder}</span>}
                    {open && loading ? (
                        <Loader2 className="ml-2 h-4 w-4 animate-spin opacity-50" />
                    ) : (
                        <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                    )}
                </Button>
            </PopoverTrigger>
            <PopoverContent className="w-full p-0">
                <Command>
                    <CommandInput
                        placeholder="Search route..."
                        value={search}
                        onValueChange={setSearch}
                    />
                    <CommandList>
                        <CommandEmpty>{loading ? "Searching..." : "Routes not found."}</CommandEmpty>
                        <CommandGroup>
                            {options.map((route) => (
                                <CommandItem
                                    key={route.id}
                                    value={route.name}
                                    onSelect={() => {
                                        onChange(route);
                                        setOpen(false);
                                    }}
                                >
                                    <Check
                                        className={
                                            value && value.id === route.id
                                                ? "mr-2 h-4 w-4 opacity-100"
                                                : "mr-2 h-4 w-4 opacity-0"
                                        }
                                    />
                                    {route.name}
                                </CommandItem>
                            ))}
                        </CommandGroup>
                    </CommandList>
                </Command>
            </PopoverContent>
        </Popover>
    );
} 