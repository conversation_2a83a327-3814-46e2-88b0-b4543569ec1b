import AppLayout from '@/layouts/app-layout';
import { Head, usePage } from '@inertiajs/react';
import OwnerDashboard from './dashboard/OwnerDashboard';
import ClerkDashboard from './dashboard/ClerkDashboard';
import ManagerDashboard from './dashboard/ManagerDashboard';
import AdminDashboard from './dashboard/AdminDashboard';

const breadcrumbs = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

export default function Dashboard(props) {
    const { auth, userRoles = [] } = usePage().props;
    // Use the full user object from props if available, otherwise fallback to auth.user
    const user = props.user || auth?.user;

    const {
        minibuses = [],
        drivers = [],
        unpaidMemberships = [],
        members = [],
        memberships = [],
        misconducts = [],
        clearanceRequests = [],
        transferRequests = [],
        users = [],
        stats = {},
        message,
    } = props;

    let dashboardContent = null;
    if (userRoles.includes('system admin')) {
        dashboardContent = <AdminDashboard user={user} users={users} stats={stats} />;
    } else if (userRoles.includes('association manager')) {
        dashboardContent = <ManagerDashboard user={user} stats={stats} misconducts={stats.misconducts?.all ?? 0} unpaidMemberships={stats.memberships?.unpaid ?? 0} currentFees={props.currentFees} />;
    } else if (userRoles.includes('association clerk')) {
        dashboardContent = <ClerkDashboard user={user} stats={stats} misconducts={stats.misconducts?.all ?? 0} clearanceRequests={stats.clearanceRequests?.pending ?? 0} transferRequests={stats.transferRequests?.pending ?? 0} currentFees={props.currentFees} />;
    } else if (userRoles.includes('minibus owner')) {
        dashboardContent = <OwnerDashboard user={user} minibuses={minibuses} drivers={drivers} unpaidMemberships={unpaidMemberships} stats={stats} currentFees={props.currentFees} />;
    } else {
        dashboardContent = (
            <div className="text-center p-8">
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Welcome</h1>
                <p className="text-gray-600">Your dashboard will appear here.</p>
            </div>
        );
    }

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard" />
            <div className="p-4">
                {message && (
                    <div className="mb-4 text-center text-red-600 font-semibold">{message}</div>
                )}
                {dashboardContent}
            </div>
        </AppLayout>
    );
}
