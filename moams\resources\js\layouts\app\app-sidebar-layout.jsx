import { AppContent } from '@/components/app-content';
import { AppShell } from '@/components/app-shell';
import { AppSidebar } from '@/components/app-sidebar';
import { AppSidebarHeader } from '@/components/app-sidebar-header';
import FlashMessage from '@/components/ui/flash-message';
import { usePage } from '@inertiajs/react';

export default function AppSidebarLayout({ children, breadcrumbs = [] }) {
    const { flash } = usePage().props;
    return (
        <AppShell variant="sidebar">
            <AppSidebar />
            <AppContent variant="sidebar">
                <AppSidebarHeader breadcrumbs={breadcrumbs} />
                {/* Flash messages */}
                {flash?.success && <FlashMessage message={flash.success} type="success" />}
                {flash?.error && <FlashMessage message={flash.error} type="error" />}
                {children}
            </AppContent>
        </AppShell>
    );
}
