import{i as E,r as n,j as e,H,B as l,C as O,a as V,b as z,c as q,h as je,e as o,T as R}from"./app-DL-qYY5V.js";import{n as pe}from"./navigation-DAA2N51J.js";import{I as U}from"./input-Dm4SEXxy.js";import{L as j}from"./label-e3QxUH-L.js";import{T as ge,a as fe,b as W,c as w,d as ve,e as y}from"./table-BKlRDaCi.js";import{A as Z,B as be,T as G,b as J,c as K,d as Q,C as Ne}from"./app-layout-YqstQnqE.js";import{S as A,a as F,b as L,c as M,d as r,C as P}from"./select-Cp8NjZe8.js";import{D as X,a as Y,b as I,c as ee,d as se,e as ae}from"./dialog-CXtul0Wy.js";import{C as te}from"./chevron-left-DFeVEtK7.js";import{P as we}from"./plus-CU6rIcI2.js";import{D as ye}from"./download-fjRG0KHX.js";import{F as Ce}from"./filter-DPFCjsfq.js";import{S as Se}from"./search-B4sum6Qx.js";import"./index-BTzg1GwG.js";import"./index-1tPPImJd.js";import"./app-logo-icon-BnXlkpcX.js";import"./bus-CKTBKI0T.js";import"./dollar-sign-Cx0-nQIX.js";import"./users-DNGXY-sJ.js";import"./clipboard-list-CxRumT8v.js";import"./index-uC6ZAdKJ.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ke=[["path",{d:"M18.36 6.64A9 9 0 0 1 20.77 15",key:"dxknvb"}],["path",{d:"M6.16 6.16a9 9 0 1 0 12.68 12.68",key:"1x7qb5"}],["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]],De=E("PowerOff",ke);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _e=[["path",{d:"M12 2v10",key:"mnfbl"}],["path",{d:"M18.4 6.6a9 9 0 1 1-12.77.04",key:"obofu9"}]],Te=E("Power",_e);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Re=[["circle",{cx:"6",cy:"19",r:"3",key:"1kj8tv"}],["path",{d:"M9 19h8.5a3.5 3.5 0 0 0 0-7h-11a3.5 3.5 0 0 1 0-7H15",key:"1d8sl"}],["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}]],re=E("Route",Re);function Ye({routes:i=[],auth:C={}}){const[p,ne]=n.useState(""),[g,le]=n.useState("all"),[d,ie]=n.useState("name"),[m,ce]=n.useState("asc"),[oe,f]=n.useState(!1),[u,v]=n.useState({open:!1,route:null,action:null}),[x,b]=n.useState(!1),[S,$]=n.useState(!1),[h,k]=n.useState({name:"",status:"active"}),[D,B]=n.useState({}),_=(i==null?void 0:i.data)||i||[],a=i!=null&&i.data?i:null,T=[{title:"Dashboard",href:"/dashboard"},{title:"Route Management",href:"/routes"}],de=s=>{ne(s),o.get("/routes",{search:s,status:g,sort:d,direction:m},{preserveState:!0,preserveScroll:!0})},me=s=>{le(s),o.get("/routes",{search:p,status:s,sort:d,direction:m},{preserveState:!0,preserveScroll:!0})},N=s=>{const t=d===s&&m==="asc"?"desc":"asc";ie(s),ce(t),o.get("/routes",{search:p,status:g,sort:s,direction:t},{preserveState:!0,preserveScroll:!0})},ue=s=>{const c=(s.status==="active"?"inactive":"active")==="active"?"activate":"deactivate";v({open:!0,route:s,action:c,title:`${c.charAt(0).toUpperCase()+c.slice(1)} Route`,description:c==="deactivate"&&s.minibuses_count>0?`Warning: This route is assigned to ${s.minibuses_count} active minibus(es). Deactivating it may affect operations.`:`Are you sure you want to ${c} this route?`})},xe=async()=>{b(!0);const{route:s,action:t}=u;try{switch(t){case"activate":await o.post(R("routes.activate",s.id));break;case"deactivate":await o.post(R("routes.deactivate",s.id));break}v({open:!1,route:null,action:null})}catch(c){console.error(`Error ${t}ing route:`,c)}finally{b(!1)}},he=async()=>{b(!0),B({});try{await o.post(R("routes.store"),h,{onError:s=>{B(s)},onSuccess:()=>{f(!1),k({name:"",status:"active"})}})}catch(s){console.error("Error creating route:",s)}finally{b(!1)}};try{return e.jsxs(Z,{breadcrumbs:T,children:[e.jsx(H,{title:"Route Management"}),e.jsx("div",{className:"w-full max-w-full overflow-hidden",children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex flex-col gap-4 mb-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4",children:[e.jsxs("button",{className:"flex items-center px-3 py-1 rounded hover:bg-gray-100 border border-gray-200 w-fit",onClick:()=>pe(T),children:[e.jsx(te,{className:"h-4 w-4 mr-1"})," Back"]}),e.jsx("div",{className:"flex-1 sm:flex-none",children:e.jsx("span",{className:"text-muted-foreground text-sm sm:text-base font-medium break-words",children:"Manage minibus routes and monitor usage patterns."})})]}),e.jsxs("div",{className:"flex flex-wrap items-center gap-2 justify-start sm:justify-end",children:[C.user&&C.user.roles&&C.user.roles.includes("association clerk")&&e.jsxs(l,{onClick:()=>f(!0),className:"w-fit flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2",children:[e.jsx(we,{className:"h-4 w-4"}),"Add New Route"]}),e.jsxs(l,{variant:"outline",onClick:async()=>{try{$(!0),window.open("/routes/export?format=pdf","_blank")}catch(s){console.error("Error downloading PDF:",s)}finally{setTimeout(()=>$(!1),2e3)}},disabled:S,className:"w-fit border-green-500 text-green-700 hover:bg-green-50 px-4 py-2 disabled:opacity-50",children:[e.jsx(ye,{className:"h-4 w-4 mr-2"}),e.jsx("span",{className:"hidden sm:inline",children:S?"Downloading...":"Export Routes Analysis PDF"}),e.jsx("span",{className:"sm:hidden",children:S?"Downloading...":"Export PDF"})]})]})]}),e.jsxs(O,{className:"mb-6 w-full",children:[e.jsx(V,{children:e.jsxs(z,{className:"flex items-center gap-2",children:[e.jsx(Ce,{className:"h-5 w-5"}),"Search & Filter"]})}),e.jsx(q,{children:e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx(j,{htmlFor:"search",children:"Search Routes"}),e.jsxs("div",{className:"relative",children:[e.jsx(Se,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),e.jsx(U,{id:"search",placeholder:"Search by route name...",value:p,onChange:s=>de(s.target.value),className:"pl-10"})]})]}),e.jsxs("div",{children:[e.jsx(j,{htmlFor:"status-filter",children:"Filter by Status"}),e.jsxs(A,{value:g,onValueChange:me,children:[e.jsx(F,{children:e.jsx(L,{placeholder:"All statuses"})}),e.jsxs(M,{children:[e.jsx(r,{value:"all",children:"All Statuses"}),e.jsx(r,{value:"active",children:"Active"}),e.jsx(r,{value:"inactive",children:"Inactive"})]})]})]}),e.jsxs("div",{children:[e.jsx(j,{htmlFor:"sort-by",children:"Sort By"}),e.jsxs(A,{value:`${d}-${m}`,onValueChange:s=>{const[t,c]=s.split("-");N(t)},children:[e.jsx(F,{children:e.jsx(L,{})}),e.jsxs(M,{children:[e.jsx(r,{value:"name-asc",children:"Name (A-Z)"}),e.jsx(r,{value:"name-desc",children:"Name (Z-A)"}),e.jsx(r,{value:"minibuses_count-desc",children:"Most Popular"}),e.jsx(r,{value:"minibuses_count-asc",children:"Least Popular"}),e.jsx(r,{value:"status-asc",children:"Status (Active First)"}),e.jsx(r,{value:"status-desc",children:"Status (Inactive First)"})]})]})]})]})})]}),e.jsx("div",{className:"w-full border-2 border-blue-200 rounded-lg",style:{maxWidth:"calc(100vw - 2rem)",overflow:"hidden"},children:e.jsxs(O,{className:"w-full border-0",children:[e.jsx(V,{children:e.jsxs(z,{className:"flex items-center gap-2",children:[e.jsx(re,{className:"h-5 w-5"}),"Routes (",(a==null?void 0:a.total)||_.length,")"]})}),e.jsx(q,{className:"p-0",children:_.length===0?e.jsxs("div",{className:"text-center py-8 px-6",children:[e.jsx(re,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),e.jsx("h3",{className:"text-lg font-medium mb-2",children:"No routes found"}),e.jsx("p",{className:"text-muted-foreground mb-4",children:p||g!=="all"?"Try adjusting your search or filter criteria.":"No routes available."})]}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs(ge,{className:"w-full min-w-[600px]",children:[e.jsx(fe,{children:e.jsxs(W,{children:[e.jsx(w,{className:"cursor-pointer hover:bg-muted/50",onClick:()=>N("name"),children:e.jsxs("div",{className:"flex items-center gap-2",children:["Route Name",d==="name"&&e.jsx(P,{className:`h-4 w-4 transition-transform ${m==="desc"?"rotate-180":""}`})]})}),e.jsx(w,{className:"cursor-pointer hover:bg-muted/50",onClick:()=>N("status"),children:e.jsxs("div",{className:"flex items-center gap-2",children:["Status",d==="status"&&e.jsx(P,{className:`h-4 w-4 transition-transform ${m==="desc"?"rotate-180":""}`})]})}),e.jsx(w,{className:"cursor-pointer hover:bg-muted/50",onClick:()=>N("minibuses_count"),children:e.jsxs("div",{className:"flex items-center gap-2",children:["Usage Count",d==="minibuses_count"&&e.jsx(P,{className:`h-4 w-4 transition-transform ${m==="desc"?"rotate-180":""}`})]})}),e.jsx(w,{children:"Actions"})]})}),e.jsx(ve,{children:_.map(s=>e.jsxs(W,{children:[e.jsx(y,{className:"font-medium",children:s.name}),e.jsx(y,{children:e.jsx(be,{variant:s.status==="active"?"default":"secondary",className:s.status==="active"?"bg-green-100 text-green-800":"",children:s.status==="active"?"Active":"Inactive"})}),e.jsx(y,{children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"font-medium",children:s.minibuses_count||0}),e.jsxs("span",{className:"text-muted-foreground text-sm",children:["minibus",(s.minibuses_count||0)!==1?"es":""]}),(s.minibuses_count||0)>0&&e.jsx(G,{children:e.jsxs(J,{children:[e.jsx(K,{children:e.jsx(je,{className:"h-4 w-4 text-amber-500"})}),e.jsx(Q,{children:"This route is assigned to active minibuses"})]})})]})}),e.jsx(y,{children:e.jsx(G,{children:e.jsxs(J,{children:[e.jsx(K,{asChild:!0,children:e.jsx(l,{variant:"outline",size:"sm",onClick:()=>ue(s),style:s.status==="active"?{borderColor:"#ea580c",color:"#ea580c",backgroundColor:"transparent"}:{borderColor:"#16a34a",color:"#16a34a",backgroundColor:"transparent"},onMouseEnter:t=>{s.status==="active"?t.target.style.backgroundColor="#fff7ed":t.target.style.backgroundColor="#f0fdf4"},onMouseLeave:t=>{t.target.style.backgroundColor="transparent"},children:s.status==="active"?e.jsx(De,{className:"h-4 w-4"}):e.jsx(Te,{className:"h-4 w-4"})})}),e.jsxs(Q,{children:[s.status==="active"?"Deactivate":"Activate"," route"]})]})})})]},s.id))})]})})})]})}),a&&a.total>0&&e.jsx("div",{className:"mt-6 w-full",children:e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 px-4 py-4 border-t bg-gray-50/50 rounded-b-lg",children:[e.jsxs("div",{className:"text-sm text-gray-700 text-center sm:text-left",children:["Showing ",a.from," to ",a.to," of ",a.total," results"]}),a.last_page>1&&e.jsxs("div",{className:"flex items-center justify-center sm:justify-end space-x-2",children:[e.jsxs(l,{variant:"outline",size:"sm",onClick:()=>o.get(a.prev_page_url),disabled:!a.prev_page_url,className:"flex items-center gap-1",children:[e.jsx(te,{className:"h-4 w-4"}),e.jsx("span",{className:"hidden sm:inline",children:"Previous"}),e.jsx("span",{className:"sm:hidden",children:"Prev"})]}),e.jsxs("span",{className:"text-sm text-gray-600 px-2",children:["Page ",a.current_page," of ",a.last_page]}),e.jsxs(l,{variant:"outline",size:"sm",onClick:()=>o.get(a.next_page_url),disabled:!a.next_page_url,className:"flex items-center gap-1",children:[e.jsx("span",{className:"hidden sm:inline",children:"Next"}),e.jsx("span",{className:"sm:hidden",children:"Next"}),e.jsx(Ne,{className:"h-4 w-4"})]})]})]})}),e.jsx(X,{open:oe,onOpenChange:f,children:e.jsxs(Y,{children:[e.jsxs(I,{children:[e.jsx(ee,{children:"Add New Route"}),e.jsx(se,{children:'Create a new minibus route. Route names must follow the pattern "Location - Location" (e.g., "Ntcheu - Chingeni").'})]}),e.jsxs("form",{onSubmit:s=>{s.preventDefault(),he()},children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(j,{htmlFor:"route-name",children:"Route Name"}),e.jsx(U,{id:"route-name",placeholder:"e.g., Ntcheu - Chingeni",value:h.name,onChange:s=>k({...h,name:s.target.value}),className:D.name?"border-red-500":""}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:'Format: "Location - Location" with exactly one space before and after the dash'}),D.name&&e.jsx("p",{className:"text-sm text-red-500 mt-1",children:D.name})]}),e.jsxs("div",{children:[e.jsx(j,{htmlFor:"route-status",children:"Status"}),e.jsxs(A,{value:h.status,onValueChange:s=>k({...h,status:s}),children:[e.jsx(F,{children:e.jsx(L,{})}),e.jsxs(M,{children:[e.jsx(r,{value:"active",children:"Active"}),e.jsx(r,{value:"inactive",children:"Inactive"})]})]})]})]}),e.jsxs(ae,{className:"mt-6",children:[e.jsx(l,{type:"button",variant:"outline",onClick:()=>f(!1),children:"Cancel"}),e.jsx(l,{type:"submit",disabled:x,className:"bg-blue-600 hover:bg-blue-700 text-white",children:x?"Creating...":"Create Route"})]})]})]})}),e.jsx(X,{open:u.open,onOpenChange:s=>{s||v({open:!1,route:null,action:null})},children:e.jsxs(Y,{showCloseButton:!1,children:[e.jsxs(I,{children:[e.jsx(ee,{children:u.title}),e.jsx(se,{children:u.description})]}),e.jsxs(ae,{children:[e.jsx(l,{type:"button",variant:"outline",onClick:()=>v({open:!1,route:null,action:null}),disabled:x,children:"Cancel"}),e.jsx(l,{type:"button",onClick:xe,disabled:x,children:x?"Loading...":u.action==="activate"?"Activate":"Deactivate"})]})]})})]})})]})}catch(s){return console.error("RouteManagement render error:",s),e.jsxs(Z,{breadcrumbs:T,children:[e.jsx(H,{title:"Route Management"}),e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"text-2xl font-bold text-red-600 mb-4",children:"Error Loading Routes"}),e.jsxs("p",{className:"text-gray-600 mb-4",children:["Error: ",s.message]}),e.jsx(l,{onClick:()=>window.location.reload(),children:"Reload Page"})]})})]})}}export{Ye as default};
