<?php

namespace App\Exceptions;

use Illuminate\Auth\AuthenticationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\QueryException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\Exceptions\ThrottleRequestsException;
use Illuminate\Session\TokenMismatchException;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\ServiceUnavailableHttpException;
use App\Exceptions\PaymentException;
use App\Exceptions\FileUploadException;
use App\Exceptions\BusinessLogicException;

use Throwable;
use Inertia\Inertia;
use Symfony\Component\HttpFoundation\Response as SymfonyResponse;
use Illuminate\Support\Facades\App;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array<int, class-string<Throwable>>
     */
    protected $dontReport = [
        ValidationException::class,
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
        'token',
        'api_key',
        'secret',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        //
    }

    /**
     * Handle unauthenticated users.
     */
    protected function unauthenticated($request, AuthenticationException $exception)
    {
        return $request->expectsJson()
            ? response()->json(['message' => 'Authentication required'], 401)
            : redirect()->guest(route('login'));
    }

    /**
     * Render an exception into an HTTP response.
     */
    public function render($request, Throwable $exception): SymfonyResponse
    {
        // Handle specific exception types with user-friendly messages
        $errorData = $this->getErrorData($exception, $request);

        // If the request expects Inertia (AJAX navigation)
        if ($request->hasHeader('X-Inertia')) {
            return Inertia::render('error', $errorData)
                ->toResponse($request)
                ->setStatusCode($errorData['status']);
        }

        // For API requests, return JSON
        if ($request->expectsJson()) {
            return response()->json([
                'error' => $errorData['title'],
                'message' => $errorData['description'],
                'status' => $errorData['status'],
            ], $errorData['status']);
        }

        // Default to parent behavior for non-Inertia requests
        return parent::render($request, $exception);
    }

    /**
     * Get user-friendly error data based on exception type
     */
    private function getErrorData(Throwable $exception, $request): array
    {
        $status = method_exists($exception, 'getStatusCode') ? $exception->getStatusCode() : 500;
        $title = 'Something went wrong';
        $description = 'An unexpected error occurred. Please try again later or contact support if the problem persists.';
        $errorType = 'general';
        $recoverable = false;
        $retryable = false;

        // Handle specific exception types
        switch (true) {
            case $exception instanceof PaymentException:
                $status = 422;
                $title = 'Payment Error';
                $description = $exception->getUserMessage();
                $errorType = 'payment';
                $recoverable = true;
                $retryable = true;
                break;

            case $exception instanceof FileUploadException:
                $status = 422;
                $title = 'File Upload Error';
                $description = $exception->getUserMessage();
                $errorType = 'file_upload';
                $recoverable = true;
                $retryable = true;
                break;

            case $exception instanceof BusinessLogicException:
                $status = 422;
                $title = 'Business Rule Violation';
                $description = $exception->getUserMessage();
                $errorType = 'business_logic';
                $recoverable = true;
                break;

            case $exception instanceof NotFoundHttpException:
                $status = 404;
                $title = 'Page Not Found';
                $description = 'The page you are looking for does not exist.';
                $errorType = 'not_found';

                // Check for payment redirect issues
                $url = $request->fullUrl();
                $hasPaymentParams = $request->has('tx_ref') || $request->has('status') ||
                    str_contains($url, 'paychangu') || str_contains($url, 'ctechpay') ||
                    str_contains($url, '/payments/');

                if ($hasPaymentParams) {
                    $title = 'Payment Redirect Issue';
                    $description = 'It looks like you\'re being redirected from a payment page. The system will help you get to the right place.';
                    $errorType = 'payment_redirect';
                    $recoverable = true;
                }
                break;

            case $exception instanceof AccessDeniedHttpException:
                $status = 403;
                $title = 'Access Denied';
                $description = 'You do not have permission to access this resource.';
                $errorType = 'forbidden';
                break;

            case $exception instanceof AuthenticationException:
                $status = 401;
                $title = 'Authentication Required';
                $description = 'You must be logged in to access this page.';
                $errorType = 'unauthorized';
                $recoverable = true;
                break;

            case $exception instanceof ValidationException:
                $status = 422;
                $title = 'Validation Error';
                $description = 'Please check your input and try again.';
                $errorType = 'validation';
                $recoverable = true;
                break;

            case $exception instanceof ModelNotFoundException:
                $status = 404;
                $title = 'Resource Not Found';
                $description = 'The requested resource could not be found.';
                $errorType = 'model_not_found';
                break;

            case $exception instanceof QueryException:
                $status = 500;
                $title = 'Database Error';
                $description = 'A database error occurred. Please try again later.';
                $errorType = 'database';
                $retryable = true;

                // Don't expose database details in production
                if (App::environment('production')) {
                    $description = 'A temporary issue occurred. Please try again in a few moments.';
                }
                break;

            case $exception instanceof TokenMismatchException:
                $status = 419;
                $title = 'Session Expired';
                $description = 'Your session has expired. Please refresh the page and try again.';
                $errorType = 'csrf';
                $recoverable = true;
                $retryable = true;
                break;

            case $exception instanceof ThrottleRequestsException:
                $status = 429;
                $title = 'Too Many Requests';
                $description = 'You are making too many requests. Please wait a moment and try again.';
                $errorType = 'throttle';
                $retryable = true;
                break;

            case $exception instanceof ServiceUnavailableHttpException:
                $status = 503;
                $title = 'Service Unavailable';
                $description = 'The service is temporarily unavailable. Please try again later.';
                $errorType = 'service_unavailable';
                $retryable = true;
                break;

            default:
                // Handle file upload errors
                if (
                    str_contains($exception->getMessage(), 'upload') ||
                    str_contains($exception->getMessage(), 'file') ||
                    str_contains($exception->getMessage(), 'storage')
                ) {
                    $title = 'File Upload Error';
                    $description = 'There was an issue uploading your file. Please check the file size and format, then try again.';
                    $errorType = 'file_upload';
                    $recoverable = true;
                    $retryable = true;
                }

                // Handle payment-related errors
                elseif (
                    str_contains($exception->getMessage(), 'payment') ||
                    str_contains($exception->getMessage(), 'ctechpay') ||
                    str_contains($exception->getMessage(), 'paychangu')
                ) {
                    $title = 'Payment Processing Error';
                    $description = 'There was an issue processing your payment. Please try again or contact support.';
                    $errorType = 'payment';
                    $recoverable = true;
                    $retryable = true;
                }

                // Handle network/connection errors
                elseif (
                    str_contains($exception->getMessage(), 'connection') ||
                    str_contains($exception->getMessage(), 'timeout') ||
                    str_contains($exception->getMessage(), 'network')
                ) {
                    $title = 'Connection Error';
                    $description = 'A network error occurred. Please check your connection and try again.';
                    $errorType = 'network';
                    $retryable = true;
                }
                break;
        }

        // Generate error ID for tracking
        $errorId = $request->attributes->get('error_id') ?? ErrorMonitoringService::logError($exception, [
            'error_type' => $errorType,
            'user_friendly_title' => $title,
            'user_friendly_description' => $description,
        ]);

        return [
            'status' => $status,
            'title' => $title,
            'description' => $description,
            'error_type' => $errorType,
            'recoverable' => $recoverable,
            'retryable' => $retryable,
            'error_id' => $errorId,
            'timestamp' => now()->toISOString(),
        ];
    }
}