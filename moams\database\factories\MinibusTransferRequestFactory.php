<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\MinibusTransferRequest>
 */
class MinibusTransferRequestFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'minibus_id' => null, // to be set in seeder
            'owner_id' => null,   // to be set in seeder
            'status' => $this->faker->randomElement(['pending', 'transferred']),
            'transfer_type' => $this->faker->randomElement(['internal', 'external']),
            'reason' => $this->faker->sentence(8),
            'ownership_transfer_certificate' => 'ownership_transfer_certificates/' . $this->faker->uuid . '.pdf',
        ];
    }
}
