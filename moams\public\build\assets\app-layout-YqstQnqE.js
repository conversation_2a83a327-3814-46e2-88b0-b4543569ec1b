import{i as V,r as l,w as ja,q as W,j as s,p as lt,g as P,y as Ta,S as vt,z as mr,B as Be,d as xe,L as fe,l as ve,v as hr,D as Da,t as Oa,h as Ia,e as ee}from"./app-DL-qYY5V.js";import{d as be,a as _,u as xt,c as he,P as ue,e as ka,b as La,C as Fa}from"./index-1tPPImJd.js";import{P as I,d as gr,R as $a,r as Ba}from"./index-BTzg1GwG.js";import{A as Wa}from"./app-logo-icon-BnXlkpcX.js";import{B as Ha}from"./bus-CKTBKI0T.js";import{D as vr}from"./dollar-sign-Cx0-nQIX.js";import{a as Ua,U as Ga}from"./users-DNGXY-sJ.js";import{C as Ka}from"./clipboard-list-CxRumT8v.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const za=[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]],Dt=V("Bell",za);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Va=[["path",{d:"M15 13a3 3 0 1 0-6 0",key:"10j68g"}],["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20",key:"k3hazp"}],["circle",{cx:"12",cy:"8",r:"2",key:"1822b1"}]],Bn=V("BookUser",Va);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qa=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],Ya=V("ChevronRight",qa);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xa=[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]],Za=V("ChevronsUpDown",Xa);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qa=[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]],Ja=V("CreditCard",Qa);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const es=[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]],ts=V("Ellipsis",es);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ns=[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]],rs=V("Key",ns);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const os=[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]],as=V("LogOut",os);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ss=[["path",{d:"M14.106 5.553a2 2 0 0 0 1.788 0l3.659-1.83A1 1 0 0 1 21 4.619v12.764a1 1 0 0 1-.553.894l-4.553 2.277a2 2 0 0 1-1.788 0l-4.212-2.106a2 2 0 0 0-1.788 0l-3.659 1.83A1 1 0 0 1 3 19.381V6.618a1 1 0 0 1 .553-.894l4.553-2.277a2 2 0 0 1 1.788 0z",key:"169xi5"}],["path",{d:"M15 5.764v15",key:"1pn4in"}],["path",{d:"M9 3.236v15",key:"1uimfh"}]],is=V("Map",ss);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cs=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]],ls=V("PanelLeft",cs);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const us=[["path",{d:"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2",key:"wrbu53"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["path",{d:"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14",key:"lysw3i"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}]],ds=V("Truck",us);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fs=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]],ps=V("UserCheck",fs);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ms=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],xr=V("X",ms),Ot=768;function wr(){const[e,t]=l.useState();return l.useEffect(()=>{const n=window.matchMedia(`(max-width: ${Ot-1}px)`),r=()=>{t(window.innerWidth<Ot)};return n.addEventListener("change",r),t(window.innerWidth<Ot),()=>n.removeEventListener("change",r)},[]),!!e}var hs=ja[" useId ".trim().toString()]||(()=>{}),gs=0;function we(e){const[t,n]=l.useState(hs());return be(()=>{n(r=>r??String(gs++))},[e]),t?`radix-${t}`:""}function oe(e){const t=l.useRef(e);return l.useEffect(()=>{t.current=e}),l.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function vs(e,t=globalThis==null?void 0:globalThis.document){const n=oe(e);l.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var xs="DismissableLayer",Yt="dismissableLayer.update",ws="dismissableLayer.pointerDownOutside",bs="dismissableLayer.focusOutside",Wn,br=l.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),wt=l.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:a,onInteractOutside:i,onDismiss:c,...f}=e,p=l.useContext(br),[d,u]=l.useState(null),m=(d==null?void 0:d.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,g]=l.useState({}),x=W(t,R=>u(R)),h=Array.from(p.layers),[v]=[...p.layersWithOutsidePointerEventsDisabled].slice(-1),b=h.indexOf(v),y=d?h.indexOf(d):-1,E=p.layersWithOutsidePointerEventsDisabled.size>0,w=y>=b,C=Es(R=>{const A=R.target,D=[...p.branches].some(j=>j.contains(A));!w||D||(o==null||o(R),i==null||i(R),R.defaultPrevented||c==null||c())},m),S=Ss(R=>{const A=R.target;[...p.branches].some(j=>j.contains(A))||(a==null||a(R),i==null||i(R),R.defaultPrevented||c==null||c())},m);return vs(R=>{y===p.layers.size-1&&(r==null||r(R),!R.defaultPrevented&&c&&(R.preventDefault(),c()))},m),l.useEffect(()=>{if(d)return n&&(p.layersWithOutsidePointerEventsDisabled.size===0&&(Wn=m.body.style.pointerEvents,m.body.style.pointerEvents="none"),p.layersWithOutsidePointerEventsDisabled.add(d)),p.layers.add(d),Hn(),()=>{n&&p.layersWithOutsidePointerEventsDisabled.size===1&&(m.body.style.pointerEvents=Wn)}},[d,m,n,p]),l.useEffect(()=>()=>{d&&(p.layers.delete(d),p.layersWithOutsidePointerEventsDisabled.delete(d),Hn())},[d,p]),l.useEffect(()=>{const R=()=>g({});return document.addEventListener(Yt,R),()=>document.removeEventListener(Yt,R)},[]),s.jsx(I.div,{...f,ref:x,style:{pointerEvents:E?w?"auto":"none":void 0,...e.style},onFocusCapture:_(e.onFocusCapture,S.onFocusCapture),onBlurCapture:_(e.onBlurCapture,S.onBlurCapture),onPointerDownCapture:_(e.onPointerDownCapture,C.onPointerDownCapture)})});wt.displayName=xs;var ys="DismissableLayerBranch",Cs=l.forwardRef((e,t)=>{const n=l.useContext(br),r=l.useRef(null),o=W(t,r);return l.useEffect(()=>{const a=r.current;if(a)return n.branches.add(a),()=>{n.branches.delete(a)}},[n.branches]),s.jsx(I.div,{...e,ref:o})});Cs.displayName=ys;function Es(e,t=globalThis==null?void 0:globalThis.document){const n=oe(e),r=l.useRef(!1),o=l.useRef(()=>{});return l.useEffect(()=>{const a=c=>{if(c.target&&!r.current){let f=function(){yr(ws,n,p,{discrete:!0})};const p={originalEvent:c};c.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=f,t.addEventListener("click",o.current,{once:!0})):f()}else t.removeEventListener("click",o.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",a)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",a),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function Ss(e,t=globalThis==null?void 0:globalThis.document){const n=oe(e),r=l.useRef(!1);return l.useEffect(()=>{const o=a=>{a.target&&!r.current&&yr(bs,n,{originalEvent:a},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function Hn(){const e=new CustomEvent(Yt);document.dispatchEvent(e)}function yr(e,t,n,{discrete:r}){const o=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?gr(o,a):o.dispatchEvent(a)}var It="focusScope.autoFocusOnMount",kt="focusScope.autoFocusOnUnmount",Un={bubbles:!1,cancelable:!0},Rs="FocusScope",sn=l.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:a,...i}=e,[c,f]=l.useState(null),p=oe(o),d=oe(a),u=l.useRef(null),m=W(t,h=>f(h)),g=l.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;l.useEffect(()=>{if(r){let h=function(E){if(g.paused||!c)return;const w=E.target;c.contains(w)?u.current=w:de(u.current,{select:!0})},v=function(E){if(g.paused||!c)return;const w=E.relatedTarget;w!==null&&(c.contains(w)||de(u.current,{select:!0}))},b=function(E){if(document.activeElement===document.body)for(const C of E)C.removedNodes.length>0&&de(c)};document.addEventListener("focusin",h),document.addEventListener("focusout",v);const y=new MutationObserver(b);return c&&y.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",h),document.removeEventListener("focusout",v),y.disconnect()}}},[r,c,g.paused]),l.useEffect(()=>{if(c){Kn.add(g);const h=document.activeElement;if(!c.contains(h)){const b=new CustomEvent(It,Un);c.addEventListener(It,p),c.dispatchEvent(b),b.defaultPrevented||(Ms(js(Cr(c)),{select:!0}),document.activeElement===h&&de(c))}return()=>{c.removeEventListener(It,p),setTimeout(()=>{const b=new CustomEvent(kt,Un);c.addEventListener(kt,d),c.dispatchEvent(b),b.defaultPrevented||de(h??document.body,{select:!0}),c.removeEventListener(kt,d),Kn.remove(g)},0)}}},[c,p,d,g]);const x=l.useCallback(h=>{if(!n&&!r||g.paused)return;const v=h.key==="Tab"&&!h.altKey&&!h.ctrlKey&&!h.metaKey,b=document.activeElement;if(v&&b){const y=h.currentTarget,[E,w]=As(y);E&&w?!h.shiftKey&&b===w?(h.preventDefault(),n&&de(E,{select:!0})):h.shiftKey&&b===E&&(h.preventDefault(),n&&de(w,{select:!0})):b===y&&h.preventDefault()}},[n,r,g.paused]);return s.jsx(I.div,{tabIndex:-1,...i,ref:m,onKeyDown:x})});sn.displayName=Rs;function Ms(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(de(r,{select:t}),document.activeElement!==n)return}function As(e){const t=Cr(e),n=Gn(t,e),r=Gn(t.reverse(),e);return[n,r]}function Cr(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Gn(e,t){for(const n of e)if(!_s(n,{upTo:t}))return n}function _s(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function Ns(e){return e instanceof HTMLInputElement&&"select"in e}function de(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&Ns(e)&&t&&e.select()}}var Kn=Ps();function Ps(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=zn(e,t),e.unshift(t)},remove(t){var n;e=zn(e,t),(n=e[0])==null||n.resume()}}}function zn(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function js(e){return e.filter(t=>t.tagName!=="A")}var Ts="Portal",bt=l.forwardRef((e,t)=>{var c;const{container:n,...r}=e,[o,a]=l.useState(!1);be(()=>a(!0),[]);const i=n||o&&((c=globalThis==null?void 0:globalThis.document)==null?void 0:c.body);return i?$a.createPortal(s.jsx(I.div,{...r,ref:t}),i):null});bt.displayName=Ts;var Lt=0;function Er(){l.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??Vn()),document.body.insertAdjacentElement("beforeend",e[1]??Vn()),Lt++,()=>{Lt===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),Lt--}},[])}function Vn(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var te=function(){return te=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(t[a]=n[a])}return t},te.apply(this,arguments)};function Sr(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function Ds(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,a;r<o;r++)(a||!(r in t))&&(a||(a=Array.prototype.slice.call(t,0,r)),a[r]=t[r]);return e.concat(a||Array.prototype.slice.call(t))}var st="right-scroll-bar-position",it="width-before-scroll-bar",Os="with-scroll-bars-hidden",Is="--removed-body-scroll-bar-size";function Ft(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function ks(e,t){var n=l.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var Ls=typeof window<"u"?l.useLayoutEffect:l.useEffect,qn=new WeakMap;function Fs(e,t){var n=ks(null,function(r){return e.forEach(function(o){return Ft(o,r)})});return Ls(function(){var r=qn.get(n);if(r){var o=new Set(r),a=new Set(e),i=n.current;o.forEach(function(c){a.has(c)||Ft(c,null)}),a.forEach(function(c){o.has(c)||Ft(c,i)})}qn.set(n,e)},[e]),n}function $s(e){return e}function Bs(e,t){t===void 0&&(t=$s);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(a){var i=t(a,r);return n.push(i),function(){n=n.filter(function(c){return c!==i})}},assignSyncMedium:function(a){for(r=!0;n.length;){var i=n;n=[],i.forEach(a)}n={push:function(c){return a(c)},filter:function(){return n}}},assignMedium:function(a){r=!0;var i=[];if(n.length){var c=n;n=[],c.forEach(a),i=n}var f=function(){var d=i;i=[],d.forEach(a)},p=function(){return Promise.resolve().then(f)};p(),n={push:function(d){i.push(d),p()},filter:function(d){return i=i.filter(d),n}}}};return o}function Ws(e){e===void 0&&(e={});var t=Bs(null);return t.options=te({async:!0,ssr:!1},e),t}var Rr=function(e){var t=e.sideCar,n=Sr(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return l.createElement(r,te({},n))};Rr.isSideCarExport=!0;function Hs(e,t){return e.useMedium(t),Rr}var Mr=Ws(),$t=function(){},yt=l.forwardRef(function(e,t){var n=l.useRef(null),r=l.useState({onScrollCapture:$t,onWheelCapture:$t,onTouchMoveCapture:$t}),o=r[0],a=r[1],i=e.forwardProps,c=e.children,f=e.className,p=e.removeScrollBar,d=e.enabled,u=e.shards,m=e.sideCar,g=e.noRelative,x=e.noIsolation,h=e.inert,v=e.allowPinchZoom,b=e.as,y=b===void 0?"div":b,E=e.gapMode,w=Sr(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),C=m,S=Fs([n,t]),R=te(te({},w),o);return l.createElement(l.Fragment,null,d&&l.createElement(C,{sideCar:Mr,removeScrollBar:p,shards:u,noRelative:g,noIsolation:x,inert:h,setCallbacks:a,allowPinchZoom:!!v,lockRef:n,gapMode:E}),i?l.cloneElement(l.Children.only(c),te(te({},R),{ref:S})):l.createElement(y,te({},R,{className:f,ref:S}),c))});yt.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};yt.classNames={fullWidth:it,zeroRight:st};var Us=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function Gs(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=Us();return t&&e.setAttribute("nonce",t),e}function Ks(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function zs(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var Vs=function(){var e=0,t=null;return{add:function(n){e==0&&(t=Gs())&&(Ks(t,n),zs(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},qs=function(){var e=Vs();return function(t,n){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},Ar=function(){var e=qs(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},Ys={left:0,top:0,right:0,gap:0},Bt=function(e){return parseInt(e||"",10)||0},Xs=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[Bt(n),Bt(r),Bt(o)]},Zs=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return Ys;var t=Xs(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},Qs=Ar(),_e="data-scroll-locked",Js=function(e,t,n,r){var o=e.left,a=e.top,i=e.right,c=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(Os,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(c,"px ").concat(r,`;
  }
  body[`).concat(_e,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(a,`px;
    padding-right: `).concat(i,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(c,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(st,` {
    right: `).concat(c,"px ").concat(r,`;
  }
  
  .`).concat(it,` {
    margin-right: `).concat(c,"px ").concat(r,`;
  }
  
  .`).concat(st," .").concat(st,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(it," .").concat(it,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(_e,`] {
    `).concat(Is,": ").concat(c,`px;
  }
`)},Yn=function(){var e=parseInt(document.body.getAttribute(_e)||"0",10);return isFinite(e)?e:0},ei=function(){l.useEffect(function(){return document.body.setAttribute(_e,(Yn()+1).toString()),function(){var e=Yn()-1;e<=0?document.body.removeAttribute(_e):document.body.setAttribute(_e,e.toString())}},[])},ti=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;ei();var a=l.useMemo(function(){return Zs(o)},[o]);return l.createElement(Qs,{styles:Js(a,!t,o,n?"":"!important")})},Xt=!1;if(typeof window<"u")try{var et=Object.defineProperty({},"passive",{get:function(){return Xt=!0,!0}});window.addEventListener("test",et,et),window.removeEventListener("test",et,et)}catch{Xt=!1}var Re=Xt?{passive:!1}:!1,ni=function(e){return e.tagName==="TEXTAREA"},_r=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!ni(e)&&n[t]==="visible")},ri=function(e){return _r(e,"overflowY")},oi=function(e){return _r(e,"overflowX")},Xn=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=Nr(e,r);if(o){var a=Pr(e,r),i=a[1],c=a[2];if(i>c)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},ai=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},si=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},Nr=function(e,t){return e==="v"?ri(t):oi(t)},Pr=function(e,t){return e==="v"?ai(t):si(t)},ii=function(e,t){return e==="h"&&t==="rtl"?-1:1},ci=function(e,t,n,r,o){var a=ii(e,window.getComputedStyle(t).direction),i=a*r,c=n.target,f=t.contains(c),p=!1,d=i>0,u=0,m=0;do{if(!c)break;var g=Pr(e,c),x=g[0],h=g[1],v=g[2],b=h-v-a*x;(x||b)&&Nr(e,c)&&(u+=b,m+=x);var y=c.parentNode;c=y&&y.nodeType===Node.DOCUMENT_FRAGMENT_NODE?y.host:y}while(!f&&c!==document.body||f&&(t.contains(c)||t===c));return(d&&Math.abs(u)<1||!d&&Math.abs(m)<1)&&(p=!0),p},tt=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Zn=function(e){return[e.deltaX,e.deltaY]},Qn=function(e){return e&&"current"in e?e.current:e},li=function(e,t){return e[0]===t[0]&&e[1]===t[1]},ui=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},di=0,Me=[];function fi(e){var t=l.useRef([]),n=l.useRef([0,0]),r=l.useRef(),o=l.useState(di++)[0],a=l.useState(Ar)[0],i=l.useRef(e);l.useEffect(function(){i.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var h=Ds([e.lockRef.current],(e.shards||[]).map(Qn),!0).filter(Boolean);return h.forEach(function(v){return v.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),h.forEach(function(v){return v.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var c=l.useCallback(function(h,v){if("touches"in h&&h.touches.length===2||h.type==="wheel"&&h.ctrlKey)return!i.current.allowPinchZoom;var b=tt(h),y=n.current,E="deltaX"in h?h.deltaX:y[0]-b[0],w="deltaY"in h?h.deltaY:y[1]-b[1],C,S=h.target,R=Math.abs(E)>Math.abs(w)?"h":"v";if("touches"in h&&R==="h"&&S.type==="range")return!1;var A=Xn(R,S);if(!A)return!0;if(A?C=R:(C=R==="v"?"h":"v",A=Xn(R,S)),!A)return!1;if(!r.current&&"changedTouches"in h&&(E||w)&&(r.current=C),!C)return!0;var D=r.current||C;return ci(D,v,h,D==="h"?E:w)},[]),f=l.useCallback(function(h){var v=h;if(!(!Me.length||Me[Me.length-1]!==a)){var b="deltaY"in v?Zn(v):tt(v),y=t.current.filter(function(C){return C.name===v.type&&(C.target===v.target||v.target===C.shadowParent)&&li(C.delta,b)})[0];if(y&&y.should){v.cancelable&&v.preventDefault();return}if(!y){var E=(i.current.shards||[]).map(Qn).filter(Boolean).filter(function(C){return C.contains(v.target)}),w=E.length>0?c(v,E[0]):!i.current.noIsolation;w&&v.cancelable&&v.preventDefault()}}},[]),p=l.useCallback(function(h,v,b,y){var E={name:h,delta:v,target:b,should:y,shadowParent:pi(b)};t.current.push(E),setTimeout(function(){t.current=t.current.filter(function(w){return w!==E})},1)},[]),d=l.useCallback(function(h){n.current=tt(h),r.current=void 0},[]),u=l.useCallback(function(h){p(h.type,Zn(h),h.target,c(h,e.lockRef.current))},[]),m=l.useCallback(function(h){p(h.type,tt(h),h.target,c(h,e.lockRef.current))},[]);l.useEffect(function(){return Me.push(a),e.setCallbacks({onScrollCapture:u,onWheelCapture:u,onTouchMoveCapture:m}),document.addEventListener("wheel",f,Re),document.addEventListener("touchmove",f,Re),document.addEventListener("touchstart",d,Re),function(){Me=Me.filter(function(h){return h!==a}),document.removeEventListener("wheel",f,Re),document.removeEventListener("touchmove",f,Re),document.removeEventListener("touchstart",d,Re)}},[]);var g=e.removeScrollBar,x=e.inert;return l.createElement(l.Fragment,null,x?l.createElement(a,{styles:ui(o)}):null,g?l.createElement(ti,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}function pi(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const mi=Hs(Mr,fi);var cn=l.forwardRef(function(e,t){return l.createElement(yt,te({},e,{ref:t,sideCar:mi}))});cn.classNames=yt.classNames;var hi=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},Ae=new WeakMap,nt=new WeakMap,rt={},Wt=0,jr=function(e){return e&&(e.host||jr(e.parentNode))},gi=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=jr(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},vi=function(e,t,n,r){var o=gi(t,Array.isArray(e)?e:[e]);rt[n]||(rt[n]=new WeakMap);var a=rt[n],i=[],c=new Set,f=new Set(o),p=function(u){!u||c.has(u)||(c.add(u),p(u.parentNode))};o.forEach(p);var d=function(u){!u||f.has(u)||Array.prototype.forEach.call(u.children,function(m){if(c.has(m))d(m);else try{var g=m.getAttribute(r),x=g!==null&&g!=="false",h=(Ae.get(m)||0)+1,v=(a.get(m)||0)+1;Ae.set(m,h),a.set(m,v),i.push(m),h===1&&x&&nt.set(m,!0),v===1&&m.setAttribute(n,"true"),x||m.setAttribute(r,"true")}catch(b){console.error("aria-hidden: cannot operate on ",m,b)}})};return d(t),c.clear(),Wt++,function(){i.forEach(function(u){var m=Ae.get(u)-1,g=a.get(u)-1;Ae.set(u,m),a.set(u,g),m||(nt.has(u)||u.removeAttribute(r),nt.delete(u)),g||u.removeAttribute(n)}),Wt--,Wt||(Ae=new WeakMap,Ae=new WeakMap,nt=new WeakMap,rt={})}},Tr=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=hi(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),vi(r,o,n,"aria-hidden")):function(){return null}},Ct="Dialog",[Dr,gf]=he(Ct),[xi,Q]=Dr(Ct),Or=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:a,modal:i=!0}=e,c=l.useRef(null),f=l.useRef(null),[p,d]=xt({prop:r,defaultProp:o??!1,onChange:a,caller:Ct});return s.jsx(xi,{scope:t,triggerRef:c,contentRef:f,contentId:we(),titleId:we(),descriptionId:we(),open:p,onOpenChange:d,onOpenToggle:l.useCallback(()=>d(u=>!u),[d]),modal:i,children:n})};Or.displayName=Ct;var Ir="DialogTrigger",wi=l.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Q(Ir,n),a=W(t,o.triggerRef);return s.jsx(I.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":dn(o.open),...r,ref:a,onClick:_(e.onClick,o.onOpenToggle)})});wi.displayName=Ir;var ln="DialogPortal",[bi,kr]=Dr(ln,{forceMount:void 0}),Lr=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:o}=e,a=Q(ln,t);return s.jsx(bi,{scope:t,forceMount:n,children:l.Children.map(r,i=>s.jsx(ue,{present:n||a.open,children:s.jsx(bt,{asChild:!0,container:o,children:i})}))})};Lr.displayName=ln;var ut="DialogOverlay",Fr=l.forwardRef((e,t)=>{const n=kr(ut,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=Q(ut,e.__scopeDialog);return a.modal?s.jsx(ue,{present:r||a.open,children:s.jsx(Ci,{...o,ref:t})}):null});Fr.displayName=ut;var yi=lt("DialogOverlay.RemoveScroll"),Ci=l.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Q(ut,n);return s.jsx(cn,{as:yi,allowPinchZoom:!0,shards:[o.contentRef],children:s.jsx(I.div,{"data-state":dn(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),ye="DialogContent",$r=l.forwardRef((e,t)=>{const n=kr(ye,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=Q(ye,e.__scopeDialog);return s.jsx(ue,{present:r||a.open,children:a.modal?s.jsx(Ei,{...o,ref:t}):s.jsx(Si,{...o,ref:t})})});$r.displayName=ye;var Ei=l.forwardRef((e,t)=>{const n=Q(ye,e.__scopeDialog),r=l.useRef(null),o=W(t,n.contentRef,r);return l.useEffect(()=>{const a=r.current;if(a)return Tr(a)},[]),s.jsx(Br,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:_(e.onCloseAutoFocus,a=>{var i;a.preventDefault(),(i=n.triggerRef.current)==null||i.focus()}),onPointerDownOutside:_(e.onPointerDownOutside,a=>{const i=a.detail.originalEvent,c=i.button===0&&i.ctrlKey===!0;(i.button===2||c)&&a.preventDefault()}),onFocusOutside:_(e.onFocusOutside,a=>a.preventDefault())})}),Si=l.forwardRef((e,t)=>{const n=Q(ye,e.__scopeDialog),r=l.useRef(!1),o=l.useRef(!1);return s.jsx(Br,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:a=>{var i,c;(i=e.onCloseAutoFocus)==null||i.call(e,a),a.defaultPrevented||(r.current||(c=n.triggerRef.current)==null||c.focus(),a.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:a=>{var f,p;(f=e.onInteractOutside)==null||f.call(e,a),a.defaultPrevented||(r.current=!0,a.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const i=a.target;((p=n.triggerRef.current)==null?void 0:p.contains(i))&&a.preventDefault(),a.detail.originalEvent.type==="focusin"&&o.current&&a.preventDefault()}})}),Br=l.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:a,...i}=e,c=Q(ye,n),f=l.useRef(null),p=W(t,f);return Er(),s.jsxs(s.Fragment,{children:[s.jsx(sn,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:a,children:s.jsx(wt,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":dn(c.open),...i,ref:p,onDismiss:()=>c.onOpenChange(!1)})}),s.jsxs(s.Fragment,{children:[s.jsx(Ri,{titleId:c.titleId}),s.jsx(Ai,{contentRef:f,descriptionId:c.descriptionId})]})]})}),un="DialogTitle",Wr=l.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Q(un,n);return s.jsx(I.h2,{id:o.titleId,...r,ref:t})});Wr.displayName=un;var Hr="DialogDescription",Ur=l.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Q(Hr,n);return s.jsx(I.p,{id:o.descriptionId,...r,ref:t})});Ur.displayName=Hr;var Gr="DialogClose",Kr=l.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Q(Gr,n);return s.jsx(I.button,{type:"button",...r,ref:t,onClick:_(e.onClick,()=>o.onOpenChange(!1))})});Kr.displayName=Gr;function dn(e){return e?"open":"closed"}var zr="DialogTitleWarning",[vf,Vr]=ka(zr,{contentName:ye,titleName:un,docsSlug:"dialog"}),Ri=({titleId:e})=>{const t=Vr(zr),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return l.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},Mi="DialogDescriptionWarning",Ai=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${Vr(Mi).contentName}}.`;return l.useEffect(()=>{var a;const o=(a=e.current)==null?void 0:a.getAttribute("aria-describedby");t&&o&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},_i=Or,Ni=Lr,Pi=Fr,ji=$r,Ti=Wr,Di=Ur,Oi=Kr;function Ii({...e}){return s.jsx(_i,{"data-slot":"sheet",...e})}function ki({...e}){return s.jsx(Ni,{"data-slot":"sheet-portal",...e})}function Li({className:e,...t}){return s.jsx(Pi,{"data-slot":"sheet-overlay",className:P("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/80",e),...t})}function Fi({className:e,children:t,side:n="right",...r}){return s.jsxs(ki,{children:[s.jsx(Li,{}),s.jsxs(ji,{"data-slot":"sheet-content",className:P("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500",n==="right"&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm",n==="left"&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm",n==="top"&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b",n==="bottom"&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",e),...r,children:[t,s.jsxs(Oi,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[s.jsx(xr,{className:"size-4"}),s.jsx("span",{className:"sr-only",children:"Close"})]})]})]})}function $i({className:e,...t}){return s.jsx("div",{"data-slot":"sheet-header",className:P("flex flex-col gap-1.5 p-4",e),...t})}function Bi({className:e,...t}){return s.jsx(Ti,{"data-slot":"sheet-title",className:P("text-foreground font-semibold",e),...t})}function Wi({className:e,...t}){return s.jsx(Di,{"data-slot":"sheet-description",className:P("text-muted-foreground text-sm",e),...t})}const Hi=["top","right","bottom","left"],pe=Math.min,K=Math.max,dt=Math.round,ot=Math.floor,re=e=>({x:e,y:e}),Ui={left:"right",right:"left",bottom:"top",top:"bottom"},Gi={start:"end",end:"start"};function Zt(e,t,n){return K(e,pe(t,n))}function ce(e,t){return typeof e=="function"?e(t):e}function le(e){return e.split("-")[0]}function Te(e){return e.split("-")[1]}function fn(e){return e==="x"?"y":"x"}function pn(e){return e==="y"?"height":"width"}const Ki=new Set(["top","bottom"]);function ne(e){return Ki.has(le(e))?"y":"x"}function mn(e){return fn(ne(e))}function zi(e,t,n){n===void 0&&(n=!1);const r=Te(e),o=mn(e),a=pn(o);let i=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[a]>t.floating[a]&&(i=ft(i)),[i,ft(i)]}function Vi(e){const t=ft(e);return[Qt(e),t,Qt(t)]}function Qt(e){return e.replace(/start|end/g,t=>Gi[t])}const Jn=["left","right"],er=["right","left"],qi=["top","bottom"],Yi=["bottom","top"];function Xi(e,t,n){switch(e){case"top":case"bottom":return n?t?er:Jn:t?Jn:er;case"left":case"right":return t?qi:Yi;default:return[]}}function Zi(e,t,n,r){const o=Te(e);let a=Xi(le(e),n==="start",r);return o&&(a=a.map(i=>i+"-"+o),t&&(a=a.concat(a.map(Qt)))),a}function ft(e){return e.replace(/left|right|bottom|top/g,t=>Ui[t])}function Qi(e){return{top:0,right:0,bottom:0,left:0,...e}}function qr(e){return typeof e!="number"?Qi(e):{top:e,right:e,bottom:e,left:e}}function pt(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function tr(e,t,n){let{reference:r,floating:o}=e;const a=ne(t),i=mn(t),c=pn(i),f=le(t),p=a==="y",d=r.x+r.width/2-o.width/2,u=r.y+r.height/2-o.height/2,m=r[c]/2-o[c]/2;let g;switch(f){case"top":g={x:d,y:r.y-o.height};break;case"bottom":g={x:d,y:r.y+r.height};break;case"right":g={x:r.x+r.width,y:u};break;case"left":g={x:r.x-o.width,y:u};break;default:g={x:r.x,y:r.y}}switch(Te(t)){case"start":g[i]-=m*(n&&p?-1:1);break;case"end":g[i]+=m*(n&&p?-1:1);break}return g}const Ji=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:a=[],platform:i}=n,c=a.filter(Boolean),f=await(i.isRTL==null?void 0:i.isRTL(t));let p=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:d,y:u}=tr(p,r,f),m=r,g={},x=0;for(let h=0;h<c.length;h++){const{name:v,fn:b}=c[h],{x:y,y:E,data:w,reset:C}=await b({x:d,y:u,initialPlacement:r,placement:m,strategy:o,middlewareData:g,rects:p,platform:i,elements:{reference:e,floating:t}});d=y??d,u=E??u,g={...g,[v]:{...g[v],...w}},C&&x<=50&&(x++,typeof C=="object"&&(C.placement&&(m=C.placement),C.rects&&(p=C.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:o}):C.rects),{x:d,y:u}=tr(p,m,f)),h=-1)}return{x:d,y:u,placement:m,strategy:o,middlewareData:g}};async function He(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:a,rects:i,elements:c,strategy:f}=e,{boundary:p="clippingAncestors",rootBoundary:d="viewport",elementContext:u="floating",altBoundary:m=!1,padding:g=0}=ce(t,e),x=qr(g),v=c[m?u==="floating"?"reference":"floating":u],b=pt(await a.getClippingRect({element:(n=await(a.isElement==null?void 0:a.isElement(v)))==null||n?v:v.contextElement||await(a.getDocumentElement==null?void 0:a.getDocumentElement(c.floating)),boundary:p,rootBoundary:d,strategy:f})),y=u==="floating"?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,E=await(a.getOffsetParent==null?void 0:a.getOffsetParent(c.floating)),w=await(a.isElement==null?void 0:a.isElement(E))?await(a.getScale==null?void 0:a.getScale(E))||{x:1,y:1}:{x:1,y:1},C=pt(a.convertOffsetParentRelativeRectToViewportRelativeRect?await a.convertOffsetParentRelativeRectToViewportRelativeRect({elements:c,rect:y,offsetParent:E,strategy:f}):y);return{top:(b.top-C.top+x.top)/w.y,bottom:(C.bottom-b.bottom+x.bottom)/w.y,left:(b.left-C.left+x.left)/w.x,right:(C.right-b.right+x.right)/w.x}}const ec=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:a,platform:i,elements:c,middlewareData:f}=t,{element:p,padding:d=0}=ce(e,t)||{};if(p==null)return{};const u=qr(d),m={x:n,y:r},g=mn(o),x=pn(g),h=await i.getDimensions(p),v=g==="y",b=v?"top":"left",y=v?"bottom":"right",E=v?"clientHeight":"clientWidth",w=a.reference[x]+a.reference[g]-m[g]-a.floating[x],C=m[g]-a.reference[g],S=await(i.getOffsetParent==null?void 0:i.getOffsetParent(p));let R=S?S[E]:0;(!R||!await(i.isElement==null?void 0:i.isElement(S)))&&(R=c.floating[E]||a.floating[x]);const A=w/2-C/2,D=R/2-h[x]/2-1,j=pe(u[b],D),F=pe(u[y],D),$=j,k=R-h[x]-F,O=R/2-h[x]/2+A,H=Zt($,O,k),T=!f.arrow&&Te(o)!=null&&O!==H&&a.reference[x]/2-(O<$?j:F)-h[x]/2<0,B=T?O<$?O-$:O-k:0;return{[g]:m[g]+B,data:{[g]:H,centerOffset:O-H-B,...T&&{alignmentOffset:B}},reset:T}}}),tc=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:a,rects:i,initialPlacement:c,platform:f,elements:p}=t,{mainAxis:d=!0,crossAxis:u=!0,fallbackPlacements:m,fallbackStrategy:g="bestFit",fallbackAxisSideDirection:x="none",flipAlignment:h=!0,...v}=ce(e,t);if((n=a.arrow)!=null&&n.alignmentOffset)return{};const b=le(o),y=ne(c),E=le(c)===c,w=await(f.isRTL==null?void 0:f.isRTL(p.floating)),C=m||(E||!h?[ft(c)]:Vi(c)),S=x!=="none";!m&&S&&C.push(...Zi(c,h,x,w));const R=[c,...C],A=await He(t,v),D=[];let j=((r=a.flip)==null?void 0:r.overflows)||[];if(d&&D.push(A[b]),u){const O=zi(o,i,w);D.push(A[O[0]],A[O[1]])}if(j=[...j,{placement:o,overflows:D}],!D.every(O=>O<=0)){var F,$;const O=(((F=a.flip)==null?void 0:F.index)||0)+1,H=R[O];if(H&&(!(u==="alignment"?y!==ne(H):!1)||j.every(N=>N.overflows[0]>0&&ne(N.placement)===y)))return{data:{index:O,overflows:j},reset:{placement:H}};let T=($=j.filter(B=>B.overflows[0]<=0).sort((B,N)=>B.overflows[1]-N.overflows[1])[0])==null?void 0:$.placement;if(!T)switch(g){case"bestFit":{var k;const B=(k=j.filter(N=>{if(S){const M=ne(N.placement);return M===y||M==="y"}return!0}).map(N=>[N.placement,N.overflows.filter(M=>M>0).reduce((M,L)=>M+L,0)]).sort((N,M)=>N[1]-M[1])[0])==null?void 0:k[0];B&&(T=B);break}case"initialPlacement":T=c;break}if(o!==T)return{reset:{placement:T}}}return{}}}};function nr(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function rr(e){return Hi.some(t=>e[t]>=0)}const nc=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=ce(e,t);switch(r){case"referenceHidden":{const a=await He(t,{...o,elementContext:"reference"}),i=nr(a,n.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:rr(i)}}}case"escaped":{const a=await He(t,{...o,altBoundary:!0}),i=nr(a,n.floating);return{data:{escapedOffsets:i,escaped:rr(i)}}}default:return{}}}}},Yr=new Set(["left","top"]);async function rc(e,t){const{placement:n,platform:r,elements:o}=e,a=await(r.isRTL==null?void 0:r.isRTL(o.floating)),i=le(n),c=Te(n),f=ne(n)==="y",p=Yr.has(i)?-1:1,d=a&&f?-1:1,u=ce(t,e);let{mainAxis:m,crossAxis:g,alignmentAxis:x}=typeof u=="number"?{mainAxis:u,crossAxis:0,alignmentAxis:null}:{mainAxis:u.mainAxis||0,crossAxis:u.crossAxis||0,alignmentAxis:u.alignmentAxis};return c&&typeof x=="number"&&(g=c==="end"?x*-1:x),f?{x:g*d,y:m*p}:{x:m*p,y:g*d}}const oc=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:a,placement:i,middlewareData:c}=t,f=await rc(t,e);return i===((n=c.offset)==null?void 0:n.placement)&&(r=c.arrow)!=null&&r.alignmentOffset?{}:{x:o+f.x,y:a+f.y,data:{...f,placement:i}}}}},ac=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:a=!0,crossAxis:i=!1,limiter:c={fn:v=>{let{x:b,y}=v;return{x:b,y}}},...f}=ce(e,t),p={x:n,y:r},d=await He(t,f),u=ne(le(o)),m=fn(u);let g=p[m],x=p[u];if(a){const v=m==="y"?"top":"left",b=m==="y"?"bottom":"right",y=g+d[v],E=g-d[b];g=Zt(y,g,E)}if(i){const v=u==="y"?"top":"left",b=u==="y"?"bottom":"right",y=x+d[v],E=x-d[b];x=Zt(y,x,E)}const h=c.fn({...t,[m]:g,[u]:x});return{...h,data:{x:h.x-n,y:h.y-r,enabled:{[m]:a,[u]:i}}}}}},sc=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:a,middlewareData:i}=t,{offset:c=0,mainAxis:f=!0,crossAxis:p=!0}=ce(e,t),d={x:n,y:r},u=ne(o),m=fn(u);let g=d[m],x=d[u];const h=ce(c,t),v=typeof h=="number"?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(f){const E=m==="y"?"height":"width",w=a.reference[m]-a.floating[E]+v.mainAxis,C=a.reference[m]+a.reference[E]-v.mainAxis;g<w?g=w:g>C&&(g=C)}if(p){var b,y;const E=m==="y"?"width":"height",w=Yr.has(le(o)),C=a.reference[u]-a.floating[E]+(w&&((b=i.offset)==null?void 0:b[u])||0)+(w?0:v.crossAxis),S=a.reference[u]+a.reference[E]+(w?0:((y=i.offset)==null?void 0:y[u])||0)-(w?v.crossAxis:0);x<C?x=C:x>S&&(x=S)}return{[m]:g,[u]:x}}}},ic=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:a,platform:i,elements:c}=t,{apply:f=()=>{},...p}=ce(e,t),d=await He(t,p),u=le(o),m=Te(o),g=ne(o)==="y",{width:x,height:h}=a.floating;let v,b;u==="top"||u==="bottom"?(v=u,b=m===(await(i.isRTL==null?void 0:i.isRTL(c.floating))?"start":"end")?"left":"right"):(b=u,v=m==="end"?"top":"bottom");const y=h-d.top-d.bottom,E=x-d.left-d.right,w=pe(h-d[v],y),C=pe(x-d[b],E),S=!t.middlewareData.shift;let R=w,A=C;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(A=E),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(R=y),S&&!m){const j=K(d.left,0),F=K(d.right,0),$=K(d.top,0),k=K(d.bottom,0);g?A=x-2*(j!==0||F!==0?j+F:K(d.left,d.right)):R=h-2*($!==0||k!==0?$+k:K(d.top,d.bottom))}await f({...t,availableWidth:A,availableHeight:R});const D=await i.getDimensions(c.floating);return x!==D.width||h!==D.height?{reset:{rects:!0}}:{}}}};function Et(){return typeof window<"u"}function De(e){return Xr(e)?(e.nodeName||"").toLowerCase():"#document"}function z(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function se(e){var t;return(t=(Xr(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Xr(e){return Et()?e instanceof Node||e instanceof z(e).Node:!1}function X(e){return Et()?e instanceof Element||e instanceof z(e).Element:!1}function ae(e){return Et()?e instanceof HTMLElement||e instanceof z(e).HTMLElement:!1}function or(e){return!Et()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof z(e).ShadowRoot}const cc=new Set(["inline","contents"]);function Ve(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=Z(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!cc.has(o)}const lc=new Set(["table","td","th"]);function uc(e){return lc.has(De(e))}const dc=[":popover-open",":modal"];function St(e){return dc.some(t=>{try{return e.matches(t)}catch{return!1}})}const fc=["transform","translate","scale","rotate","perspective"],pc=["transform","translate","scale","rotate","perspective","filter"],mc=["paint","layout","strict","content"];function hn(e){const t=gn(),n=X(e)?Z(e):e;return fc.some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||pc.some(r=>(n.willChange||"").includes(r))||mc.some(r=>(n.contain||"").includes(r))}function hc(e){let t=me(e);for(;ae(t)&&!Pe(t);){if(hn(t))return t;if(St(t))return null;t=me(t)}return null}function gn(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const gc=new Set(["html","body","#document"]);function Pe(e){return gc.has(De(e))}function Z(e){return z(e).getComputedStyle(e)}function Rt(e){return X(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function me(e){if(De(e)==="html")return e;const t=e.assignedSlot||e.parentNode||or(e)&&e.host||se(e);return or(t)?t.host:t}function Zr(e){const t=me(e);return Pe(t)?e.ownerDocument?e.ownerDocument.body:e.body:ae(t)&&Ve(t)?t:Zr(t)}function Ue(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=Zr(e),a=o===((r=e.ownerDocument)==null?void 0:r.body),i=z(o);if(a){const c=Jt(i);return t.concat(i,i.visualViewport||[],Ve(o)?o:[],c&&n?Ue(c):[])}return t.concat(o,Ue(o,[],n))}function Jt(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Qr(e){const t=Z(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=ae(e),a=o?e.offsetWidth:n,i=o?e.offsetHeight:r,c=dt(n)!==a||dt(r)!==i;return c&&(n=a,r=i),{width:n,height:r,$:c}}function vn(e){return X(e)?e:e.contextElement}function Ne(e){const t=vn(e);if(!ae(t))return re(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:a}=Qr(t);let i=(a?dt(n.width):n.width)/r,c=(a?dt(n.height):n.height)/o;return(!i||!Number.isFinite(i))&&(i=1),(!c||!Number.isFinite(c))&&(c=1),{x:i,y:c}}const vc=re(0);function Jr(e){const t=z(e);return!gn()||!t.visualViewport?vc:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function xc(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==z(e)?!1:t}function Ce(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),a=vn(e);let i=re(1);t&&(r?X(r)&&(i=Ne(r)):i=Ne(e));const c=xc(a,n,r)?Jr(a):re(0);let f=(o.left+c.x)/i.x,p=(o.top+c.y)/i.y,d=o.width/i.x,u=o.height/i.y;if(a){const m=z(a),g=r&&X(r)?z(r):r;let x=m,h=Jt(x);for(;h&&r&&g!==x;){const v=Ne(h),b=h.getBoundingClientRect(),y=Z(h),E=b.left+(h.clientLeft+parseFloat(y.paddingLeft))*v.x,w=b.top+(h.clientTop+parseFloat(y.paddingTop))*v.y;f*=v.x,p*=v.y,d*=v.x,u*=v.y,f+=E,p+=w,x=z(h),h=Jt(x)}}return pt({width:d,height:u,x:f,y:p})}function xn(e,t){const n=Rt(e).scrollLeft;return t?t.left+n:Ce(se(e)).left+n}function eo(e,t,n){n===void 0&&(n=!1);const r=e.getBoundingClientRect(),o=r.left+t.scrollLeft-(n?0:xn(e,r)),a=r.top+t.scrollTop;return{x:o,y:a}}function wc(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const a=o==="fixed",i=se(r),c=t?St(t.floating):!1;if(r===i||c&&a)return n;let f={scrollLeft:0,scrollTop:0},p=re(1);const d=re(0),u=ae(r);if((u||!u&&!a)&&((De(r)!=="body"||Ve(i))&&(f=Rt(r)),ae(r))){const g=Ce(r);p=Ne(r),d.x=g.x+r.clientLeft,d.y=g.y+r.clientTop}const m=i&&!u&&!a?eo(i,f,!0):re(0);return{width:n.width*p.x,height:n.height*p.y,x:n.x*p.x-f.scrollLeft*p.x+d.x+m.x,y:n.y*p.y-f.scrollTop*p.y+d.y+m.y}}function bc(e){return Array.from(e.getClientRects())}function yc(e){const t=se(e),n=Rt(e),r=e.ownerDocument.body,o=K(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),a=K(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let i=-n.scrollLeft+xn(e);const c=-n.scrollTop;return Z(r).direction==="rtl"&&(i+=K(t.clientWidth,r.clientWidth)-o),{width:o,height:a,x:i,y:c}}function Cc(e,t){const n=z(e),r=se(e),o=n.visualViewport;let a=r.clientWidth,i=r.clientHeight,c=0,f=0;if(o){a=o.width,i=o.height;const p=gn();(!p||p&&t==="fixed")&&(c=o.offsetLeft,f=o.offsetTop)}return{width:a,height:i,x:c,y:f}}const Ec=new Set(["absolute","fixed"]);function Sc(e,t){const n=Ce(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,a=ae(e)?Ne(e):re(1),i=e.clientWidth*a.x,c=e.clientHeight*a.y,f=o*a.x,p=r*a.y;return{width:i,height:c,x:f,y:p}}function ar(e,t,n){let r;if(t==="viewport")r=Cc(e,n);else if(t==="document")r=yc(se(e));else if(X(t))r=Sc(t,n);else{const o=Jr(e);r={x:t.x-o.x,y:t.y-o.y,width:t.width,height:t.height}}return pt(r)}function to(e,t){const n=me(e);return n===t||!X(n)||Pe(n)?!1:Z(n).position==="fixed"||to(n,t)}function Rc(e,t){const n=t.get(e);if(n)return n;let r=Ue(e,[],!1).filter(c=>X(c)&&De(c)!=="body"),o=null;const a=Z(e).position==="fixed";let i=a?me(e):e;for(;X(i)&&!Pe(i);){const c=Z(i),f=hn(i);!f&&c.position==="fixed"&&(o=null),(a?!f&&!o:!f&&c.position==="static"&&!!o&&Ec.has(o.position)||Ve(i)&&!f&&to(e,i))?r=r.filter(d=>d!==i):o=c,i=me(i)}return t.set(e,r),r}function Mc(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const i=[...n==="clippingAncestors"?St(t)?[]:Rc(t,this._c):[].concat(n),r],c=i[0],f=i.reduce((p,d)=>{const u=ar(t,d,o);return p.top=K(u.top,p.top),p.right=pe(u.right,p.right),p.bottom=pe(u.bottom,p.bottom),p.left=K(u.left,p.left),p},ar(t,c,o));return{width:f.right-f.left,height:f.bottom-f.top,x:f.left,y:f.top}}function Ac(e){const{width:t,height:n}=Qr(e);return{width:t,height:n}}function _c(e,t,n){const r=ae(t),o=se(t),a=n==="fixed",i=Ce(e,!0,a,t);let c={scrollLeft:0,scrollTop:0};const f=re(0);function p(){f.x=xn(o)}if(r||!r&&!a)if((De(t)!=="body"||Ve(o))&&(c=Rt(t)),r){const g=Ce(t,!0,a,t);f.x=g.x+t.clientLeft,f.y=g.y+t.clientTop}else o&&p();a&&!r&&o&&p();const d=o&&!r&&!a?eo(o,c):re(0),u=i.left+c.scrollLeft-f.x-d.x,m=i.top+c.scrollTop-f.y-d.y;return{x:u,y:m,width:i.width,height:i.height}}function Ht(e){return Z(e).position==="static"}function sr(e,t){if(!ae(e)||Z(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return se(e)===n&&(n=n.ownerDocument.body),n}function no(e,t){const n=z(e);if(St(e))return n;if(!ae(e)){let o=me(e);for(;o&&!Pe(o);){if(X(o)&&!Ht(o))return o;o=me(o)}return n}let r=sr(e,t);for(;r&&uc(r)&&Ht(r);)r=sr(r,t);return r&&Pe(r)&&Ht(r)&&!hn(r)?n:r||hc(e)||n}const Nc=async function(e){const t=this.getOffsetParent||no,n=this.getDimensions,r=await n(e.floating);return{reference:_c(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function Pc(e){return Z(e).direction==="rtl"}const jc={convertOffsetParentRelativeRectToViewportRelativeRect:wc,getDocumentElement:se,getClippingRect:Mc,getOffsetParent:no,getElementRects:Nc,getClientRects:bc,getDimensions:Ac,getScale:Ne,isElement:X,isRTL:Pc};function ro(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function Tc(e,t){let n=null,r;const o=se(e);function a(){var c;clearTimeout(r),(c=n)==null||c.disconnect(),n=null}function i(c,f){c===void 0&&(c=!1),f===void 0&&(f=1),a();const p=e.getBoundingClientRect(),{left:d,top:u,width:m,height:g}=p;if(c||t(),!m||!g)return;const x=ot(u),h=ot(o.clientWidth-(d+m)),v=ot(o.clientHeight-(u+g)),b=ot(d),E={rootMargin:-x+"px "+-h+"px "+-v+"px "+-b+"px",threshold:K(0,pe(1,f))||1};let w=!0;function C(S){const R=S[0].intersectionRatio;if(R!==f){if(!w)return i();R?i(!1,R):r=setTimeout(()=>{i(!1,1e-7)},1e3)}R===1&&!ro(p,e.getBoundingClientRect())&&i(),w=!1}try{n=new IntersectionObserver(C,{...E,root:o.ownerDocument})}catch{n=new IntersectionObserver(C,E)}n.observe(e)}return i(!0),a}function Dc(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:a=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:c=typeof IntersectionObserver=="function",animationFrame:f=!1}=r,p=vn(e),d=o||a?[...p?Ue(p):[],...Ue(t)]:[];d.forEach(b=>{o&&b.addEventListener("scroll",n,{passive:!0}),a&&b.addEventListener("resize",n)});const u=p&&c?Tc(p,n):null;let m=-1,g=null;i&&(g=new ResizeObserver(b=>{let[y]=b;y&&y.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var E;(E=g)==null||E.observe(t)})),n()}),p&&!f&&g.observe(p),g.observe(t));let x,h=f?Ce(e):null;f&&v();function v(){const b=Ce(e);h&&!ro(h,b)&&n(),h=b,x=requestAnimationFrame(v)}return n(),()=>{var b;d.forEach(y=>{o&&y.removeEventListener("scroll",n),a&&y.removeEventListener("resize",n)}),u==null||u(),(b=g)==null||b.disconnect(),g=null,f&&cancelAnimationFrame(x)}}const Oc=oc,Ic=ac,kc=tc,Lc=ic,Fc=nc,ir=ec,$c=sc,Bc=(e,t,n)=>{const r=new Map,o={platform:jc,...n},a={...o.platform,_c:r};return Ji(e,t,{...o,platform:a})};var Wc=typeof document<"u",Hc=function(){},ct=Wc?l.useLayoutEffect:Hc;function mt(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!mt(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const a=o[r];if(!(a==="_owner"&&e.$$typeof)&&!mt(e[a],t[a]))return!1}return!0}return e!==e&&t!==t}function oo(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function cr(e,t){const n=oo(e);return Math.round(t*n)/n}function Ut(e){const t=l.useRef(e);return ct(()=>{t.current=e}),t}function Uc(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:a,floating:i}={},transform:c=!0,whileElementsMounted:f,open:p}=e,[d,u]=l.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[m,g]=l.useState(r);mt(m,r)||g(r);const[x,h]=l.useState(null),[v,b]=l.useState(null),y=l.useCallback(N=>{N!==S.current&&(S.current=N,h(N))},[]),E=l.useCallback(N=>{N!==R.current&&(R.current=N,b(N))},[]),w=a||x,C=i||v,S=l.useRef(null),R=l.useRef(null),A=l.useRef(d),D=f!=null,j=Ut(f),F=Ut(o),$=Ut(p),k=l.useCallback(()=>{if(!S.current||!R.current)return;const N={placement:t,strategy:n,middleware:m};F.current&&(N.platform=F.current),Bc(S.current,R.current,N).then(M=>{const L={...M,isPositioned:$.current!==!1};O.current&&!mt(A.current,L)&&(A.current=L,Ba.flushSync(()=>{u(L)}))})},[m,t,n,F,$]);ct(()=>{p===!1&&A.current.isPositioned&&(A.current.isPositioned=!1,u(N=>({...N,isPositioned:!1})))},[p]);const O=l.useRef(!1);ct(()=>(O.current=!0,()=>{O.current=!1}),[]),ct(()=>{if(w&&(S.current=w),C&&(R.current=C),w&&C){if(j.current)return j.current(w,C,k);k()}},[w,C,k,j,D]);const H=l.useMemo(()=>({reference:S,floating:R,setReference:y,setFloating:E}),[y,E]),T=l.useMemo(()=>({reference:w,floating:C}),[w,C]),B=l.useMemo(()=>{const N={position:n,left:0,top:0};if(!T.floating)return N;const M=cr(T.floating,d.x),L=cr(T.floating,d.y);return c?{...N,transform:"translate("+M+"px, "+L+"px)",...oo(T.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:M,top:L}},[n,c,T.floating,d.x,d.y]);return l.useMemo(()=>({...d,update:k,refs:H,elements:T,floatingStyles:B}),[d,k,H,T,B])}const Gc=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?ir({element:r.current,padding:o}).fn(n):{}:r?ir({element:r,padding:o}).fn(n):{}}}},Kc=(e,t)=>({...Oc(e),options:[e,t]}),zc=(e,t)=>({...Ic(e),options:[e,t]}),Vc=(e,t)=>({...$c(e),options:[e,t]}),qc=(e,t)=>({...kc(e),options:[e,t]}),Yc=(e,t)=>({...Lc(e),options:[e,t]}),Xc=(e,t)=>({...Fc(e),options:[e,t]}),Zc=(e,t)=>({...Gc(e),options:[e,t]});var Qc="Arrow",ao=l.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...a}=e;return s.jsx(I.svg,{...a,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:s.jsx("polygon",{points:"0,0 30,0 15,10"})})});ao.displayName=Qc;var Jc=ao,wn="Popper",[so,Mt]=he(wn),[el,io]=so(wn),co=e=>{const{__scopePopper:t,children:n}=e,[r,o]=l.useState(null);return s.jsx(el,{scope:t,anchor:r,onAnchorChange:o,children:n})};co.displayName=wn;var lo="PopperAnchor",uo=l.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,a=io(lo,n),i=l.useRef(null),c=W(t,i);return l.useEffect(()=>{a.onAnchorChange((r==null?void 0:r.current)||i.current)}),r?null:s.jsx(I.div,{...o,ref:c})});uo.displayName=lo;var bn="PopperContent",[tl,nl]=so(bn),fo=l.forwardRef((e,t)=>{var ie,ke,q,Le,Ln,Fn;const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:a="center",alignOffset:i=0,arrowPadding:c=0,avoidCollisions:f=!0,collisionBoundary:p=[],collisionPadding:d=0,sticky:u="partial",hideWhenDetached:m=!1,updatePositionStrategy:g="optimized",onPlaced:x,...h}=e,v=io(bn,n),[b,y]=l.useState(null),E=W(t,Fe=>y(Fe)),[w,C]=l.useState(null),S=La(w),R=(S==null?void 0:S.width)??0,A=(S==null?void 0:S.height)??0,D=r+(a!=="center"?"-"+a:""),j=typeof d=="number"?d:{top:0,right:0,bottom:0,left:0,...d},F=Array.isArray(p)?p:[p],$=F.length>0,k={padding:j,boundary:F.filter(ol),altBoundary:$},{refs:O,floatingStyles:H,placement:T,isPositioned:B,middlewareData:N}=Uc({strategy:"fixed",placement:D,whileElementsMounted:(...Fe)=>Dc(...Fe,{animationFrame:g==="always"}),elements:{reference:v.anchor},middleware:[Kc({mainAxis:o+A,alignmentAxis:i}),f&&zc({mainAxis:!0,crossAxis:!1,limiter:u==="partial"?Vc():void 0,...k}),f&&qc({...k}),Yc({...k,apply:({elements:Fe,rects:$n,availableWidth:Aa,availableHeight:_a})=>{const{width:Na,height:Pa}=$n.reference,Je=Fe.floating.style;Je.setProperty("--radix-popper-available-width",`${Aa}px`),Je.setProperty("--radix-popper-available-height",`${_a}px`),Je.setProperty("--radix-popper-anchor-width",`${Na}px`),Je.setProperty("--radix-popper-anchor-height",`${Pa}px`)}}),w&&Zc({element:w,padding:c}),al({arrowWidth:R,arrowHeight:A}),m&&Xc({strategy:"referenceHidden",...k})]}),[M,L]=ho(T),U=oe(x);be(()=>{B&&(U==null||U())},[B,U]);const J=(ie=N.arrow)==null?void 0:ie.x,Oe=(ke=N.arrow)==null?void 0:ke.y,Ie=((q=N.arrow)==null?void 0:q.centerOffset)!==0,[Qe,ge]=l.useState();return be(()=>{b&&ge(window.getComputedStyle(b).zIndex)},[b]),s.jsx("div",{ref:O.setFloating,"data-radix-popper-content-wrapper":"",style:{...H,transform:B?H.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Qe,"--radix-popper-transform-origin":[(Le=N.transformOrigin)==null?void 0:Le.x,(Ln=N.transformOrigin)==null?void 0:Ln.y].join(" "),...((Fn=N.hide)==null?void 0:Fn.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:s.jsx(tl,{scope:n,placedSide:M,onArrowChange:C,arrowX:J,arrowY:Oe,shouldHideArrow:Ie,children:s.jsx(I.div,{"data-side":M,"data-align":L,...h,ref:E,style:{...h.style,animation:B?void 0:"none"}})})})});fo.displayName=bn;var po="PopperArrow",rl={top:"bottom",right:"left",bottom:"top",left:"right"},mo=l.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,a=nl(po,r),i=rl[a.placedSide];return s.jsx("span",{ref:a.onArrowChange,style:{position:"absolute",left:a.arrowX,top:a.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[a.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[a.placedSide],visibility:a.shouldHideArrow?"hidden":void 0},children:s.jsx(Jc,{...o,ref:n,style:{...o.style,display:"block"}})})});mo.displayName=po;function ol(e){return e!==null}var al=e=>({name:"transformOrigin",options:e,fn(t){var v,b,y;const{placement:n,rects:r,middlewareData:o}=t,i=((v=o.arrow)==null?void 0:v.centerOffset)!==0,c=i?0:e.arrowWidth,f=i?0:e.arrowHeight,[p,d]=ho(n),u={start:"0%",center:"50%",end:"100%"}[d],m=(((b=o.arrow)==null?void 0:b.x)??0)+c/2,g=(((y=o.arrow)==null?void 0:y.y)??0)+f/2;let x="",h="";return p==="bottom"?(x=i?u:`${m}px`,h=`${-f}px`):p==="top"?(x=i?u:`${m}px`,h=`${r.floating.height+f}px`):p==="right"?(x=`${-f}px`,h=i?u:`${g}px`):p==="left"&&(x=`${r.floating.width+f}px`,h=i?u:`${g}px`),{data:{x,y:h}}}});function ho(e){const[t,n="center"]=e.split("-");return[t,n]}var go=co,vo=uo,xo=fo,wo=mo,sl=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),il="VisuallyHidden",bo=l.forwardRef((e,t)=>s.jsx(I.span,{...e,ref:t,style:{...sl,...e.style}}));bo.displayName=il;var cl=bo,[At,xf]=he("Tooltip",[Mt]),_t=Mt(),yo="TooltipProvider",ll=700,en="tooltip.open",[ul,yn]=At(yo),Co=e=>{const{__scopeTooltip:t,delayDuration:n=ll,skipDelayDuration:r=300,disableHoverableContent:o=!1,children:a}=e,i=l.useRef(!0),c=l.useRef(!1),f=l.useRef(0);return l.useEffect(()=>{const p=f.current;return()=>window.clearTimeout(p)},[]),s.jsx(ul,{scope:t,isOpenDelayedRef:i,delayDuration:n,onOpen:l.useCallback(()=>{window.clearTimeout(f.current),i.current=!1},[]),onClose:l.useCallback(()=>{window.clearTimeout(f.current),f.current=window.setTimeout(()=>i.current=!0,r)},[r]),isPointerInTransitRef:c,onPointerInTransitChange:l.useCallback(p=>{c.current=p},[]),disableHoverableContent:o,children:a})};Co.displayName=yo;var Ge="Tooltip",[dl,qe]=At(Ge),Eo=e=>{const{__scopeTooltip:t,children:n,open:r,defaultOpen:o,onOpenChange:a,disableHoverableContent:i,delayDuration:c}=e,f=yn(Ge,e.__scopeTooltip),p=_t(t),[d,u]=l.useState(null),m=we(),g=l.useRef(0),x=i??f.disableHoverableContent,h=c??f.delayDuration,v=l.useRef(!1),[b,y]=xt({prop:r,defaultProp:o??!1,onChange:R=>{R?(f.onOpen(),document.dispatchEvent(new CustomEvent(en))):f.onClose(),a==null||a(R)},caller:Ge}),E=l.useMemo(()=>b?v.current?"delayed-open":"instant-open":"closed",[b]),w=l.useCallback(()=>{window.clearTimeout(g.current),g.current=0,v.current=!1,y(!0)},[y]),C=l.useCallback(()=>{window.clearTimeout(g.current),g.current=0,y(!1)},[y]),S=l.useCallback(()=>{window.clearTimeout(g.current),g.current=window.setTimeout(()=>{v.current=!0,y(!0),g.current=0},h)},[h,y]);return l.useEffect(()=>()=>{g.current&&(window.clearTimeout(g.current),g.current=0)},[]),s.jsx(go,{...p,children:s.jsx(dl,{scope:t,contentId:m,open:b,stateAttribute:E,trigger:d,onTriggerChange:u,onTriggerEnter:l.useCallback(()=>{f.isOpenDelayedRef.current?S():w()},[f.isOpenDelayedRef,S,w]),onTriggerLeave:l.useCallback(()=>{x?C():(window.clearTimeout(g.current),g.current=0)},[C,x]),onOpen:w,onClose:C,disableHoverableContent:x,children:n})})};Eo.displayName=Ge;var tn="TooltipTrigger",So=l.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=qe(tn,n),a=yn(tn,n),i=_t(n),c=l.useRef(null),f=W(t,c,o.onTriggerChange),p=l.useRef(!1),d=l.useRef(!1),u=l.useCallback(()=>p.current=!1,[]);return l.useEffect(()=>()=>document.removeEventListener("pointerup",u),[u]),s.jsx(vo,{asChild:!0,...i,children:s.jsx(I.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...r,ref:f,onPointerMove:_(e.onPointerMove,m=>{m.pointerType!=="touch"&&!d.current&&!a.isPointerInTransitRef.current&&(o.onTriggerEnter(),d.current=!0)}),onPointerLeave:_(e.onPointerLeave,()=>{o.onTriggerLeave(),d.current=!1}),onPointerDown:_(e.onPointerDown,()=>{o.open&&o.onClose(),p.current=!0,document.addEventListener("pointerup",u,{once:!0})}),onFocus:_(e.onFocus,()=>{p.current||o.onOpen()}),onBlur:_(e.onBlur,o.onClose),onClick:_(e.onClick,o.onClose)})})});So.displayName=tn;var Cn="TooltipPortal",[fl,pl]=At(Cn,{forceMount:void 0}),Ro=e=>{const{__scopeTooltip:t,forceMount:n,children:r,container:o}=e,a=qe(Cn,t);return s.jsx(fl,{scope:t,forceMount:n,children:s.jsx(ue,{present:n||a.open,children:s.jsx(bt,{asChild:!0,container:o,children:r})})})};Ro.displayName=Cn;var je="TooltipContent",Mo=l.forwardRef((e,t)=>{const n=pl(je,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...a}=e,i=qe(je,e.__scopeTooltip);return s.jsx(ue,{present:r||i.open,children:i.disableHoverableContent?s.jsx(Ao,{side:o,...a,ref:t}):s.jsx(ml,{side:o,...a,ref:t})})}),ml=l.forwardRef((e,t)=>{const n=qe(je,e.__scopeTooltip),r=yn(je,e.__scopeTooltip),o=l.useRef(null),a=W(t,o),[i,c]=l.useState(null),{trigger:f,onClose:p}=n,d=o.current,{onPointerInTransitChange:u}=r,m=l.useCallback(()=>{c(null),u(!1)},[u]),g=l.useCallback((x,h)=>{const v=x.currentTarget,b={x:x.clientX,y:x.clientY},y=xl(b,v.getBoundingClientRect()),E=wl(b,y),w=bl(h.getBoundingClientRect()),C=Cl([...E,...w]);c(C),u(!0)},[u]);return l.useEffect(()=>()=>m(),[m]),l.useEffect(()=>{if(f&&d){const x=v=>g(v,d),h=v=>g(v,f);return f.addEventListener("pointerleave",x),d.addEventListener("pointerleave",h),()=>{f.removeEventListener("pointerleave",x),d.removeEventListener("pointerleave",h)}}},[f,d,g,m]),l.useEffect(()=>{if(i){const x=h=>{const v=h.target,b={x:h.clientX,y:h.clientY},y=(f==null?void 0:f.contains(v))||(d==null?void 0:d.contains(v)),E=!yl(b,i);y?m():E&&(m(),p())};return document.addEventListener("pointermove",x),()=>document.removeEventListener("pointermove",x)}},[f,d,i,p,m]),s.jsx(Ao,{...e,ref:a})}),[hl,gl]=At(Ge,{isInside:!1}),vl=Ta("TooltipContent"),Ao=l.forwardRef((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":o,onEscapeKeyDown:a,onPointerDownOutside:i,...c}=e,f=qe(je,n),p=_t(n),{onClose:d}=f;return l.useEffect(()=>(document.addEventListener(en,d),()=>document.removeEventListener(en,d)),[d]),l.useEffect(()=>{if(f.trigger){const u=m=>{const g=m.target;g!=null&&g.contains(f.trigger)&&d()};return window.addEventListener("scroll",u,{capture:!0}),()=>window.removeEventListener("scroll",u,{capture:!0})}},[f.trigger,d]),s.jsx(wt,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:a,onPointerDownOutside:i,onFocusOutside:u=>u.preventDefault(),onDismiss:d,children:s.jsxs(xo,{"data-state":f.stateAttribute,...p,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[s.jsx(vl,{children:r}),s.jsx(hl,{scope:n,isInside:!0,children:s.jsx(cl,{id:f.contentId,role:"tooltip",children:o||r})})]})})});Mo.displayName=je;var _o="TooltipArrow",No=l.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=_t(n);return gl(_o,n).isInside?null:s.jsx(wo,{...o,...r,ref:t})});No.displayName=_o;function xl(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),a=Math.abs(t.left-e.x);switch(Math.min(n,r,o,a)){case a:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function wl(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function bl(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}function yl(e,t){const{x:n,y:r}=e;let o=!1;for(let a=0,i=t.length-1;a<t.length;i=a++){const c=t[a],f=t[i],p=c.x,d=c.y,u=f.x,m=f.y;d>r!=m>r&&n<(u-p)*(r-d)/(m-d)+p&&(o=!o)}return o}function Cl(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),El(t)}function El(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const o=e[r];for(;t.length>=2;){const a=t[t.length-1],i=t[t.length-2];if((a.x-i.x)*(o.y-i.y)>=(a.y-i.y)*(o.x-i.x))t.pop();else break}t.push(o)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const o=e[r];for(;n.length>=2;){const a=n[n.length-1],i=n[n.length-2];if((a.x-i.x)*(o.y-i.y)>=(a.y-i.y)*(o.x-i.x))n.pop();else break}n.push(o)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var Sl=Co,Rl=Eo,Ml=So,Al=Ro,_l=Mo,Nl=No;function Po({delayDuration:e=0,...t}){return s.jsx(Sl,{"data-slot":"tooltip-provider",delayDuration:e,...t})}function Pl({...e}){return s.jsx(Po,{children:s.jsx(Rl,{"data-slot":"tooltip",...e})})}function jl({...e}){return s.jsx(Ml,{"data-slot":"tooltip-trigger",...e})}function Tl({className:e,sideOffset:t=4,children:n,...r}){return s.jsx(Al,{children:s.jsxs(_l,{"data-slot":"tooltip-content",sideOffset:t,className:P("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-w-sm rounded-md px-3 py-1.5 text-xs",e),...r,children:[n,s.jsx(Nl,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}const Dl="sidebar_state",Ol=3600*24*7,Il="16rem",kl="18rem",Ll="3rem",Fl="b",jo=l.createContext(null);function Nt(){const e=l.useContext(jo);if(!e)throw new Error("useSidebar must be used within a SidebarProvider.");return e}function $l({defaultOpen:e=!0,open:t,onOpenChange:n,className:r,style:o,children:a,...i}){const c=wr(),[f,p]=l.useState(!1),[d,u]=l.useState(e),m=t??d,g=l.useCallback(b=>{const y=typeof b=="function"?b(m):b;n?n(y):u(y),document.cookie=`${Dl}=${y}; path=/; max-age=${Ol}`},[n,m]),x=l.useCallback(()=>c?p(b=>!b):g(b=>!b),[c,g,p]);l.useEffect(()=>{const b=y=>{y.key===Fl&&(y.metaKey||y.ctrlKey)&&(y.preventDefault(),x())};return window.addEventListener("keydown",b),()=>window.removeEventListener("keydown",b)},[x]);const h=m?"expanded":"collapsed",v=l.useMemo(()=>({state:h,open:m,setOpen:g,isMobile:c,openMobile:f,setOpenMobile:p,toggleSidebar:x}),[h,m,g,c,f,p,x]);return s.jsx(jo.Provider,{value:v,children:s.jsx(Po,{delayDuration:0,children:s.jsx("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":Il,"--sidebar-width-icon":Ll,...o},className:P("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",r),...i,children:a})})})}function Bl({side:e="left",variant:t="sidebar",collapsible:n="offcanvas",className:r,children:o,...a}){const{isMobile:i,state:c,openMobile:f,setOpenMobile:p}=Nt();return n==="none"?s.jsx("div",{"data-slot":"sidebar",className:P("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",r),...a,children:o}):i?s.jsxs(Ii,{open:f,onOpenChange:p,...a,children:[s.jsxs($i,{className:"sr-only",children:[s.jsx(Bi,{children:"Sidebar"}),s.jsx(Wi,{children:"Displays the mobile sidebar."})]}),s.jsx(Fi,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":kl},side:e,children:s.jsx("div",{className:"flex h-full w-full flex-col",children:o})})]}):s.jsxs("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":c,"data-collapsible":c==="collapsed"?n:"","data-variant":t,"data-side":e,"data-slot":"sidebar",children:[s.jsx("div",{className:P("relative h-svh w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180",t==="floating"||t==="inset"?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),s.jsx("div",{className:P("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex",e==="left"?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]",t==="floating"||t==="inset"?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",r),...a,children:s.jsx("div",{"data-sidebar":"sidebar",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:o})})]})}function Wl({className:e,onClick:t,...n}){const{toggleSidebar:r}=Nt();return s.jsxs(Be,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:P("h-7 w-7",e),onClick:o=>{t==null||t(o),r()},...n,children:[s.jsx(ls,{}),s.jsx("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function Hl({className:e,...t}){return s.jsx("main",{"data-slot":"sidebar-inset",className:P("bg-background relative flex min-h-svh flex-1 flex-col","peer-data-[variant=inset]:min-h-[calc(100svh-(--spacing(4)))] md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-0",e),...t})}function Ul({className:e,...t}){return s.jsx("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:P("flex flex-col gap-2 p-2",e),...t})}function Gl({className:e,...t}){return s.jsx("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",className:P("flex flex-col gap-2 p-2",e),...t})}function Kl({className:e,...t}){return s.jsx("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:P("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...t})}function zl({className:e,...t}){return s.jsx("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:P("relative flex w-full min-w-0 flex-col p-2",e),...t})}function Vl({className:e,asChild:t=!1,...n}){const r=t?vt:"div";return s.jsx(r,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:P("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",e),...n})}function En({className:e,...t}){return s.jsx("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:P("flex w-full min-w-0 flex-col gap-1",e),...t})}function Sn({className:e,...t}){return s.jsx("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:P("group/menu-item relative",e),...t})}const ql=mr("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function Rn({asChild:e=!1,isActive:t=!1,variant:n="default",size:r="default",tooltip:o,className:a,...i}){const c=e?vt:"button",{isMobile:f,state:p}=Nt(),d=s.jsx(c,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":r,"data-active":t,className:P(ql({variant:n,size:r}),a),...i});return o?(typeof o=="string"&&(o={children:o}),s.jsxs(Pl,{children:[s.jsx(jl,{asChild:!0,children:d}),s.jsx(Tl,{side:"right",align:"center",hidden:p!=="collapsed"||f,...o})]})):d}function Yl({variant:e="header",children:t,...n}){return e==="sidebar"?s.jsx(Hl,{...n,children:t}):s.jsx("main",{className:"mx-auto flex h-full w-full max-w-7xl flex-1 flex-col gap-4 rounded-xl",...n,children:t})}function Xl({children:e,variant:t="header"}){const[n,r]=l.useState(()=>typeof window<"u"?localStorage.getItem("sidebar")!=="false":!0),o=a=>{r(a),typeof window<"u"&&localStorage.setItem("sidebar",String(a))};return t==="header"?s.jsx("div",{className:"flex min-h-screen w-full flex-col",children:e}):s.jsx($l,{defaultOpen:n,open:n,onOpenChange:o,children:e})}function Zl({items:e=[]}){const t=xe();return s.jsxs(zl,{className:"px-2 py-0",children:[s.jsx(Vl,{}),s.jsx(En,{children:e.map(n=>s.jsx(Sn,{children:s.jsx(Rn,{asChild:!0,isActive:t.url.startsWith(n.url),children:s.jsxs(fe,{href:n.url,prefetch:!0,children:[n.icon&&s.jsx(n.icon,{}),s.jsx("span",{children:n.title})]})})},n.title))})]})}function To(e){const t=e+"CollectionProvider",[n,r]=he(t),[o,a]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=h=>{const{scope:v,children:b}=h,y=ve.useRef(null),E=ve.useRef(new Map).current;return s.jsx(o,{scope:v,itemMap:E,collectionRef:y,children:b})};i.displayName=t;const c=e+"CollectionSlot",f=lt(c),p=ve.forwardRef((h,v)=>{const{scope:b,children:y}=h,E=a(c,b),w=W(v,E.collectionRef);return s.jsx(f,{ref:w,children:y})});p.displayName=c;const d=e+"CollectionItemSlot",u="data-radix-collection-item",m=lt(d),g=ve.forwardRef((h,v)=>{const{scope:b,children:y,...E}=h,w=ve.useRef(null),C=W(v,w),S=a(d,b);return ve.useEffect(()=>(S.itemMap.set(w,{ref:w,...E}),()=>void S.itemMap.delete(w))),s.jsx(m,{[u]:"",ref:C,children:y})});g.displayName=d;function x(h){const v=a(e+"CollectionConsumer",h);return ve.useCallback(()=>{const y=v.collectionRef.current;if(!y)return[];const E=Array.from(y.querySelectorAll(`[${u}]`));return Array.from(v.itemMap.values()).sort((S,R)=>E.indexOf(S.ref.current)-E.indexOf(R.ref.current))},[v.collectionRef,v.itemMap])}return[{Provider:i,Slot:p,ItemSlot:g},x,r]}var Ql=l.createContext(void 0);function Do(e){const t=l.useContext(Ql);return e||t||"ltr"}var Gt="rovingFocusGroup.onEntryFocus",Jl={bubbles:!1,cancelable:!0},Ye="RovingFocusGroup",[nn,Oo,eu]=To(Ye),[tu,Io]=he(Ye,[eu]),[nu,ru]=tu(Ye),ko=l.forwardRef((e,t)=>s.jsx(nn.Provider,{scope:e.__scopeRovingFocusGroup,children:s.jsx(nn.Slot,{scope:e.__scopeRovingFocusGroup,children:s.jsx(ou,{...e,ref:t})})}));ko.displayName=Ye;var ou=l.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:r,loop:o=!1,dir:a,currentTabStopId:i,defaultCurrentTabStopId:c,onCurrentTabStopIdChange:f,onEntryFocus:p,preventScrollOnEntryFocus:d=!1,...u}=e,m=l.useRef(null),g=W(t,m),x=Do(a),[h,v]=xt({prop:i,defaultProp:c??null,onChange:f,caller:Ye}),[b,y]=l.useState(!1),E=oe(p),w=Oo(n),C=l.useRef(!1),[S,R]=l.useState(0);return l.useEffect(()=>{const A=m.current;if(A)return A.addEventListener(Gt,E),()=>A.removeEventListener(Gt,E)},[E]),s.jsx(nu,{scope:n,orientation:r,dir:x,loop:o,currentTabStopId:h,onItemFocus:l.useCallback(A=>v(A),[v]),onItemShiftTab:l.useCallback(()=>y(!0),[]),onFocusableItemAdd:l.useCallback(()=>R(A=>A+1),[]),onFocusableItemRemove:l.useCallback(()=>R(A=>A-1),[]),children:s.jsx(I.div,{tabIndex:b||S===0?-1:0,"data-orientation":r,...u,ref:g,style:{outline:"none",...e.style},onMouseDown:_(e.onMouseDown,()=>{C.current=!0}),onFocus:_(e.onFocus,A=>{const D=!C.current;if(A.target===A.currentTarget&&D&&!b){const j=new CustomEvent(Gt,Jl);if(A.currentTarget.dispatchEvent(j),!j.defaultPrevented){const F=w().filter(T=>T.focusable),$=F.find(T=>T.active),k=F.find(T=>T.id===h),H=[$,k,...F].filter(Boolean).map(T=>T.ref.current);$o(H,d)}}C.current=!1}),onBlur:_(e.onBlur,()=>y(!1))})})}),Lo="RovingFocusGroupItem",Fo=l.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:r=!0,active:o=!1,tabStopId:a,children:i,...c}=e,f=we(),p=a||f,d=ru(Lo,n),u=d.currentTabStopId===p,m=Oo(n),{onFocusableItemAdd:g,onFocusableItemRemove:x,currentTabStopId:h}=d;return l.useEffect(()=>{if(r)return g(),()=>x()},[r,g,x]),s.jsx(nn.ItemSlot,{scope:n,id:p,focusable:r,active:o,children:s.jsx(I.span,{tabIndex:u?0:-1,"data-orientation":d.orientation,...c,ref:t,onMouseDown:_(e.onMouseDown,v=>{r?d.onItemFocus(p):v.preventDefault()}),onFocus:_(e.onFocus,()=>d.onItemFocus(p)),onKeyDown:_(e.onKeyDown,v=>{if(v.key==="Tab"&&v.shiftKey){d.onItemShiftTab();return}if(v.target!==v.currentTarget)return;const b=iu(v,d.orientation,d.dir);if(b!==void 0){if(v.metaKey||v.ctrlKey||v.altKey||v.shiftKey)return;v.preventDefault();let E=m().filter(w=>w.focusable).map(w=>w.ref.current);if(b==="last")E.reverse();else if(b==="prev"||b==="next"){b==="prev"&&E.reverse();const w=E.indexOf(v.currentTarget);E=d.loop?cu(E,w+1):E.slice(w+1)}setTimeout(()=>$o(E))}}),children:typeof i=="function"?i({isCurrentTabStop:u,hasTabStop:h!=null}):i})})});Fo.displayName=Lo;var au={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function su(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function iu(e,t,n){const r=su(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(r))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(r)))return au[r]}function $o(e,t=!1){const n=document.activeElement;for(const r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}function cu(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var lu=ko,uu=Fo,rn=["Enter"," "],du=["ArrowDown","PageUp","Home"],Bo=["ArrowUp","PageDown","End"],fu=[...du,...Bo],pu={ltr:[...rn,"ArrowRight"],rtl:[...rn,"ArrowLeft"]},mu={ltr:["ArrowLeft"],rtl:["ArrowRight"]},Xe="Menu",[Ke,hu,gu]=To(Xe),[Ee,Wo]=he(Xe,[gu,Mt,Io]),Pt=Mt(),Ho=Io(),[vu,Se]=Ee(Xe),[xu,Ze]=Ee(Xe),Uo=e=>{const{__scopeMenu:t,open:n=!1,children:r,dir:o,onOpenChange:a,modal:i=!0}=e,c=Pt(t),[f,p]=l.useState(null),d=l.useRef(!1),u=oe(a),m=Do(o);return l.useEffect(()=>{const g=()=>{d.current=!0,document.addEventListener("pointerdown",x,{capture:!0,once:!0}),document.addEventListener("pointermove",x,{capture:!0,once:!0})},x=()=>d.current=!1;return document.addEventListener("keydown",g,{capture:!0}),()=>{document.removeEventListener("keydown",g,{capture:!0}),document.removeEventListener("pointerdown",x,{capture:!0}),document.removeEventListener("pointermove",x,{capture:!0})}},[]),s.jsx(go,{...c,children:s.jsx(vu,{scope:t,open:n,onOpenChange:u,content:f,onContentChange:p,children:s.jsx(xu,{scope:t,onClose:l.useCallback(()=>u(!1),[u]),isUsingKeyboardRef:d,dir:m,modal:i,children:r})})})};Uo.displayName=Xe;var wu="MenuAnchor",Mn=l.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,o=Pt(n);return s.jsx(vo,{...o,...r,ref:t})});Mn.displayName=wu;var An="MenuPortal",[bu,Go]=Ee(An,{forceMount:void 0}),Ko=e=>{const{__scopeMenu:t,forceMount:n,children:r,container:o}=e,a=Se(An,t);return s.jsx(bu,{scope:t,forceMount:n,children:s.jsx(ue,{present:n||a.open,children:s.jsx(bt,{asChild:!0,container:o,children:r})})})};Ko.displayName=An;var Y="MenuContent",[yu,_n]=Ee(Y),zo=l.forwardRef((e,t)=>{const n=Go(Y,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,a=Se(Y,e.__scopeMenu),i=Ze(Y,e.__scopeMenu);return s.jsx(Ke.Provider,{scope:e.__scopeMenu,children:s.jsx(ue,{present:r||a.open,children:s.jsx(Ke.Slot,{scope:e.__scopeMenu,children:i.modal?s.jsx(Cu,{...o,ref:t}):s.jsx(Eu,{...o,ref:t})})})})}),Cu=l.forwardRef((e,t)=>{const n=Se(Y,e.__scopeMenu),r=l.useRef(null),o=W(t,r);return l.useEffect(()=>{const a=r.current;if(a)return Tr(a)},[]),s.jsx(Nn,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:_(e.onFocusOutside,a=>a.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),Eu=l.forwardRef((e,t)=>{const n=Se(Y,e.__scopeMenu);return s.jsx(Nn,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),Su=lt("MenuContent.ScrollLock"),Nn=l.forwardRef((e,t)=>{const{__scopeMenu:n,loop:r=!1,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:i,disableOutsidePointerEvents:c,onEntryFocus:f,onEscapeKeyDown:p,onPointerDownOutside:d,onFocusOutside:u,onInteractOutside:m,onDismiss:g,disableOutsideScroll:x,...h}=e,v=Se(Y,n),b=Ze(Y,n),y=Pt(n),E=Ho(n),w=hu(n),[C,S]=l.useState(null),R=l.useRef(null),A=W(t,R,v.onContentChange),D=l.useRef(0),j=l.useRef(""),F=l.useRef(0),$=l.useRef(null),k=l.useRef("right"),O=l.useRef(0),H=x?cn:l.Fragment,T=x?{as:Su,allowPinchZoom:!0}:void 0,B=M=>{var ie,ke;const L=j.current+M,U=w().filter(q=>!q.disabled),J=document.activeElement,Oe=(ie=U.find(q=>q.ref.current===J))==null?void 0:ie.textValue,Ie=U.map(q=>q.textValue),Qe=ku(Ie,L,Oe),ge=(ke=U.find(q=>q.textValue===Qe))==null?void 0:ke.ref.current;(function q(Le){j.current=Le,window.clearTimeout(D.current),Le!==""&&(D.current=window.setTimeout(()=>q(""),1e3))})(L),ge&&setTimeout(()=>ge.focus())};l.useEffect(()=>()=>window.clearTimeout(D.current),[]),Er();const N=l.useCallback(M=>{var U,J;return k.current===((U=$.current)==null?void 0:U.side)&&Fu(M,(J=$.current)==null?void 0:J.area)},[]);return s.jsx(yu,{scope:n,searchRef:j,onItemEnter:l.useCallback(M=>{N(M)&&M.preventDefault()},[N]),onItemLeave:l.useCallback(M=>{var L;N(M)||((L=R.current)==null||L.focus(),S(null))},[N]),onTriggerLeave:l.useCallback(M=>{N(M)&&M.preventDefault()},[N]),pointerGraceTimerRef:F,onPointerGraceIntentChange:l.useCallback(M=>{$.current=M},[]),children:s.jsx(H,{...T,children:s.jsx(sn,{asChild:!0,trapped:o,onMountAutoFocus:_(a,M=>{var L;M.preventDefault(),(L=R.current)==null||L.focus({preventScroll:!0})}),onUnmountAutoFocus:i,children:s.jsx(wt,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:p,onPointerDownOutside:d,onFocusOutside:u,onInteractOutside:m,onDismiss:g,children:s.jsx(lu,{asChild:!0,...E,dir:b.dir,orientation:"vertical",loop:r,currentTabStopId:C,onCurrentTabStopIdChange:S,onEntryFocus:_(f,M=>{b.isUsingKeyboardRef.current||M.preventDefault()}),preventScrollOnEntryFocus:!0,children:s.jsx(xo,{role:"menu","aria-orientation":"vertical","data-state":ca(v.open),"data-radix-menu-content":"",dir:b.dir,...y,...h,ref:A,style:{outline:"none",...h.style},onKeyDown:_(h.onKeyDown,M=>{const U=M.target.closest("[data-radix-menu-content]")===M.currentTarget,J=M.ctrlKey||M.altKey||M.metaKey,Oe=M.key.length===1;U&&(M.key==="Tab"&&M.preventDefault(),!J&&Oe&&B(M.key));const Ie=R.current;if(M.target!==Ie||!fu.includes(M.key))return;M.preventDefault();const ge=w().filter(ie=>!ie.disabled).map(ie=>ie.ref.current);Bo.includes(M.key)&&ge.reverse(),Ou(ge)}),onBlur:_(e.onBlur,M=>{M.currentTarget.contains(M.target)||(window.clearTimeout(D.current),j.current="")}),onPointerMove:_(e.onPointerMove,ze(M=>{const L=M.target,U=O.current!==M.clientX;if(M.currentTarget.contains(L)&&U){const J=M.clientX>O.current?"right":"left";k.current=J,O.current=M.clientX}}))})})})})})})});zo.displayName=Y;var Ru="MenuGroup",Pn=l.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return s.jsx(I.div,{role:"group",...r,ref:t})});Pn.displayName=Ru;var Mu="MenuLabel",Vo=l.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return s.jsx(I.div,{...r,ref:t})});Vo.displayName=Mu;var ht="MenuItem",lr="menu.itemSelect",jt=l.forwardRef((e,t)=>{const{disabled:n=!1,onSelect:r,...o}=e,a=l.useRef(null),i=Ze(ht,e.__scopeMenu),c=_n(ht,e.__scopeMenu),f=W(t,a),p=l.useRef(!1),d=()=>{const u=a.current;if(!n&&u){const m=new CustomEvent(lr,{bubbles:!0,cancelable:!0});u.addEventListener(lr,g=>r==null?void 0:r(g),{once:!0}),gr(u,m),m.defaultPrevented?p.current=!1:i.onClose()}};return s.jsx(qo,{...o,ref:f,disabled:n,onClick:_(e.onClick,d),onPointerDown:u=>{var m;(m=e.onPointerDown)==null||m.call(e,u),p.current=!0},onPointerUp:_(e.onPointerUp,u=>{var m;p.current||(m=u.currentTarget)==null||m.click()}),onKeyDown:_(e.onKeyDown,u=>{const m=c.searchRef.current!=="";n||m&&u.key===" "||rn.includes(u.key)&&(u.currentTarget.click(),u.preventDefault())})})});jt.displayName=ht;var qo=l.forwardRef((e,t)=>{const{__scopeMenu:n,disabled:r=!1,textValue:o,...a}=e,i=_n(ht,n),c=Ho(n),f=l.useRef(null),p=W(t,f),[d,u]=l.useState(!1),[m,g]=l.useState("");return l.useEffect(()=>{const x=f.current;x&&g((x.textContent??"").trim())},[a.children]),s.jsx(Ke.ItemSlot,{scope:n,disabled:r,textValue:o??m,children:s.jsx(uu,{asChild:!0,...c,focusable:!r,children:s.jsx(I.div,{role:"menuitem","data-highlighted":d?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0,...a,ref:p,onPointerMove:_(e.onPointerMove,ze(x=>{r?i.onItemLeave(x):(i.onItemEnter(x),x.defaultPrevented||x.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:_(e.onPointerLeave,ze(x=>i.onItemLeave(x))),onFocus:_(e.onFocus,()=>u(!0)),onBlur:_(e.onBlur,()=>u(!1))})})})}),Au="MenuCheckboxItem",Yo=l.forwardRef((e,t)=>{const{checked:n=!1,onCheckedChange:r,...o}=e;return s.jsx(ea,{scope:e.__scopeMenu,checked:n,children:s.jsx(jt,{role:"menuitemcheckbox","aria-checked":gt(n)?"mixed":n,...o,ref:t,"data-state":Tn(n),onSelect:_(o.onSelect,()=>r==null?void 0:r(gt(n)?!0:!n),{checkForDefaultPrevented:!1})})})});Yo.displayName=Au;var Xo="MenuRadioGroup",[_u,Nu]=Ee(Xo,{value:void 0,onValueChange:()=>{}}),Zo=l.forwardRef((e,t)=>{const{value:n,onValueChange:r,...o}=e,a=oe(r);return s.jsx(_u,{scope:e.__scopeMenu,value:n,onValueChange:a,children:s.jsx(Pn,{...o,ref:t})})});Zo.displayName=Xo;var Qo="MenuRadioItem",Jo=l.forwardRef((e,t)=>{const{value:n,...r}=e,o=Nu(Qo,e.__scopeMenu),a=n===o.value;return s.jsx(ea,{scope:e.__scopeMenu,checked:a,children:s.jsx(jt,{role:"menuitemradio","aria-checked":a,...r,ref:t,"data-state":Tn(a),onSelect:_(r.onSelect,()=>{var i;return(i=o.onValueChange)==null?void 0:i.call(o,n)},{checkForDefaultPrevented:!1})})})});Jo.displayName=Qo;var jn="MenuItemIndicator",[ea,Pu]=Ee(jn,{checked:!1}),ta=l.forwardRef((e,t)=>{const{__scopeMenu:n,forceMount:r,...o}=e,a=Pu(jn,n);return s.jsx(ue,{present:r||gt(a.checked)||a.checked===!0,children:s.jsx(I.span,{...o,ref:t,"data-state":Tn(a.checked)})})});ta.displayName=jn;var ju="MenuSeparator",na=l.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return s.jsx(I.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});na.displayName=ju;var Tu="MenuArrow",ra=l.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,o=Pt(n);return s.jsx(wo,{...o,...r,ref:t})});ra.displayName=Tu;var Du="MenuSub",[wf,oa]=Ee(Du),We="MenuSubTrigger",aa=l.forwardRef((e,t)=>{const n=Se(We,e.__scopeMenu),r=Ze(We,e.__scopeMenu),o=oa(We,e.__scopeMenu),a=_n(We,e.__scopeMenu),i=l.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:f}=a,p={__scopeMenu:e.__scopeMenu},d=l.useCallback(()=>{i.current&&window.clearTimeout(i.current),i.current=null},[]);return l.useEffect(()=>d,[d]),l.useEffect(()=>{const u=c.current;return()=>{window.clearTimeout(u),f(null)}},[c,f]),s.jsx(Mn,{asChild:!0,...p,children:s.jsx(qo,{id:o.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":o.contentId,"data-state":ca(n.open),...e,ref:hr(t,o.onTriggerChange),onClick:u=>{var m;(m=e.onClick)==null||m.call(e,u),!(e.disabled||u.defaultPrevented)&&(u.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:_(e.onPointerMove,ze(u=>{a.onItemEnter(u),!u.defaultPrevented&&!e.disabled&&!n.open&&!i.current&&(a.onPointerGraceIntentChange(null),i.current=window.setTimeout(()=>{n.onOpenChange(!0),d()},100))})),onPointerLeave:_(e.onPointerLeave,ze(u=>{var g,x;d();const m=(g=n.content)==null?void 0:g.getBoundingClientRect();if(m){const h=(x=n.content)==null?void 0:x.dataset.side,v=h==="right",b=v?-5:5,y=m[v?"left":"right"],E=m[v?"right":"left"];a.onPointerGraceIntentChange({area:[{x:u.clientX+b,y:u.clientY},{x:y,y:m.top},{x:E,y:m.top},{x:E,y:m.bottom},{x:y,y:m.bottom}],side:h}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>a.onPointerGraceIntentChange(null),300)}else{if(a.onTriggerLeave(u),u.defaultPrevented)return;a.onPointerGraceIntentChange(null)}})),onKeyDown:_(e.onKeyDown,u=>{var g;const m=a.searchRef.current!=="";e.disabled||m&&u.key===" "||pu[r.dir].includes(u.key)&&(n.onOpenChange(!0),(g=n.content)==null||g.focus(),u.preventDefault())})})})});aa.displayName=We;var sa="MenuSubContent",ia=l.forwardRef((e,t)=>{const n=Go(Y,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,a=Se(Y,e.__scopeMenu),i=Ze(Y,e.__scopeMenu),c=oa(sa,e.__scopeMenu),f=l.useRef(null),p=W(t,f);return s.jsx(Ke.Provider,{scope:e.__scopeMenu,children:s.jsx(ue,{present:r||a.open,children:s.jsx(Ke.Slot,{scope:e.__scopeMenu,children:s.jsx(Nn,{id:c.contentId,"aria-labelledby":c.triggerId,...o,ref:p,align:"start",side:i.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:d=>{var u;i.isUsingKeyboardRef.current&&((u=f.current)==null||u.focus()),d.preventDefault()},onCloseAutoFocus:d=>d.preventDefault(),onFocusOutside:_(e.onFocusOutside,d=>{d.target!==c.trigger&&a.onOpenChange(!1)}),onEscapeKeyDown:_(e.onEscapeKeyDown,d=>{i.onClose(),d.preventDefault()}),onKeyDown:_(e.onKeyDown,d=>{var g;const u=d.currentTarget.contains(d.target),m=mu[i.dir].includes(d.key);u&&m&&(a.onOpenChange(!1),(g=c.trigger)==null||g.focus(),d.preventDefault())})})})})})});ia.displayName=sa;function ca(e){return e?"open":"closed"}function gt(e){return e==="indeterminate"}function Tn(e){return gt(e)?"indeterminate":e?"checked":"unchecked"}function Ou(e){const t=document.activeElement;for(const n of e)if(n===t||(n.focus(),document.activeElement!==t))return}function Iu(e,t){return e.map((n,r)=>e[(t+r)%e.length])}function ku(e,t,n){const o=t.length>1&&Array.from(t).every(p=>p===t[0])?t[0]:t,a=n?e.indexOf(n):-1;let i=Iu(e,Math.max(a,0));o.length===1&&(i=i.filter(p=>p!==n));const f=i.find(p=>p.toLowerCase().startsWith(o.toLowerCase()));return f!==n?f:void 0}function Lu(e,t){const{x:n,y:r}=e;let o=!1;for(let a=0,i=t.length-1;a<t.length;i=a++){const c=t[a],f=t[i],p=c.x,d=c.y,u=f.x,m=f.y;d>r!=m>r&&n<(u-p)*(r-d)/(m-d)+p&&(o=!o)}return o}function Fu(e,t){if(!t)return!1;const n={x:e.clientX,y:e.clientY};return Lu(n,t)}function ze(e){return t=>t.pointerType==="mouse"?e(t):void 0}var $u=Uo,Bu=Mn,Wu=Ko,Hu=zo,Uu=Pn,Gu=Vo,Ku=jt,zu=Yo,Vu=Zo,qu=Jo,Yu=ta,Xu=na,Zu=ra,Qu=aa,Ju=ia,Tt="DropdownMenu",[ed,bf]=he(Tt,[Wo]),G=Wo(),[td,la]=ed(Tt),ua=e=>{const{__scopeDropdownMenu:t,children:n,dir:r,open:o,defaultOpen:a,onOpenChange:i,modal:c=!0}=e,f=G(t),p=l.useRef(null),[d,u]=xt({prop:o,defaultProp:a??!1,onChange:i,caller:Tt});return s.jsx(td,{scope:t,triggerId:we(),triggerRef:p,contentId:we(),open:d,onOpenChange:u,onOpenToggle:l.useCallback(()=>u(m=>!m),[u]),modal:c,children:s.jsx($u,{...f,open:d,onOpenChange:u,dir:r,modal:c,children:n})})};ua.displayName=Tt;var da="DropdownMenuTrigger",fa=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,disabled:r=!1,...o}=e,a=la(da,n),i=G(n);return s.jsx(Bu,{asChild:!0,...i,children:s.jsx(I.button,{type:"button",id:a.triggerId,"aria-haspopup":"menu","aria-expanded":a.open,"aria-controls":a.open?a.contentId:void 0,"data-state":a.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...o,ref:hr(t,a.triggerRef),onPointerDown:_(e.onPointerDown,c=>{!r&&c.button===0&&c.ctrlKey===!1&&(a.onOpenToggle(),a.open||c.preventDefault())}),onKeyDown:_(e.onKeyDown,c=>{r||(["Enter"," "].includes(c.key)&&a.onOpenToggle(),c.key==="ArrowDown"&&a.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(c.key)&&c.preventDefault())})})})});fa.displayName=da;var nd="DropdownMenuPortal",pa=e=>{const{__scopeDropdownMenu:t,...n}=e,r=G(t);return s.jsx(Wu,{...r,...n})};pa.displayName=nd;var ma="DropdownMenuContent",ha=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=la(ma,n),a=G(n),i=l.useRef(!1);return s.jsx(Hu,{id:o.contentId,"aria-labelledby":o.triggerId,...a,...r,ref:t,onCloseAutoFocus:_(e.onCloseAutoFocus,c=>{var f;i.current||(f=o.triggerRef.current)==null||f.focus(),i.current=!1,c.preventDefault()}),onInteractOutside:_(e.onInteractOutside,c=>{const f=c.detail.originalEvent,p=f.button===0&&f.ctrlKey===!0,d=f.button===2||p;(!o.modal||d)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});ha.displayName=ma;var rd="DropdownMenuGroup",ga=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=G(n);return s.jsx(Uu,{...o,...r,ref:t})});ga.displayName=rd;var od="DropdownMenuLabel",va=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=G(n);return s.jsx(Gu,{...o,...r,ref:t})});va.displayName=od;var ad="DropdownMenuItem",xa=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=G(n);return s.jsx(Ku,{...o,...r,ref:t})});xa.displayName=ad;var sd="DropdownMenuCheckboxItem",id=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=G(n);return s.jsx(zu,{...o,...r,ref:t})});id.displayName=sd;var cd="DropdownMenuRadioGroup",ld=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=G(n);return s.jsx(Vu,{...o,...r,ref:t})});ld.displayName=cd;var ud="DropdownMenuRadioItem",dd=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=G(n);return s.jsx(qu,{...o,...r,ref:t})});dd.displayName=ud;var fd="DropdownMenuItemIndicator",pd=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=G(n);return s.jsx(Yu,{...o,...r,ref:t})});pd.displayName=fd;var md="DropdownMenuSeparator",wa=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=G(n);return s.jsx(Xu,{...o,...r,ref:t})});wa.displayName=md;var hd="DropdownMenuArrow",gd=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=G(n);return s.jsx(Zu,{...o,...r,ref:t})});gd.displayName=hd;var vd="DropdownMenuSubTrigger",xd=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=G(n);return s.jsx(Qu,{...o,...r,ref:t})});xd.displayName=vd;var wd="DropdownMenuSubContent",bd=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=G(n);return s.jsx(Ju,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});bd.displayName=wd;var yd=ua,Cd=fa,Ed=pa,Sd=ha,Rd=ga,Md=va,Ad=xa,_d=wa;function Dn({...e}){return s.jsx(yd,{"data-slot":"dropdown-menu",...e})}function On({...e}){return s.jsx(Cd,{"data-slot":"dropdown-menu-trigger",...e})}function In({className:e,sideOffset:t=4,...n}){return s.jsx(Ed,{children:s.jsx(Sd,{"data-slot":"dropdown-menu-content",sideOffset:t,className:P("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] overflow-hidden rounded-md border p-1 shadow-md",e),...n})})}function Nd({...e}){return s.jsx(Rd,{"data-slot":"dropdown-menu-group",...e})}function on({className:e,inset:t,variant:n="default",...r}){return s.jsx(Ad,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":n,className:P("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive-foreground data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/40 data-[variant=destructive]:focus:text-destructive-foreground data-[variant=destructive]:*:[svg]:!text-destructive-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...r})}function Pd({className:e,inset:t,...n}){return s.jsx(Md,{"data-slot":"dropdown-menu-label","data-inset":t,className:P("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",e),...n})}function an({className:e,...t}){return s.jsx(_d,{"data-slot":"dropdown-menu-separator",className:P("bg-border -mx-1 my-1 h-px",e),...t})}var Kt={exports:{}},zt={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ur;function jd(){if(ur)return zt;ur=1;var e=Da();function t(u,m){return u===m&&(u!==0||1/u===1/m)||u!==u&&m!==m}var n=typeof Object.is=="function"?Object.is:t,r=e.useState,o=e.useEffect,a=e.useLayoutEffect,i=e.useDebugValue;function c(u,m){var g=m(),x=r({inst:{value:g,getSnapshot:m}}),h=x[0].inst,v=x[1];return a(function(){h.value=g,h.getSnapshot=m,f(h)&&v({inst:h})},[u,g,m]),o(function(){return f(h)&&v({inst:h}),u(function(){f(h)&&v({inst:h})})},[u]),i(g),g}function f(u){var m=u.getSnapshot;u=u.value;try{var g=m();return!n(u,g)}catch{return!0}}function p(u,m){return m()}var d=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?p:c;return zt.useSyncExternalStore=e.useSyncExternalStore!==void 0?e.useSyncExternalStore:d,zt}var dr;function Td(){return dr||(dr=1,Kt.exports=jd()),Kt.exports}var Dd=Td();function Od(){return Dd.useSyncExternalStore(Id,()=>!0,()=>!1)}function Id(){return()=>{}}var kn="Avatar",[kd,yf]=he(kn),[Ld,ba]=kd(kn),ya=l.forwardRef((e,t)=>{const{__scopeAvatar:n,...r}=e,[o,a]=l.useState("idle");return s.jsx(Ld,{scope:n,imageLoadingStatus:o,onImageLoadingStatusChange:a,children:s.jsx(I.span,{...r,ref:t})})});ya.displayName=kn;var Ca="AvatarImage",Ea=l.forwardRef((e,t)=>{const{__scopeAvatar:n,src:r,onLoadingStatusChange:o=()=>{},...a}=e,i=ba(Ca,n),c=Fd(r,a),f=oe(p=>{o(p),i.onImageLoadingStatusChange(p)});return be(()=>{c!=="idle"&&f(c)},[c,f]),c==="loaded"?s.jsx(I.img,{...a,ref:t,src:r}):null});Ea.displayName=Ca;var Sa="AvatarFallback",Ra=l.forwardRef((e,t)=>{const{__scopeAvatar:n,delayMs:r,...o}=e,a=ba(Sa,n),[i,c]=l.useState(r===void 0);return l.useEffect(()=>{if(r!==void 0){const f=window.setTimeout(()=>c(!0),r);return()=>window.clearTimeout(f)}},[r]),i&&a.imageLoadingStatus!=="loaded"?s.jsx(I.span,{...o,ref:t}):null});Ra.displayName=Sa;function fr(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}function Fd(e,{referrerPolicy:t,crossOrigin:n}){const r=Od(),o=l.useRef(null),a=r?(o.current||(o.current=new window.Image),o.current):null,[i,c]=l.useState(()=>fr(a,e));return be(()=>{c(fr(a,e))},[a,e]),be(()=>{const f=u=>()=>{c(u)};if(!a)return;const p=f("loaded"),d=f("error");return a.addEventListener("load",p),a.addEventListener("error",d),t&&(a.referrerPolicy=t),typeof n=="string"&&(a.crossOrigin=n),()=>{a.removeEventListener("load",p),a.removeEventListener("error",d)}},[a,n,t]),i}var $d=ya,Bd=Ea,Wd=Ra;function Hd({className:e,...t}){return s.jsx($d,{"data-slot":"avatar",className:P("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function Ud({className:e,...t}){return s.jsx(Bd,{"data-slot":"avatar-image",className:P("aspect-square size-full",e),...t})}function Gd({className:e,...t}){return s.jsx(Wd,{"data-slot":"avatar-fallback",className:P("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}function Cf({text:e,className:t}){const n=e.split(" ").map(r=>r[0]).join("").toUpperCase();return s.jsx("span",{className:P("font-bold text-lg",t),children:n})}function Kd(){return l.useCallback(t=>{const n=t.trim().split(" ");if(n.length===0)return"";if(n.length===1)return n[0].charAt(0).toUpperCase();const r=n[0].charAt(0),o=n[n.length-1].charAt(0);return`${r}${o}`.toUpperCase()},[])}function Ma({user:e,showEmail:t=!1}){const n=Kd(),r=e.first_name+" "+e.last_name;return s.jsxs(s.Fragment,{children:[s.jsxs(Hd,{className:"h-8 w-8 overflow-hidden rounded-full",children:[s.jsx(Ud,{src:e.avatar,alt:r}),s.jsx(Gd,{className:"rounded-lg bg-neutral-200 text-black dark:bg-neutral-700 dark:text-white",children:n(r)})]}),s.jsxs("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[s.jsx("span",{className:"truncate font-medium",children:r}),t&&s.jsx("span",{className:"text-muted-foreground truncate text-xs",children:e.email})]})]})}function zd(){return l.useCallback(()=>{document.body.style.removeProperty("pointer-events")},[])}function Vd({user:e}){const t=zd();return s.jsxs(s.Fragment,{children:[s.jsx(Pd,{className:"p-0 font-normal",children:s.jsx("div",{className:"flex items-center gap-2 px-1 py-1.5 text-left text-sm",children:s.jsx(Ma,{user:e,showEmail:!0})})}),s.jsx(an,{}),s.jsx(Nd,{children:s.jsx(on,{asChild:!0,children:s.jsxs(fe,{className:"block w-full",href:route("password.edit"),as:"button",prefetch:!0,onClick:t,children:[s.jsx(rs,{className:"mr-2"}),"Change Password"]})})}),s.jsx(an,{}),s.jsx(on,{asChild:!0,children:s.jsxs(fe,{className:"block w-full",method:"post",href:route("logout"),as:"button",onClick:t,children:[s.jsx(as,{className:"mr-2"}),"Log out"]})})]})}function qd(){const{auth:e}=xe().props,{state:t}=Nt(),n=wr();return s.jsx(En,{children:s.jsx(Sn,{children:s.jsxs(Dn,{children:[s.jsx(On,{asChild:!0,children:s.jsxs(Rn,{size:"lg",className:"text-sidebar-accent-foreground data-[state=open]:bg-sidebar-accent group",children:[s.jsx(Ma,{user:e.user}),s.jsx(Za,{className:"ml-auto size-4"})]})}),s.jsx(In,{className:"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg",align:"end",side:n?"bottom":t==="collapsed"?"left":"bottom",children:s.jsx(Vd,{user:e.user})})]})})})}function Yd(){return s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-sm",children:s.jsx(Wa,{className:"size-7 rounded-sm fill-current"})}),s.jsx("div",{className:"ml-1 grid flex-1 text-left text-sm overflow-hidden",children:s.jsx("span",{className:"mb-0.5 truncate leading-none font-semibold",children:"MOAMS"})})]})}const Xd=[{title:"Dashboard",url:"/dashboard",icon:Oa},{title:"Minibus Management",url:"/minibuses",icon:Ha,role:["association clerk","minibus owner"]},{title:"Route Management",url:"/routes",icon:is,role:["association clerk","association manager"]},{title:"Membership Management",url:"/membership-management",icon:Bn,role:["association clerk"]},{title:"Fee Management",url:"/fee-settings",icon:vr,role:["association manager"]},{title:"My Membership",url:"/my-membership",icon:Bn,role:["minibus owner"]},{title:"Driver Management",url:"/drivers",icon:ps,role:["association clerk","minibus owner"]},{title:"Misconduct Management",url:"/misconducts",icon:Ia,role:["association clerk","association manager","minibus owner"]},{title:"User Management",url:"/admin/users",icon:Ua,role:"system admin"}];function Zd(){var o;const{auth:e}=xe().props,t=xe().props;xe();let n=[];t.userRoles&&Array.isArray(t.userRoles)?n=t.userRoles:(o=e==null?void 0:e.user)!=null&&o.roles&&Array.isArray(e.user.roles)&&(n=e.user.roles.map(a=>typeof a=="string"?a:a.name)),n.includes("system admin");const r=Xd.filter(a=>a.role?Array.isArray(a.role)?a.role.some(i=>n.includes(i)):n.includes(a.role):!0).map(a=>a.url==="/minibuses"&&n.includes("minibus owner")?{...a,title:"My Minibuses"}:a.url==="/drivers"&&n.includes("minibus owner")?{...a,title:"My Drivers"}:a);return s.jsxs(Bl,{collapsible:"icon",variant:"inset",children:[s.jsx(Ul,{children:s.jsx(En,{children:s.jsx(Sn,{children:s.jsx(Rn,{size:"lg",asChild:!0,children:s.jsx(fe,{href:"/dashboard",prefetch:!0,children:s.jsx(Yd,{})})})})})}),s.jsx("hr",{}),s.jsx(Kl,{children:s.jsx(Zl,{items:r})}),s.jsx(Gl,{children:s.jsx(qd,{})})]})}function Qd({...e}){return s.jsx("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",...e})}function Jd({className:e,...t}){return s.jsx("ol",{"data-slot":"breadcrumb-list",className:P("text-muted-foreground flex flex-nowrap items-center gap-1.5 text-sm break-words sm:gap-2.5 max-w-full overflow-hidden",e),...t})}function $e({className:e,...t}){return s.jsx("li",{"data-slot":"breadcrumb-item",className:P("inline-flex items-center gap-1.5 max-w-full",e),...t})}function Vt({asChild:e,className:t,...n}){const r=e?vt:"a";return s.jsx(r,{"data-slot":"breadcrumb-link",className:P("hover:text-foreground transition-colors",t),...n})}function qt({className:e,...t}){return s.jsx("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",className:P("text-foreground font-normal",e),...t})}function at({children:e,className:t,...n}){return s.jsx("li",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",className:P("[&>svg]:size-3.5",t),...n,children:e??s.jsx(Ya,{})})}function ef({className:e,...t}){return s.jsxs("span",{"data-slot":"breadcrumb-ellipsis",role:"presentation","aria-hidden":"true",className:P("flex size-9 items-center justify-center",e),...t,children:[s.jsx(ts,{className:"size-4"}),s.jsx("span",{className:"sr-only",children:"More"})]})}function tf({breadcrumbs:e}){if(e.length===0)return null;const t=e.length>2;return s.jsx(Qd,{children:s.jsx(Jd,{children:t?s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"sm:hidden flex items-center",children:[s.jsx($e,{children:s.jsx(Vt,{asChild:!0,children:s.jsx(fe,{href:e[0].href,children:e[0].title})})}),s.jsx(at,{}),e.length>2&&s.jsxs(s.Fragment,{children:[s.jsx($e,{children:s.jsxs(Dn,{children:[s.jsx(On,{className:"flex items-center gap-1 hover:text-foreground",children:s.jsx(ef,{})}),s.jsx(In,{align:"start",children:e.slice(1,-1).map((n,r)=>s.jsx(on,{asChild:!0,children:s.jsx(fe,{href:n.href,className:"w-full",children:n.title})},r))})]})}),s.jsx(at,{})]}),s.jsx($e,{children:s.jsx(qt,{children:e[e.length-1].title})})]}),s.jsx("div",{className:"hidden sm:flex sm:items-center",children:e.map((n,r)=>{const o=r===e.length-1;return s.jsxs(l.Fragment,{children:[s.jsx($e,{children:o?s.jsx(qt,{children:n.title}):s.jsx(Vt,{asChild:!0,children:s.jsx(fe,{href:n.href,children:n.title})})}),!o&&s.jsx(at,{})]},r)})})]}):e.map((n,r)=>{const o=r===e.length-1;return s.jsxs(l.Fragment,{children:[s.jsx($e,{children:o?s.jsx(qt,{children:n.title}):s.jsx(Vt,{asChild:!0,children:s.jsx(fe,{href:n.href,children:n.title})})}),!o&&s.jsx(at,{})]},r)})})})}const nf=mr("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-auto",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function rf({className:e,variant:t,asChild:n=!1,...r}){const o=n?vt:"span";return s.jsx(o,{"data-slot":"badge",className:P(nf({variant:t}),e),...r})}function of(){var y,E;const[e,t]=l.useState([]),[n,r]=l.useState(0),[o,a]=l.useState(!1),[i,c]=l.useState(!1),{props:f}=xe(),p=((E=(y=f.auth)==null?void 0:y.user)==null?void 0:E.roles)||[];l.useEffect(()=>{d();const w=setInterval(d,3e4);return()=>clearInterval(w)},[]);const d=async()=>{try{const C=await(await fetch("/api/notifications/unread")).json();t(C.notifications||[]),r(C.count||0)}catch(w){console.error("Failed to fetch notifications:",w)}},u=async w=>{var C;try{await fetch(`/api/notifications/${w}/read`,{method:"POST",headers:{"X-CSRF-TOKEN":(C=document.querySelector('meta[name="csrf-token"]'))==null?void 0:C.getAttribute("content")}}),t(S=>S.filter(R=>R.id!==w)),r(S=>Math.max(0,S-1))}catch(S){console.error("Failed to mark notification as read:",S)}},m=async()=>{var w;try{c(!0),await fetch("/api/notifications/mark-all-read",{method:"POST",headers:{"X-CSRF-TOKEN":(w=document.querySelector('meta[name="csrf-token"]'))==null?void 0:w.getAttribute("content")}}),t([]),r(0)}catch(C){console.error("Failed to mark all notifications as read:",C)}finally{c(!1)}},g=async w=>{var C;try{await fetch(`/api/notifications/${w}`,{method:"DELETE",headers:{"X-CSRF-TOKEN":(C=document.querySelector('meta[name="csrf-token"]'))==null?void 0:C.getAttribute("content")}}),t(S=>S.filter(R=>R.id!==w)),r(S=>Math.max(0,S-1))}catch(S){console.error("Failed to delete notification:",S)}},x=w=>w.includes("Payment")?s.jsx(Ja,{className:"h-4 w-4 text-blue-600"}):w.includes("Transfer")?s.jsx(ds,{className:"h-4 w-4 text-green-600"}):w.includes("Clearance")?s.jsx(Ka,{className:"h-4 w-4 text-orange-600"}):w.includes("Fee")?s.jsx(vr,{className:"h-4 w-4 text-yellow-600"}):w.includes("Membership")?s.jsx(Ga,{className:"h-4 w-4 text-purple-600"}):s.jsx(Dt,{className:"h-4 w-4 text-gray-600"}),h=w=>{const C=w.data;if(C.title)return C.title;const S=w.type;return S.includes("PaymentSuccess")?"Payment Successful":S.includes("PaymentFailure")?"Payment Failed":S.includes("TransferRequest")?"Transfer Request":S.includes("ClearanceRequest")?"Clearance Request":S.includes("FeeChange")?"Fee Update":S.includes("MembershipExpiration")?"Membership Reminder":"Notification"},v=w=>{const C=w.data;return C.message?C.message:C.user_name&&C.amount?`${C.user_name} - MK ${C.amount}`:C.minibus_number_plate?`Minibus: ${C.minibus_number_plate}`:C.driver_name?`Driver: ${C.driver_name}`:"New notification received"},b=w=>{u(w.id);const C=w.data,S=w.type,R=p.includes("association clerk"),A=p.includes("association manager");S.includes("TransferRequest")&&C.transfer_request_id?ee.visit(`/minibuses/transfer-requests/${C.transfer_request_id}`):S.includes("ClearanceRequest")&&C.clearance_request_id?ee.visit(`/drivers/clearance-requests/${C.clearance_request_id}`):S.includes("Payment")?R||A?ee.visit("/payment-analytics"):ee.visit("/summary-membership"):S.includes("Membership")&&C.membership_id?R||A?ee.visit("/membership-management"):ee.visit("/my-membership"):S.includes("Fee")?R||A?ee.visit("/fee-settings"):ee.visit("/my-membership"):ee.visit("/dashboard"),a(!1)};return s.jsxs(Dn,{open:o,onOpenChange:a,children:[s.jsx(On,{asChild:!0,children:s.jsxs(Be,{variant:"ghost",size:"icon",className:"relative h-9 w-9",children:[s.jsx(Dt,{className:"h-5 w-5"}),n>0&&s.jsx(rf,{variant:"destructive",className:"absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs",children:n>99?"99+":n})]})}),s.jsxs(In,{align:"end",className:"w-80",children:[s.jsxs("div",{className:"flex items-center justify-between p-3 border-b",children:[s.jsx("h3",{className:"font-semibold",children:"Notifications"}),n>0&&s.jsxs(Be,{variant:"ghost",size:"sm",onClick:m,disabled:i,className:"text-xs",children:[s.jsx(Fa,{className:"h-3 w-3 mr-1"}),"Mark all read"]})]}),s.jsx("div",{className:"max-h-96 overflow-y-auto",children:e.length===0?s.jsxs("div",{className:"p-4 text-center text-gray-500",children:[s.jsx(Dt,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),s.jsx("p",{className:"text-sm",children:"No new notifications"})]}):e.map(w=>s.jsx("div",{className:"p-3 border-b hover:bg-gray-50 cursor-pointer group",onClick:()=>b(w),children:s.jsxs("div",{className:"flex items-start gap-3",children:[s.jsx("div",{className:"flex-shrink-0 mt-1",children:x(w.type)}),s.jsxs("div",{className:"flex-1 min-w-0",children:[s.jsx("p",{className:"font-medium text-sm truncate",children:h(w)}),s.jsx("p",{className:"text-xs text-gray-600 mt-1 line-clamp-2",children:v(w)}),s.jsx("p",{className:"text-xs text-gray-400 mt-1",children:w.created_at})]}),s.jsx(Be,{variant:"ghost",size:"sm",className:"opacity-0 group-hover:opacity-100 h-6 w-6 p-0",onClick:C=>{C.stopPropagation(),g(w.id)},children:s.jsx(xr,{className:"h-3 w-3"})})]})},w.id))}),e.length>0&&s.jsxs(s.Fragment,{children:[s.jsx(an,{}),s.jsx("div",{className:"p-2",children:s.jsx(Be,{variant:"ghost",size:"sm",className:"w-full text-xs",onClick:()=>{ee.visit("/notifications"),a(!1)},children:"View all notifications"})})]})]})]})}function af({breadcrumbs:e=[]}){return s.jsxs("header",{className:"border-sidebar-border/50 flex h-16 shrink-0 items-center gap-2 border-b px-6 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 md:px-4",children:[s.jsxs("div",{className:"flex items-center gap-2 flex-1",children:[s.jsx(Wl,{className:"-ml-1"}),s.jsx(tf,{breadcrumbs:e})]}),s.jsx("div",{className:"flex items-center gap-2",children:s.jsx(of,{})})]})}function pr({message:e,type:t="success",duration:n=5e3,className:r="",onClose:o}){if(!e||typeof e!="string"||e.trim()==="")return null;const[a,i]=l.useState(!!e);if(l.useEffect(()=>{if(e&&(i(!0),n>0)){const f=setTimeout(()=>{i(!1),o&&o()},n);return()=>clearTimeout(f)}},[e,n,o]),!a||!e)return null;const c={success:"bg-green-200 text-green-900 border-green-400",error:"bg-red-200 text-red-900 border-red-400",info:"bg-blue-200 text-blue-900 border-blue-400",warning:"bg-yellow-200 text-yellow-900 border-yellow-400"};return s.jsx("div",{className:P("fixed top-0 left-0 w-full flex justify-center z-50 transition-all duration-300",a?"opacity-100 translate-y-0":"opacity-0 -translate-y-2 pointer-events-none",r),children:s.jsx("div",{className:P("mt-6 max-w-2xl w-full flex items-center text-center gap-3 px-5 py-3 rounded-lg border shadow-2xl font-semibold text-base text-center",c[t]||c.success),style:{minWidth:220},children:e})})}function sf({children:e,breadcrumbs:t=[]}){const{flash:n}=xe().props;return s.jsxs(Xl,{variant:"sidebar",children:[s.jsx(Zd,{}),s.jsxs(Yl,{variant:"sidebar",children:[s.jsx(af,{breadcrumbs:t}),(n==null?void 0:n.success)&&s.jsx(pr,{message:n.success,type:"success"}),(n==null?void 0:n.error)&&s.jsx(pr,{message:n.error,type:"error"}),e]})]})}const Ef=({children:e,breadcrumbs:t,...n})=>s.jsx(sf,{breadcrumbs:t,...n,children:e});export{Ef as A,rf as B,Ya as C,Di as D,sn as F,Pi as O,Ni as P,_i as R,Po as T,ps as U,sl as V,xr as X,Za as a,Pl as b,jl as c,Tl as d,ji as e,Oi as f,Ti as g,Ja as h,Hd as i,Gd as j,Cf as k,Mt as l,go as m,we as n,To as o,vo as p,bt as q,oe as r,Tr as s,Er as t,Do as u,cn as v,wt as w,xo as x,wo as y};
