<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('membership_id')->nullable()->constrained('memberships')->onDelete('cascade');
            $table->decimal('amount', 10, 2);
            $table->enum('fee_type', ['registration', 'affiliation']);
            $table->enum('status', ['pending', 'fulfilled'])->default('pending');
            $table->enum('payment_method', ['cash', 'mobile_money', 'bank_transfer', 'ctechpay_card'])->default('cash');
            $table->timestamp('paid_at')->nullable();
            $table->string('verification_method')->nullable(); // 'clerk_manual', 'bank_transfer', 'ctechpay_api', etc.
            $table->text('verification_notes')->nullable();

            // Ctechpay specific fields
            $table->string('ctechpay_order_reference')->nullable(); // Order reference from ctechpay
            $table->string('ctechpay_transaction_id')->nullable(); // Transaction ID from ctechpay
            $table->json('ctechpay_response_data')->nullable(); // Store full API response
            $table->string('ctechpay_payment_url')->nullable(); // Payment page URL
            $table->enum('ctechpay_status', ['PENDING', 'PURCHASED', 'FAILED', 'CANCELLED'])->nullable(); // Ctechpay status
            $table->string('ctechpay_currency_code')->nullable(); // Currency code (e.g., MWK)
            $table->string('ctechpay_formatted_amount')->nullable(); // Formatted amount from API
            $table->string('ctechpay_card_holder_name')->nullable(); // Card holder name for successful payments
            $table->text('ctechpay_error_message')->nullable(); // Error message for failed payments
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
