import{j as e,L as t,B as r,C as n,a as m,b as x,c as o}from"./app-DL-qYY5V.js";import{A as u,B as h}from"./app-layout-YqstQnqE.js";import{A as g}from"./arrow-left-DCW23wrL.js";import{D as N}from"./dollar-sign-Cx0-nQIX.js";import{C as y}from"./calendar-Bzuvt9Ns.js";import{U as v}from"./users-DNGXY-sJ.js";import{F as b}from"./file-text-BcxQqvSb.js";import"./index-1tPPImJd.js";import"./index-BTzg1GwG.js";import"./app-logo-icon-BnXlkpcX.js";import"./bus-CKTBKI0T.js";import"./clipboard-list-CxRumT8v.js";function k({feeSetting:s}){var c,d;const p=[{title:"Dashboard",href:"/dashboard"},{title:"Fee Management",href:"/fee-settings"},{title:`${s.fee_type} Fee Details`,href:`/fee-settings/${s.id}`}],i=a=>{switch(a){case"registration":return"📝";case"affiliation":return"🤝";default:return"💰"}},j=a=>a?e.jsx(h,{className:"bg-green-100 text-green-800 hover:bg-green-100",children:"Active"}):e.jsx(h,{variant:"secondary",children:"Inactive"}),f=a=>new Date(a).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),l=a=>new Date(a).toLocaleString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return e.jsx(u,{breadcrumbs:p,children:e.jsxs("div",{className:"p-2 sm:p-4 md:p-8",children:[e.jsx("div",{className:"flex flex-col sm:flex-row gap-2 sm:gap-4 items-center mb-4 sm:mb-6",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(t,{href:"/fee-settings",children:e.jsxs(r,{variant:"outline",className:"w-full sm:w-auto",children:[e.jsx(g,{className:"h-4 w-4 mr-2"}),"Back to Fee Management"]})}),e.jsx("span",{className:"text-muted-foreground text-base font-medium",children:"View detailed information about this fee setting."})]})}),e.jsxs("div",{className:"w-full max-w-4xl mx-auto space-y-6",children:[e.jsxs(n,{children:[e.jsx(m,{children:e.jsxs(x,{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-2xl",children:i(s.fee_type)}),s.fee_type.charAt(0).toUpperCase()+s.fee_type.slice(1)," Fee Setting",j(s.is_active)]})}),e.jsxs(o,{children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(N,{className:"h-5 w-5 text-green-600"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Amount"}),e.jsxs("p",{className:"text-2xl font-bold",children:["$",s.amount.toFixed(2)]})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(y,{className:"h-5 w-5 text-blue-600"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Effective From"}),e.jsx("p",{className:"text-lg font-semibold",children:f(s.effective_from)})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(v,{className:"h-5 w-5 text-purple-600"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Created By"}),e.jsxs("p",{className:"text-lg font-semibold",children:[(c=s.created_by_user)==null?void 0:c.first_name," ",(d=s.created_by_user)==null?void 0:d.last_name]})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-500 mb-2",children:"Fee Type"}),e.jsxs("div",{className:"flex items-center gap-2 p-3 border rounded-md bg-gray-50",children:[e.jsx("span",{className:"text-lg",children:i(s.fee_type)}),e.jsx("span",{className:"font-medium capitalize",children:s.fee_type})]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-500 mb-2",children:"Created At"}),e.jsx("p",{className:"text-sm",children:l(s.created_at)})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-500 mb-2",children:"Last Updated"}),e.jsx("p",{className:"text-sm",children:l(s.updated_at)})]})]})]}),s.description&&e.jsxs("div",{className:"mt-6 pt-6 border-t",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-3",children:[e.jsx(b,{className:"h-5 w-5 text-gray-600"}),e.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Description"})]}),e.jsx("div",{className:"p-4 bg-gray-50 rounded-lg",children:e.jsx("p",{className:"text-gray-700",children:s.description})})]})]})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3",children:[s.is_active&&e.jsx(t,{href:route("fee-settings.edit",s.id),children:e.jsx(r,{className:"flex-1 sm:flex-none",children:"Edit Fee Setting"})}),e.jsx(t,{href:route("fee-settings.history",s.fee_type),children:e.jsx(r,{variant:"outline",className:"flex-1 sm:flex-none",children:"View Fee History"})}),e.jsx(t,{href:"/fee-settings",children:e.jsx(r,{variant:"outline",className:"flex-1 sm:flex-none",children:"Back to Fee Management"})})]}),e.jsxs(n,{children:[e.jsx(m,{children:e.jsx(x,{className:"text-lg",children:"Additional Information"})}),e.jsx(o,{children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-gray-700",children:"Fee Setting ID"}),e.jsxs("p",{className:"text-gray-600",children:["#",s.id]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-gray-700",children:"Status"}),e.jsx("p",{className:"text-gray-600",children:s.is_active?"Currently Active":"Not Active"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-gray-700",children:"Effective Date"}),e.jsx("p",{className:"text-gray-600",children:new Date(s.effective_from)<=new Date?"Already in effect":"Future effective date"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-gray-700",children:"Created By User ID"}),e.jsxs("p",{className:"text-gray-600",children:["#",s.created_by]})]})]})})]})]})]})})}export{k as default};
