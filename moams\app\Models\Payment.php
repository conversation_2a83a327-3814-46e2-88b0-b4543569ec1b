<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $user_id
 * @property int|null $membership_id
 * @property float $amount
 * @property string $fee_type
 * @property string $status
 * @property string $payment_method
 * @property \Carbon\Carbon|null $paid_at
 * @property string|null $verification_method
 * @property string|null $verification_notes
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property-read \App\Models\Membership|null $membership
 * @property-read \App\Models\User $user
 */
class Payment extends Model
{
    /** @use HasFactory<\Database\Factories\PaymentFactory> */
    use HasFactory;

    protected $fillable = [
        'user_id',
        'membership_id',
        'amount',
        'fee_type',
        'status',
        'payment_method',
        'paid_at',
        'verification_method',
        'verification_notes',
        // Ctechpay fields
        'ctechpay_order_reference',
        'ctechpay_transaction_id',
        'ctechpay_response_data',
        'ctechpay_payment_url',
        'ctechpay_status',
        'ctechpay_currency_code',
        'ctechpay_formatted_amount',
        'ctechpay_card_holder_name',
        'ctechpay_error_message',

    ];

    protected $casts = [
        'paid_at' => 'datetime',
        'amount' => 'decimal:2',
        'ctechpay_response_data' => 'array',

    ];

    public function membership(): BelongsTo
    {
        return $this->belongsTo(Membership::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Check if this is a ctechpay payment
     */
    public function isCtechpayPayment(): bool
    {
        return $this->payment_method === 'ctechpay_card';
    }

    /**
     * Check if ctechpay payment is successful
     */
    public function isCtechpaySuccessful(): bool
    {
        return $this->isCtechpayPayment() && $this->ctechpay_status === 'PURCHASED';
    }

    /**
     * Check if ctechpay payment is pending
     */
    public function isCtechpayPending(): bool
    {
        return $this->isCtechpayPayment() && $this->ctechpay_status === 'PENDING';
    }

    /**
     * Check if ctechpay payment has failed
     */
    public function isCtechpayFailed(): bool
    {
        return $this->isCtechpayPayment() && in_array($this->ctechpay_status, ['FAILED', 'CANCELLED']);
    }



    /**
     * Get the payment gateway used
     */
    public function getPaymentGateway(): string
    {
        if ($this->isCtechpayPayment()) {
            return 'ctechpay';
        } else {
            return 'manual';
        }
    }

    /**
     * Check if payment is successful (any gateway)
     */
    public function isSuccessful(): bool
    {
        return $this->status === 'fulfilled' ||
            $this->isCtechpaySuccessful();
    }

    /**
     * Check if payment is pending (any gateway)
     */
    public function isPending(): bool
    {
        return $this->status === 'pending' ||
            $this->isCtechpayPending();
    }
}
