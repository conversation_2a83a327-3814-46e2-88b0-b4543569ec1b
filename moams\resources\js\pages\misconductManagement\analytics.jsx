import { Head, Link, router } from '@inertiajs/react';
import { navigateToLogicalParent } from '@/utils/navigation';
import { ChevronLeft, BarChart3, TrendingUp, Users, AlertTriangle, CheckCircle, FileText, Calendar, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import AppLayout from '@/layouts/app-layout';
import { useState } from 'react';

export default function MisconductAnalytics({
    monthlyStats,
    trends,
    topOffenders,
    severityBreakdown,
    resolutionBreakdown,
    routeStats,
    selectedMonth,
    viewType = 'monthly'
}) {
    const [loading, setLoading] = useState(false);

    const handleExport = async () => {
        try {
            setLoading(true);

            // Create a temporary link to download the PDF
            const link = document.createElement('a');
            const params = new URLSearchParams({
                view: viewType,
                ...(viewType === 'monthly' ? { month: selectedMonth } : {})
            });
            link.href = `/misconduct-analytics/report?${params.toString()}`;
            link.download = viewType === 'monthly'
                ? `misconduct-report-${selectedMonth}.pdf`
                : `misconduct-report-alltime.pdf`;

            // Trigger download without opening new window
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

        } catch (error) {
            console.error('Error downloading PDF:', error);
        } finally {
            setLoading(false);
        }
    };
    const breadcrumbs = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Misconduct Management', href: '/misconducts' },
        { title: 'Analytics', href: '/misconduct-analytics' },
    ];

    const StatCard = ({ title, value, icon: Icon, color = "blue", subtitle }) => (
        <Card>
            <CardContent className="p-6">
                <div className="flex items-center justify-between">
                    <div>
                        <p className="text-sm font-medium text-gray-600">{title}</p>
                        <p className={`text-2xl font-bold text-${color}-600`}>{value}</p>
                        {subtitle && <p className="text-xs text-gray-500 mt-1">{subtitle}</p>}
                    </div>
                    <Icon className={`h-8 w-8 text-${color}-600`} />
                </div>
            </CardContent>
        </Card>
    );

    const getSeverityColor = (severity) => {
        const colors = {
            low: 'bg-green-100 text-green-800',
            medium: 'bg-orange-100 text-orange-800',
            high: 'bg-red-100 text-red-800'
        };
        return colors[severity?.toLowerCase()] || 'bg-gray-100 text-gray-800';
    };

    const getTrustScoreColor = (score) => {
        if (score >= 80) return 'bg-green-100 text-green-800';
        if (score >= 60) return 'bg-orange-100 text-orange-800';
        return 'bg-red-100 text-red-800';
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Misconduct Analytics" />

            <div className="w-full max-w-full overflow-hidden">
                <div className="container mx-auto px-4 py-8">
                    {/* Header */}
                    <div className="flex flex-col gap-4 mb-6">
                        {/* First row: Back button and tabs */}
                        <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                            <div className="flex-1 flex items-center gap-2">
                                <Link
                                    href="/misconducts"
                                    className="flex items-center px-3 py-1 rounded hover:bg-gray-100 border border-gray-200"
                                >
                                    <ChevronLeft className="h-4 w-4 mr-1" /> Back
                                </Link>
                                <div className="flex items-center gap-4">
                                    <Tabs
                                        value={viewType}
                                        onValueChange={(value) => router.get('/misconduct-analytics', {
                                            view: value,
                                            ...(value === 'monthly' ? { month: selectedMonth } : {})
                                        })}
                                    >
                                        <TabsList className="grid w-fit grid-cols-2">
                                            <TabsTrigger value="monthly" className="flex items-center gap-2">
                                                <Calendar className="h-4 w-4" />
                                                Monthly
                                            </TabsTrigger>
                                            <TabsTrigger value="alltime" className="flex items-center gap-2">
                                                <Clock className="h-4 w-4" />
                                                All-Time
                                            </TabsTrigger>
                                        </TabsList>
                                    </Tabs>

                                    {/* Month selector - hidden on small screens */}
                                    {viewType === 'monthly' && (
                                        <div className="hidden sm:flex items-center gap-2">
                                            <Select
                                                value={selectedMonth}
                                                onValueChange={(value) => router.get('/misconduct-analytics', { view: 'monthly', month: value })}
                                            >
                                                <SelectTrigger className="w-40">
                                                    <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {Array.from({ length: 6 }, (_, i) => {
                                                        const date = new Date();
                                                        date.setMonth(date.getMonth() - i);
                                                        const monthValue = date.toISOString().slice(0, 7);
                                                        const monthLabel = date.toLocaleDateString('en-US', {
                                                            year: 'numeric',
                                                            month: 'long'
                                                        });
                                                        return (
                                                            <SelectItem key={monthValue} value={monthValue}>
                                                                {monthLabel}
                                                            </SelectItem>
                                                        );
                                                    })}
                                                </SelectContent>
                                            </Select>
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* Export button - hidden on small screens */}
                            <Button
                                variant="outline"
                                onClick={handleExport}
                                disabled={loading}
                                className="hidden sm:flex items-center gap-2 border-green-600 text-green-600 hover:bg-green-50 disabled:opacity-50"
                            >
                                <FileText className="h-4 w-4" />
                                {loading ? 'Generating PDF...' : 'Export PDF Report'}
                            </Button>
                        </div>

                        {/* Second row: Month selector and Export button on small screens */}
                        <div className="flex sm:hidden items-center justify-between gap-4">
                            {viewType === 'monthly' && (
                                <Select
                                    value={selectedMonth}
                                    onValueChange={(value) => router.get('/misconduct-analytics', { view: 'monthly', month: value })}
                                >
                                    <SelectTrigger className="w-40">
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {Array.from({ length: 6 }, (_, i) => {
                                            const date = new Date();
                                            date.setMonth(date.getMonth() - i);
                                            const monthValue = date.toISOString().slice(0, 7);
                                            const monthLabel = date.toLocaleDateString('en-US', {
                                                year: 'numeric',
                                                month: 'short'
                                            });
                                            return (
                                                <SelectItem key={monthValue} value={monthValue}>
                                                    {monthLabel}
                                                </SelectItem>
                                            );
                                        })}
                                    </SelectContent>
                                </Select>
                            )}

                            <Button
                                variant="outline"
                                onClick={handleExport}
                                disabled={loading}
                                className="flex items-center gap-2 border-green-600 text-green-600 hover:bg-green-50 disabled:opacity-50"
                            >
                                <FileText className="h-4 w-4" />
                                <span className="hidden xs:inline">{loading ? 'Generating...' : 'Export'}</span>
                                <span className="xs:hidden">PDF</span>
                            </Button>
                        </div>
                    </div>

                    <p className="text-gray-600 mb-6">
                        {viewType === 'monthly'
                            ? `Monthly insights and trends for ${selectedMonth}`
                            : 'All-time insights and trends'
                        }
                    </p>

                    {/* Key Statistics */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8">
                        <StatCard
                            title="Total Misconducts"
                            value={monthlyStats.total_misconducts}
                            icon={AlertTriangle}
                            color="red"
                            subtitle="This month"
                        />
                        <StatCard
                            title="Affected Drivers"
                            value={monthlyStats.unique_drivers}
                            icon={Users}
                            color="blue"
                            subtitle="With misconducts"
                        />
                        <StatCard
                            title="Resolution Rate"
                            value={`${monthlyStats.resolution_rate}%`}
                            icon={CheckCircle}
                            color="green"
                            subtitle="Cases resolved"
                        />
                        <StatCard
                            title="Avg Trust Score"
                            value={monthlyStats.average_trust_score}
                            icon={TrendingUp}
                            color="purple"
                            subtitle="Affected drivers"
                        />
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8">
                        {/* Severity Breakdown */}
                        <Card className="w-full">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <BarChart3 className="h-5 w-5" />
                                    Severity Breakdown
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    {severityBreakdown.map((item, index) => (
                                        <div key={index} className="flex items-center justify-between">
                                            <div className="flex items-center gap-3">
                                                <Badge className={getSeverityColor(item.severity)}>
                                                    {item.severity}
                                                </Badge>
                                            </div>
                                            <span className="font-semibold">{item.count}</span>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Resolution Status */}
                        <Card className="w-full">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <CheckCircle className="h-5 w-5" />
                                    Resolution Status
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    {resolutionBreakdown.map((item, index) => (
                                        <div key={index} className="flex items-center justify-between">
                                            <div className="flex items-center gap-3">
                                                <Badge className={item.status === 'Resolved' ? 'bg-green-100 text-green-800' : 'bg-orange-100 text-orange-800'}>
                                                    {item.status}
                                                </Badge>
                                            </div>
                                            <span className="font-semibold">{item.count}</span>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Top Offending Drivers */}
                    <div
                        className="w-full border-2 border-blue-200 rounded-lg mt-8"
                        style={{
                            maxWidth: 'calc(100vw - 2rem)',
                            overflow: 'hidden'
                        }}
                    >
                        <Card className="w-full border-0">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Users className="h-5 w-5" />
                                    Top 10 Offending Drivers
                                </CardTitle>
                                <p className="text-sm text-gray-600">
                                    Misconduct count for selected month • Trust score reflects all-time performance
                                </p>
                            </CardHeader>
                            <CardContent className="p-0">
                                {topOffenders.length === 0 ? (
                                    <div className="text-center py-8 px-6">
                                        <Users className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                                        <p className="text-gray-500">No driver misconducts recorded this month.</p>
                                    </div>
                                ) : (
                                    <div className="overflow-x-auto">
                                        <Table className="w-full min-w-[600px]">
                                            <TableHeader>
                                                <TableRow>
                                                    <TableHead className="px-2 py-1">Driver Name</TableHead>
                                                    <TableHead className="px-2 py-1">Trust Score</TableHead>
                                                    <TableHead className="px-2 py-1">Misconduct Count</TableHead>
                                                    <TableHead className="px-2 py-1">Minibus</TableHead>
                                                </TableRow>
                                            </TableHeader>
                                            <TableBody>
                                                {topOffenders.map((driver, index) => (
                                                    <TableRow key={index}>
                                                        <TableCell className="font-medium px-2 py-1">{driver.name}</TableCell>
                                                        <TableCell className="px-2 py-1">
                                                            <Badge className={getTrustScoreColor(driver.trust_score)}>
                                                                {driver.trust_score}%
                                                            </Badge>
                                                        </TableCell>
                                                        <TableCell className="px-2 py-1">
                                                            <Badge variant="outline">
                                                                {driver.misconduct_count}
                                                            </Badge>
                                                        </TableCell>
                                                        <TableCell className="px-2 py-1">{driver.minibus}</TableCell>
                                                    </TableRow>
                                                ))}
                                            </TableBody>
                                        </Table>
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                    </div>

                    {/* Route Statistics */}
                    <div
                        className="w-full border-2 border-blue-200 rounded-lg mt-8"
                        style={{
                            maxWidth: 'calc(100vw - 2rem)',
                            overflow: 'hidden'
                        }}
                    >
                        <Card className="w-full border-0">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <BarChart3 className="h-5 w-5" />
                                    Misconducts by Route
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="p-0">
                                {routeStats.length === 0 ? (
                                    <div className="text-center py-8 px-6">
                                        <BarChart3 className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                                        <p className="text-gray-500">No route-specific data available.</p>
                                    </div>
                                ) : (
                                    <div className="overflow-x-auto">
                                        <Table className="w-full min-w-[400px]">
                                            <TableHeader>
                                                <TableRow>
                                                    <TableHead className="px-2 py-1">Route Name</TableHead>
                                                    <TableHead className="px-2 py-1">Misconduct Count</TableHead>
                                                </TableRow>
                                            </TableHeader>
                                            <TableBody>
                                                {routeStats.map((route, index) => (
                                                    <TableRow key={index}>
                                                        <TableCell className="font-medium px-2 py-1">{route.route_name}</TableCell>
                                                        <TableCell className="px-2 py-1">
                                                            <Badge variant="outline">
                                                                {route.misconduct_count}
                                                            </Badge>
                                                        </TableCell>
                                                    </TableRow>
                                                ))}
                                            </TableBody>
                                        </Table>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>

                    {/* 6-Month Trends */}
                    <Card className="mt-8 w-full">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <TrendingUp className="h-5 w-5" />
                                6-Month Trend
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {trends.map((trend, index) => (
                                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <span className="font-medium">{trend.month}</span>
                                        <Badge variant="outline">{trend.count} misconducts</Badge>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
