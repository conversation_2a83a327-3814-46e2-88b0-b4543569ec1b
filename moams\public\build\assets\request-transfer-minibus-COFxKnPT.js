import{u as w,j as e,B as m,C as j,L as _}from"./app-DL-qYY5V.js";import{A as g}from"./app-layout-YqstQnqE.js";import{I as c}from"./input-error-C6jcuIY6.js";import{L as t}from"./label-e3QxUH-L.js";import{I as l}from"./input-Dm4SEXxy.js";import{T as y}from"./textarea-SHrtPYpi.js";import{A as N}from"./arrow-left-DCW23wrL.js";import{B as v}from"./bus-CKTBKI0T.js";import"./index-1tPPImJd.js";import"./index-BTzg1GwG.js";import"./app-logo-icon-BnXlkpcX.js";import"./dollar-sign-Cx0-nQIX.js";import"./users-DNGXY-sJ.js";import"./clipboard-list-CxRumT8v.js";function S({minibus:r,userRole:d,ownerName:p="",ownerPhone:u=""}){const{data:a,setData:o,post:h,processing:n,errors:i}=w({number_plate:r.number_plate,current_owner:p,owner_phone:u,new_owner_type:"member",reason:"",ownership_transfer_certificate:null}),x=[{title:"Dashboard",href:"/dashboard"},{title:d==="minibus owner"?"My Minibuses":"Minibus Management",href:"/minibuses"},{title:r.number_plate,href:`/minibuses/${r.id}`},{title:"Request Transfer",href:`/minibuses/${r.id}/transfer/request`}],f=s=>{o("ownership_transfer_certificate",s.target.files[0])},b=s=>{s.preventDefault(),h(route("minibuses.transfer.request.store",r.id),{forceFormData:!0})};return e.jsx(g,{breadcrumbs:x,children:e.jsxs("div",{className:"p-2 sm:p-4 md:p-8",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row gap-2 sm:gap-4 items-center sm:items-center sm:justify-between mb-4 sm:mb-6",children:[e.jsxs(m,{variant:"outline",onClick:()=>window.history.back(),className:"w-full sm:w-auto",children:[e.jsx(N,{className:"h-4 w-4 mr-2"}),"Back"]}),e.jsx("h1",{className:"text-lg sm:text-xl md:text-2xl font-bold text-center sm:text-left",children:"Request Minibus Transfer"})]}),e.jsx("div",{className:"w-full max-w-3xl mx-auto",children:e.jsxs(j,{className:"p-4 sm:p-6",children:[e.jsx("div",{className:"mb-6",children:e.jsxs("h3",{className:"text-base sm:text-lg md:text-xl font-semibold mb-3",children:[e.jsx(v,{className:"inline-block text-gray-500 mr-2 h-5 w-5 align-text-bottom"}),"Ownership Transfer Request Details"]})}),e.jsxs("form",{onSubmit:b,className:"space-y-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(t,{htmlFor:"number_plate",children:"Number Plate"}),e.jsx(l,{id:"number_plate",value:a.number_plate,disabled:!0,className:"bg-gray-50"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(t,{htmlFor:"current_owner",children:"Current Owner"}),e.jsx(l,{id:"current_owner",value:a.current_owner,disabled:!0,className:"bg-gray-50"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(t,{htmlFor:"owner_phone",children:"Owner Phone"}),e.jsx(l,{id:"owner_phone",value:a.owner_phone,disabled:!0,className:"bg-gray-50"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(t,{htmlFor:"new_owner_type",children:"New Owner Type"}),e.jsxs("select",{id:"new_owner_type",value:a.new_owner_type,onChange:s=>o("new_owner_type",s.target.value),className:"mt-1 block w-full px-3 py-2 text-base border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary",disabled:n,children:[e.jsx("option",{value:"member",children:"Association Member"}),e.jsx("option",{value:"non_member",children:"Non-Member"})]}),e.jsx(c,{message:i.new_owner_type})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(t,{htmlFor:"reason",children:"Reason for Ownership Transfer"}),e.jsx(y,{id:"reason",value:a.reason,onChange:s=>o("reason",s.target.value),placeholder:"Please provide a detailed reason for requesting the transfer...",rows:4,className:"mt-1 block w-full",disabled:n}),e.jsx(c,{message:i.reason})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(t,{htmlFor:"ownership_transfer_certificate",children:"Ownership Transfer Certificate (PDF/Image) *"}),e.jsx(l,{id:"ownership_transfer_certificate",type:"file",accept:".pdf,.jpg,.jpeg,.png",onChange:f,className:i.ownership_transfer_certificate?"border-red-500":""}),e.jsx(c,{message:i.ownership_transfer_certificate})]}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx(_,{href:route("minibuses.show",r.id),children:e.jsx(m,{type:"button",variant:"outline",disabled:n,children:"Cancel"})}),e.jsx(m,{type:"submit",disabled:n||!a.reason,className:"bg-blue-500 hover:bg-blue-600 text-white disabled:bg-blue-300",children:n?"Submitting...":"Submit Transfer Request"})]})]})]})})]})})}export{S as default};
