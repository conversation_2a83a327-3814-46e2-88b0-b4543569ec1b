import { Head, <PERSON> } from '@inertiajs/react';
import { navigateToLogicalParent } from '@/utils/navigation';
import { ArrowLeft, Eye, Clock, CheckCircle, XCircle, Search, Filter, ChevronLeft, ChevronRight } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { useState } from 'react';
import { router } from '@inertiajs/react';
import { route } from 'ziggy-js';

export default function ClearanceRequests({ clearanceRequests, userRole, filters }) {
    const [searchTerm, setSearchTerm] = useState(filters?.search || '');
    const [statusFilter, setStatusFilter] = useState(filters?.status || 'all');

    const breadcrumbs = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Driver Management', href: '/drivers' },
        { title: 'Clearance Requests', href: '/drivers/clearance-requests' },
    ];

    const getStatusIcon = (status) => {
        switch (status) {
            case 'pending':
                return <Clock className="h-4 w-4 text-amber-500" />;
            case 'approved':
                return <CheckCircle className="h-4 w-4 text-green-500" />;
            case 'rejected':
                return <XCircle className="h-4 w-4 text-red-500" />;
            default:
                return null;
        }
    };

    const getStatusBadge = (status) => {
        switch (status) {
            case 'pending':
                return <Badge variant="outline" className="text-amber-600 border-amber-600">Pending</Badge>;
            case 'approved':
                return <Badge variant="default" className="bg-green-600">Approved</Badge>;
            case 'rejected':
                return <Badge variant="destructive">Rejected</Badge>;
            default:
                return null;
        }
    };

    // Handle search and filter changes
    const handleSearch = (value) => {
        setSearchTerm(value);
        router.get('/drivers/clearance-requests', {
            search: value,
            status: statusFilter,
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleStatusFilter = (value) => {
        setStatusFilter(value);
        router.get('/drivers/clearance-requests', {
            search: searchTerm,
            status: value,
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleClearFilters = () => {
        setSearchTerm('');
        setStatusFilter('all');
        router.get('/drivers/clearance-requests', {}, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    // Get requests data (now paginated)
    const requestsData = clearanceRequests?.data || [];


    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Driver Clearance Requests" />

            <div className="w-full max-w-full overflow-hidden">
                <div className="container mx-auto px-4 py-8">
                    {/* Header section */}
                    <div className="flex flex-col gap-4 mb-6">
                        <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
                            <button
                                className="flex items-center px-3 py-1 rounded hover:bg-gray-100 border border-gray-200 w-fit"
                                onClick={() => navigateToLogicalParent(breadcrumbs)}
                            >
                                <ArrowLeft className="h-4 w-4 mr-1" /> Back
                            </button>
                            <span className="text-muted-foreground text-base font-medium">View and manage driver clearance requests.</span>
                        </div>
                    </div>

                    {/* Search and Filter section */}
                    <Card className="mb-6 w-full">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Search className="h-5 w-5" />
                                Search & Filter
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                                {/* Search Input */}
                                <div className="md:col-span-2">
                                    <Label htmlFor="search">Search</Label>
                                    <div className="relative">
                                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                                        <Input
                                            id="search"
                                            placeholder="Search by driver name, owner, phone, or reason..."
                                            value={searchTerm}
                                            onChange={(e) => handleSearch(e.target.value)}
                                            className="pl-10"
                                        />
                                    </div>
                                </div>

                                {/* Status Filter */}
                                <div>
                                    <Label htmlFor="status">Status</Label>
                                    <Select value={statusFilter} onValueChange={handleStatusFilter}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="All Requests" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">All Requests</SelectItem>
                                            <SelectItem value="pending">Pending</SelectItem>
                                            <SelectItem value="approved">Approved</SelectItem>
                                            <SelectItem value="rejected">Rejected</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                {/* Clear Filters Button */}
                                <div className="flex items-end">
                                    <Button
                                        variant="outline"
                                        onClick={handleClearFilters}
                                        className="w-full"
                                    >
                                        <Filter className="h-4 w-4 mr-2" />
                                        Clear Filters
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Requests Table Container - Fixed width that fits screen */}
                    <div
                        className="w-full"
                        style={{
                            maxWidth: 'calc(100vw - 2rem)',
                            overflow: 'hidden'
                        }}
                    >
                        <Card className="w-full border-0 shadow-none">
                            <CardHeader className="px-0">
                                <CardTitle className="flex items-center gap-2">
                                    <Clock className="h-5 w-5" />
                                    Clearance Requests ({clearanceRequests?.total || requestsData.length})
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="p-0">
                                {requestsData.length > 0 ? (
                                    <div className="overflow-x-auto mb-8">
                                        <table className="min-w-full w-full bg-white border border-gray-200 rounded-lg">
                                            <thead>
                                                <tr className="bg-gray-100 text-gray-700">
                                                    <th className="px-4 py-3 text-left font-medium">Driver</th>
                                                    <th className="px-4 py-3 text-left font-medium">Owner</th>
                                                    <th className="px-4 py-3 text-left font-medium">Reason</th>
                                                    <th className="px-4 py-3 text-left font-medium">Status</th>
                                                    <th className="px-4 py-3 text-left font-medium">Submitted Date</th>
                                                    <th className="px-4 py-3 text-left font-medium">Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {requestsData.map(request => (
                                                    <tr key={request.id} className="border-b hover:bg-gray-50">
                                                        <td className="px-4 py-3 font-medium">
                                                            {request.driver ? `${request.driver.first_name} ${request.driver.last_name}` : 'N/A'}
                                                        </td>
                                                        <td className="px-4 py-3">
                                                            {request.owner ? `${request.owner.first_name} ${request.owner.last_name}` : 'N/A'}
                                                        </td>
                                                        <td className="px-4 py-3 max-w-24">
                                                            <div className="truncate" title={request.reason || 'N/A'}>
                                                                {request.reason || 'N/A'}
                                                            </div>
                                                        </td>
                                                        <td className="px-4 py-3">
                                                            {getStatusBadge(request.status)}
                                                        </td>
                                                        <td className="px-4 py-3">
                                                            {request.created_at ? new Date(request.created_at).toLocaleDateString() : 'N/A'}
                                                        </td>
                                                        <td className="px-4 py-3">
                                                            <Button
                                                                variant="outline"
                                                                size="sm"
                                                                onClick={() => router.visit(route('drivers.clearance-requests.show', request.id))}
                                                                disabled={!request.driver || !request.owner}
                                                            >
                                                                <Eye className="h-4 w-4 mr-2" />
                                                                {request.status === 'pending' ? 'Review' : 'View Details'}
                                                            </Button>
                                                        </td>
                                                    </tr>
                                                ))}
                                            </tbody>
                                        </table>
                                    </div>

                                ) : (
                                    <div className="text-center py-8 px-6">
                                        <Clock className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                                        <h3 className="text-lg font-medium mb-2">No clearance requests found</h3>
                                        <p className="text-muted-foreground mb-4">
                                            {searchTerm || statusFilter !== 'all'
                                                ? 'Try adjusting your search or filter criteria.'
                                                : 'No clearance requests have been submitted yet.'}
                                        </p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>

                    {/* Pagination */}
                    {clearanceRequests && clearanceRequests.total > 0 && (
                        <div className="mt-6 w-full">
                            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 px-4 py-4 border-t bg-gray-50/50 rounded-b-lg">
                                <div className="text-sm text-gray-700 text-center sm:text-left">
                                    Showing {clearanceRequests.from} to {clearanceRequests.to} of {clearanceRequests.total} results
                                </div>
                                {clearanceRequests.last_page > 1 && (
                                    <div className="flex items-center justify-center sm:justify-end space-x-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => router.get(clearanceRequests.prev_page_url)}
                                            disabled={!clearanceRequests.prev_page_url}
                                            className="flex items-center gap-1"
                                        >
                                            <ChevronLeft className="h-4 w-4" />
                                            <span className="hidden sm:inline">Previous</span>
                                            <span className="sm:hidden">Prev</span>
                                        </Button>

                                        <span className="text-sm text-gray-600 px-2">
                                            {clearanceRequests.current_page} of {clearanceRequests.last_page}
                                        </span>

                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => router.get(clearanceRequests.next_page_url)}
                                            disabled={!clearanceRequests.next_page_url}
                                            className="flex items-center gap-1"
                                        >
                                            <span className="hidden sm:inline">Next</span>
                                            <span className="sm:hidden">Next</span>
                                            <ChevronRight className="h-4 w-4" />
                                        </Button>
                                    </div>
                                )}
                            </div>
                        </div>
                    )}

                </div>
            </div>
        </AppLayout>
    );
} 