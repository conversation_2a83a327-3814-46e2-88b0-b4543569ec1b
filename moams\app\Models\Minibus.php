<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Model;

class Minibus extends Model
{
    /** @use HasFactory<\Database\Factories\MinibusFactory> */
    use HasFactory;

    protected $fillable = [
        'number_plate',
        'make',
        'model',
        'year_of_make',
        'main_colour',
        'proof_of_ownership',
        'route_id',
        'owner_id',
        'archived'
    ];

    protected $casts = [
        'archived' => 'boolean'
    ];

    /**
     * The minibus owner (user with the 'minibus owner' role)
     */
    public function minibusOwner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id')->whereHas('roles', function ($q) {
            $q->where('name', 'minibus owner');
        });
    }

    /**
     * The user who owns the minibus (legacy, for backward compatibility)
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    /**
     * The route this minibus is assigned to
     */
    public function route()
    {
        return $this->belongsTo(VehicleRoute::class, 'route_id');
    }

    /**
     * The ownership history for this minibus
     */
    public function ownershipHistory(): HasMany
    {
        return $this->hasMany(MinibusOwnershipHistory::class);
    }

    /**
     * The offenses associated with this minibus
     */
    // public function offenses(): HasMany
    // {
    //     return $this->hasMany(Offense::class);
    // }
}
