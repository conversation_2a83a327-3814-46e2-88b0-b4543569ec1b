import { Head, Link, router, useForm } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Edit, Archive, History, User, Trash2, AlertTriangle } from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { Dialog, DialogTrigger, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription, DialogClose } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { useState } from 'react';
import ConfirmDialog from '@/components/ui/confirm-dialog';

export default function ShowDriver({ driver, userRole, flash, employmentHistories }) {
    const breadcrumbs = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: userRole === 'minibus owner' ? 'My Drivers' : 'Driver Management', href: '/drivers' },
        { title: `${driver.first_name} ${driver.last_name}`, href: `/drivers/${driver.id}` },
    ];

    const [confirmDialog, setConfirmDialog] = useState({ open: false, action: null });
    const [loading, setLoading] = useState(false);
    const { data, setData, patch, processing, errors, reset } = useForm({
        reason: '',
        admin_notes: '',
    });
    const [licenseModalOpen, setLicenseModalOpen] = useState(false);

    const handleAction = (action) => {
        setConfirmDialog({ open: true, action });
    };
    const handleCancel = () => {
        setConfirmDialog({ open: false, action: null });
        reset();
    };
    const handleConfirm = async () => {
        setLoading(true);
        if (confirmDialog.action === 'delete') {
            await router.delete(route('drivers.destroy', driver.id));
        } else if (confirmDialog.action === 'unarchive') {
            await router.patch(route('drivers.unarchive', driver.id), {}, { onSuccess: () => router.reload() });
        } else if (confirmDialog.action === 'clear') {
            await patch(route('drivers.direct-clear', driver.id), {
                onSuccess: () => {
                    reset();
                },
            });
        }
        setLoading(false);
        setConfirmDialog({ open: false, action: null });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Driver - ${driver.first_name} ${driver.last_name}`} />

            <div className="container mx-auto px-4 py-8">
                <div className="space-y-8">
                    <div className="flex items-center space-x-4 mb-6">
                        <Link href={route('drivers.index')}>
                            <Button variant="outline" size="sm">
                                <span className="block sm:hidden">
                                    <Tooltip>
                                        <TooltipTrigger asChild>
                                            <span><ArrowLeft className="h-4 w-4" /></span>
                                        </TooltipTrigger>
                                        <TooltipContent>Back to Drivers</TooltipContent>
                                    </Tooltip>
                                </span>
                                <span className="hidden sm:flex items-center">
                                    <ArrowLeft className="mr-2 h-4 w-4" />
                                    Back to Drivers
                                </span>
                            </Button>
                        </Link>
                        <span className="text-muted-foreground text-base font-medium">
                            View detailed information about this driver and their employment history.
                        </span>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        {/* Driver Information */}
                        <div className="lg:col-span-2 space-y-8">
                            <Card className="mb-6">
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <User className="h-5 w-5" />
                                        Personal Information
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div>
                                            <label className="text-sm font-medium text-muted-foreground">First Name</label>
                                            <p className="text-sm">{driver.first_name}</p>
                                        </div>
                                        <div>
                                            <label className="text-sm font-medium text-muted-foreground">Last Name</label>
                                            <p className="text-sm">{driver.last_name}</p>
                                        </div>
                                        <div>
                                            <label className="text-sm font-medium text-muted-foreground">Phone Number</label>
                                            <p className="text-sm">{driver.phone_number}</p>
                                        </div>
                                        <div>
                                            <label className="text-sm font-medium text-muted-foreground">Status</label>
                                            <div className="flex items-center gap-2">
                                                {driver.archived ? (
                                                    <Badge className="bg-red-100 text-red-800 border border-red-600">Archived</Badge>
                                                ) : (
                                                    <Badge className="bg-green-100 text-green-800 border border-green-600">Active</Badge>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card className="mb-6">
                                <CardHeader>
                                    <CardTitle>Location Information</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div>
                                            <label className="text-sm font-medium text-muted-foreground">District</label>
                                            <p className="text-sm">{driver.district}</p>
                                        </div>
                                        <div>
                                            <label className="text-sm font-medium text-muted-foreground">Village/Town</label>
                                            <p className="text-sm">{driver.village_town}</p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader>
                                    <CardTitle>Employment Information</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div>
                                            <label className="text-sm font-medium text-muted-foreground">Minibus Owner</label>
                                            <p className="text-sm">{driver.minibus_owner ? `${driver.minibus_owner.first_name ?? driver.minibus_owner.user?.first_name ?? ''} ${driver.minibus_owner.last_name ?? driver.minibus_owner.user?.last_name ?? ''}`.trim() : 'Not assigned'}</p>
                                        </div>
                                        <div>
                                            <label className="text-sm font-medium text-muted-foreground">Registration Date</label>
                                            <p className="text-sm">
                                                {new Date(driver.created_at).toLocaleDateString()}
                                            </p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Actions Sidebar */}
                        <div className="space-y-6">
                            <Card className="mb-6">
                                <CardHeader>
                                    <CardTitle>Actions</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="flex flex-col gap-3">
                                        {/* Report Misconduct - Only for minibus owners */}
                                        {userRole === 'minibus owner' && (
                                            <Link href={`/misconducts/create?driver_id=${driver.id}`} className="w-full">
                                                <Button variant="outline" className="w-full border-red-600 text-red-600 hover:bg-red-50">
                                                    <AlertTriangle className="mr-2 h-4 w-4" />
                                                    Report Misconduct
                                                </Button>
                                            </Link>
                                        )}

                                        {/* Edit Driver - Only for association clerks */}
                                        {userRole === 'association clerk' && (
                                            <Link href={route('drivers.edit', driver.id)} className="w-full">
                                                <Button variant="outline" className="w-full">
                                                    <Edit className="mr-2 h-4 w-4" />
                                                    Edit Driver
                                                </Button>
                                            </Link>
                                        )}

                                        {/* View History - Only for association clerks */}
                                        {userRole === 'association clerk' && (
                                            <Link href={route('drivers.history', driver.id)} className="w-full">
                                                <Button variant="outline" className="w-full">
                                                    <History className="mr-2 h-4 w-4" />
                                                    View History
                                                </Button>
                                            </Link>
                                        )}

                                        {/* Request Clearance - Only for minibus owners */}
                                        {!driver.archived && userRole === 'minibus owner' && (
                                            <Link href={route('drivers.request-clearance', driver.id)} className="w-full">
                                                <Button variant="outline" className="w-full">
                                                    <Archive className="mr-2 h-4 w-4" />
                                                    Request Clearance
                                                </Button>
                                            </Link>
                                        )}

                                        {/* View License Button - place before Clear Driver - Only for association clerks and system admins */}
                                        {driver.license && userRole && (userRole === 'association clerk' || userRole === 'system admin') && (
                                            <Button
                                                variant="outline"
                                                className="w-full justify-center bg-blue-100 text-blue-800 border-blue-300 hover:bg-blue-200"
                                                onClick={() => setLicenseModalOpen(true)}
                                            >
                                                View License
                                            </Button>
                                        )}

                                        {/* Unarchive Driver - Only for association clerks and archived drivers */}
                                        {userRole === 'association clerk' && driver.archived && (
                                            <Button
                                                variant="outline"
                                                className="w-full"
                                                onClick={() => handleAction('unarchive')}
                                            >
                                                <Archive className="mr-2 h-4 w-4" />
                                                Unarchive Driver
                                            </Button>
                                        )}

                                        {/* Clear Driver - Only for association clerks and active drivers */}
                                        {userRole === 'association clerk' && !driver.archived && (
                                            <Button variant="destructive" className="w-full" onClick={() => handleAction('clear')}>
                                                Clear Driver
                                            </Button>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader>
                                    <CardTitle>Quick Stats</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-3">
                                    <div className="flex justify-between">
                                        <span className="text-sm text-muted-foreground">Status</span>
                                        {driver.archived ? (
                                            <Badge className="bg-red-100 text-red-800 border border-red-600">Archived</Badge>
                                        ) : (
                                            <Badge className="bg-green-100 text-green-800 border border-green-600">Active</Badge>
                                        )}
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-sm text-muted-foreground">Registered</span>
                                        <span className="text-sm">
                                            {new Date(driver.created_at).toLocaleDateString()}
                                        </span>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </div>
            {/* License Modal */}
            <Dialog open={licenseModalOpen} onOpenChange={setLicenseModalOpen}>
                <DialogContent className="max-w-2xl">
                    <DialogHeader>
                        <DialogTitle className="text-center w-full">Driver's License</DialogTitle>
                        <DialogDescription className="text-center w-full">
                            {driver.license && driver.license.endsWith('.pdf')
                                ? 'Preview of the uploaded Driver License (PDF).'
                                : 'Preview of the uploaded Driver License (image).'}
                        </DialogDescription>
                    </DialogHeader>
                    {driver.license && driver.license.endsWith('.pdf') ? (
                        <iframe
                            src={`/storage/${driver.license}`}
                            title="Driver License PDF"
                            className="w-full h-[70vh] border"
                        />
                    ) : driver.license ? (
                        <img
                            src={`/storage/${driver.license}`}
                            alt="Driver License"
                            className="w-full max-h-[70vh] object-contain"
                        />
                    ) : null}
                </DialogContent>
            </Dialog>
            <ConfirmDialog
                open={confirmDialog.open}
                title={
                    confirmDialog.action === 'delete' ? `Delete ${driver.first_name} ${driver.last_name}?` :
                        confirmDialog.action === 'unarchive' ? `Unarchive ${driver.first_name} ${driver.last_name}?` :
                            confirmDialog.action === 'clear' ? `Clear ${driver.first_name} ${driver.last_name}?` :
                                'Are you sure?'
                }
                description={
                    confirmDialog.action === 'delete' ? 'This action cannot be undone and will permanently remove the driver from the system.' :
                        confirmDialog.action === 'unarchive' ? 'This driver will be restored and regain active status.' :
                            confirmDialog.action === 'clear' ? 'Please provide a reason for clearing this driver. This action will archive the driver and update their employment history.' :
                                ''
                }
                confirmText={
                    confirmDialog.action === 'delete' ? 'Delete' :
                        confirmDialog.action === 'unarchive' ? 'Unarchive' :
                            confirmDialog.action === 'clear' ? 'Clear Driver' :
                                'Confirm'
                }
                confirmVariant={confirmDialog.action === 'delete' || confirmDialog.action === 'clear' ? 'destructive' : 'default'}
                loading={loading || processing}
                onCancel={handleCancel}
                onConfirm={handleConfirm}
            >
                {confirmDialog.action === 'clear' && (
                    <form className="space-y-4 mt-4" onSubmit={e => { e.preventDefault(); handleConfirm(); }}>
                        <div>
                            <label htmlFor="reason" className="block text-sm font-medium">Reason for Clearance *</label>
                            <Textarea
                                id="reason"
                                value={data.reason}
                                onChange={e => setData('reason', e.target.value)}
                                placeholder="Enter reason..."
                                rows={4}
                                required
                            />
                            {errors.reason && <div className="text-sm text-red-500 mt-1">{errors.reason}</div>}
                        </div>
                        <div>
                            <label htmlFor="admin_notes" className="block text-sm font-medium">Administrative Notes (optional)</label>
                            <Textarea
                                id="admin_notes"
                                value={data.admin_notes}
                                onChange={e => setData('admin_notes', e.target.value)}
                                placeholder="Enter notes..."
                                rows={3}
                            />
                            {errors.admin_notes && <div className="text-sm text-red-500 mt-1">{errors.admin_notes}</div>}
                        </div>
                    </form>
                )}
            </ConfirmDialog>
        </AppLayout>
    );
}
