import{l as b,j as e,L as c,T as d,C as t,a as r,b as i,c as l,B as n,m as v}from"./app-DL-qYY5V.js";import{A as h,h as f}from"./app-layout-YqstQnqE.js";import{C as u}from"./circle-alert-B36J8eZz.js";import{D as g}from"./dollar-sign-Cx0-nQIX.js";import{U as m}from"./users-DNGXY-sJ.js";import{C as w}from"./calendar-Bzuvt9Ns.js";import{P as j,M as N}from"./phone-d7ZPSr1q.js";import"./index-1tPPImJd.js";import"./index-BTzg1GwG.js";import"./app-logo-icon-BnXlkpcX.js";import"./bus-CKTBKI0T.js";import"./clipboard-list-CxRumT8v.js";function B({payment:s,associationClerk:a}){const[y,x]=b.useState(!1),p=()=>{s.ctechpay_order_reference&&(navigator.clipboard.writeText(s.ctechpay_order_reference),x(!0),setTimeout(()=>x(!1),2e3))},o=[{title:"Dashboard",href:"/dashboard"},{title:"My Membership",href:"/my-membership"},{title:"Payment Verification",href:"#"}];return s?e.jsx(h,{title:"Payment Verification Needed",breadcrumbs:o,children:e.jsxs("div",{className:"max-w-2xl mx-auto py-8 px-4",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("div",{className:"flex justify-center mb-4",children:e.jsx("div",{className:"w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center",children:e.jsx(u,{className:"h-10 w-10 text-yellow-600"})})}),e.jsx("h1",{className:"text-3xl font-bold text-yellow-700 mb-2",children:"Payment Verification Needed"}),e.jsx("p",{className:"text-gray-600",children:"We couldn't automatically verify your payment status. Please contact MOAM staff for assistance."})]}),e.jsxs(t,{className:"mb-6 border-yellow-200 bg-yellow-50",children:[e.jsx(r,{children:e.jsxs(i,{className:"flex items-center gap-2 text-yellow-800",children:[e.jsx(u,{className:"h-5 w-5"}),"Important Information"]})}),e.jsxs(l,{className:"space-y-3",children:[e.jsx("p",{className:"text-yellow-800",children:"Your payment may have been processed successfully, but we couldn't automatically confirm the status. This sometimes happens due to technical issues with the payment gateway."}),e.jsx("p",{className:"text-yellow-800 font-medium",children:"Please contact MOAM staff with your order reference number below to verify your payment status."})]})]}),e.jsxs(t,{className:"mb-6",children:[e.jsx(r,{children:e.jsxs(i,{className:"flex items-center gap-2",children:[e.jsx(f,{className:"h-5 w-5"}),"Payment Details"]})}),e.jsxs(l,{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(g,{className:"h-5 w-5 text-gray-600"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Amount"}),e.jsxs("div",{className:"font-semibold",children:["MWK ",parseFloat(s.amount).toLocaleString()]})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(m,{className:"h-5 w-5 text-gray-600"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Fee Type"}),e.jsx("div",{className:"font-semibold capitalize",children:s.fee_type})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(f,{className:"h-5 w-5 text-gray-600"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Payment Method"}),e.jsx("div",{className:"font-semibold",children:s.payment_method==="ctechpay_card"?"Card Payment":s.payment_method})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(w,{className:"h-5 w-5 text-gray-600"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Date & Time"}),e.jsx("div",{className:"font-semibold",children:new Date(s.created_at).toLocaleString()})]})]})]}),s.ctechpay_order_reference&&e.jsx("div",{className:"pt-4 border-t border-gray-200",children:e.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[e.jsx("div",{className:"text-sm text-blue-800 font-medium mb-2",children:"Order Reference Number (Important!)"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"font-mono text-lg font-bold text-blue-900 bg-white px-3 py-2 rounded border flex-1",children:s.ctechpay_order_reference}),e.jsxs(n,{variant:"outline",size:"sm",onClick:p,className:"flex items-center gap-1",children:[e.jsx(v,{className:"h-4 w-4"}),y?"Copied!":"Copy"]})]}),e.jsx("div",{className:"text-xs text-blue-700 mt-2",children:"Provide this reference number to MOAM staff for payment verification"})]})})]})]}),e.jsxs(t,{className:"mb-6",children:[e.jsx(r,{children:e.jsx(i,{children:"Contact MOAM Staff"})}),e.jsx(l,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{className:"text-gray-600 mb-4",children:"Please contact MOAM staff using any of the methods below. Have your order reference number ready."}),a?e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-gray-50 rounded-lg",children:[e.jsx(j,{className:"h-5 w-5 text-blue-600"}),e.jsxs("div",{children:[e.jsxs("div",{className:"font-medium",children:["Phone - ",a.first_name," ",a.last_name]}),e.jsx("div",{className:"text-sm text-gray-600",children:"Call association clerk during business hours"}),e.jsx("div",{className:"text-blue-600 font-medium",children:a.phone_number})]})]}),e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-gray-50 rounded-lg",children:[e.jsx(N,{className:"h-5 w-5 text-blue-600"}),e.jsxs("div",{children:[e.jsxs("div",{className:"font-medium",children:["Email - ",a.first_name," ",a.last_name]}),e.jsx("div",{className:"text-sm text-gray-600",children:"Send an email with your order reference"}),e.jsx("a",{href:`mailto:${a.email}`,className:"text-blue-600 font-medium hover:underline",children:a.email})]})]}),e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-gray-50 rounded-lg",children:[e.jsx(m,{className:"h-5 w-5 text-blue-600"}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:"Visit Office"}),e.jsx("div",{className:"text-sm text-gray-600",children:"Visit MOAM office with your order reference number"}),e.jsx("div",{className:"text-blue-600 font-medium",children:"MOAM Office, Blantyre"})]})]})]}):e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-gray-50 rounded-lg",children:[e.jsx(j,{className:"h-5 w-5 text-blue-600"}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:"Phone"}),e.jsx("div",{className:"text-sm text-gray-600",children:"Call MOAM office during business hours"}),e.jsx("div",{className:"text-blue-600 font-medium",children:"Contact information not available"})]})]}),e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-gray-50 rounded-lg",children:[e.jsx(N,{className:"h-5 w-5 text-blue-600"}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:"Email"}),e.jsx("div",{className:"text-sm text-gray-600",children:"Send an email with your order reference"}),e.jsx("div",{className:"text-blue-600 font-medium",children:"Contact information not available"})]})]}),e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-gray-50 rounded-lg",children:[e.jsx(m,{className:"h-5 w-5 text-blue-600"}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:"Visit Office"}),e.jsx("div",{className:"text-sm text-gray-600",children:"Visit MOAM office with your order reference number"}),e.jsx("div",{className:"text-blue-600 font-medium",children:"MOAM Office, Blantyre"})]})]})]})]})})]}),e.jsxs(t,{className:"mb-6",children:[e.jsx(r,{children:e.jsx(i,{children:"What to Expect"})}),e.jsx(l,{children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5",children:e.jsx("span",{className:"text-blue-600 text-sm font-semibold",children:"1"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:"Quick Verification"}),e.jsx("div",{className:"text-sm text-gray-600",children:"MOAM staff will check your payment status using your order reference number."})]})]}),e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5",children:e.jsx("span",{className:"text-blue-600 text-sm font-semibold",children:"2"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:"Status Update"}),e.jsx("div",{className:"text-sm text-gray-600",children:"Your payment status will be updated manually once verified."})]})]}),e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5",children:e.jsx("span",{className:"text-blue-600 text-sm font-semibold",children:"3"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:"Confirmation"}),e.jsx("div",{className:"text-sm text-gray-600",children:"You'll receive confirmation once your payment is verified and processed."})]})]})]})})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[e.jsx(n,{asChild:!0,variant:"outline",children:e.jsx(c,{href:d("dashboard"),children:"Return to Dashboard"})}),e.jsx(n,{asChild:!0,children:e.jsx(c,{href:d("my.membership"),children:"View My Membership"})})]}),e.jsx("div",{className:"mt-8 text-center text-sm text-gray-500",children:e.jsx("p",{children:"This verification process ensures accurate payment tracking and prevents any issues with your membership status."})})]})}):e.jsx(h,{title:"Payment Not Found",breadcrumbs:o,children:e.jsxs("div",{className:"max-w-2xl mx-auto py-8 px-4 text-center",children:[e.jsx("h1",{className:"text-2xl font-bold text-red-600 mb-4",children:"Payment Not Found"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"The payment information could not be loaded."}),e.jsx(c,{href:d("dashboard"),className:"text-blue-600 hover:underline",children:"Return to Dashboard"})]})})}export{B as default};
