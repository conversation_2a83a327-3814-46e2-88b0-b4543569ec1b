<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\CtechpayService;
use App\Models\User;
use App\Models\Membership;
use App\Models\Payment;

class TestCtechpayIntegration extends Command
{
    protected $signature = 'ctechpay:test-integration {--order-ref= : Test specific order reference}';
    protected $description = 'Test the complete ctechpay integration';

    private $ctechpayService;

    public function __construct(CtechpayService $ctechpayService)
    {
        parent::__construct();
        $this->ctechpayService = $ctechpayService;
    }

    public function handle()
    {
        $this->info('🧪 Testing Ctechpay Integration...');
        $this->newLine();

        // Test 1: Configuration
        $this->testConfiguration();

        // Test 2: API Connectivity
        $this->testApiConnectivity();

        // Test 3: Order Creation
        if (!$this->option('order-ref')) {
            $this->testOrderCreation();
        }

        // Test 4: Status Check
        $this->testStatusCheck();

        $this->newLine();
        $this->info('✅ Integration test completed!');
    }

    private function testConfiguration()
    {
        $this->info('📋 Testing Real Ctechpay Configuration...');

        $apiUrl = config('services.ctechpay.api_url');
        $apiToken = config('services.ctechpay.token');
        $registration = config('services.ctechpay.registration');
        $environment = config('services.ctechpay.environment');

        $this->line("   API URL: {$apiUrl}");
        $this->line("   Environment: {$environment}");
        $this->line("   Registration: " . ($registration ? 'Set' : 'Not set'));
        $this->line("   Token: " . ($apiToken ? (strlen($apiToken) > 10 ? substr($apiToken, 0, 10) . '...' : $apiToken) : 'Not set'));

        if (!$apiUrl || !$apiToken || !$registration) {
            $this->error('   ❌ Missing configuration! Please set CTECHPAY_API_KEY and CTECHPAY_REGISTRATION in your .env file.');
            $this->warn('   ⚠️  You need to get these credentials from Ctechpay for your student project.');
            return false;
        }

        if ($environment !== 'production') {
            $this->warn('   ⚠️  Environment is set to: ' . $environment);
            $this->warn('   ⚠️  For real payments, set CTECHPAY_ENVIRONMENT=production in your .env file.');
        }

        $this->info('   ✅ Configuration looks good!');
        return true;
    }

    private function testApiConnectivity()
    {
        $this->info('🌐 Testing Real Ctechpay API Connectivity...');

        try {
            // Test with a real order creation request (small amount for testing)
            $testData = [
                'amount' => 1000, // MWK 1000 for testing
                'redirect_url' => route('ctechpay.return', ['payment' => 'test']),
                'cancel_url' => route('ctechpay.cancel', ['payment' => 'test']),
            ];

            $this->line('   Creating test payment order...');
            $result = $this->ctechpayService->createCardPayment($testData);

            if ($result['success']) {
                $this->info('   ✅ Real API connectivity successful!');
                $this->line("   Order Reference: {$result['order_reference']}");
                $this->line("   Payment URL: {$result['payment_page_url']}");
                $this->warn('   ⚠️  A real payment order was created. You can test it by visiting the payment URL.');
                return $result['order_reference'];
            } else {
                $this->error("   ❌ API connectivity failed: {$result['error']}");
                $this->warn('   ⚠️  This might be due to invalid credentials or API issues.');
                return false;
            }
        } catch (\Exception $e) {
            $this->error("   ❌ API connectivity exception: {$e->getMessage()}");
            $this->warn('   ⚠️  Check your internet connection and API credentials.');
            return false;
        }
    }

    private function testOrderCreation()
    {
        $this->info('📦 Testing Order Creation...');

        // Find a test user and membership
        $user = User::first();
        $membership = Membership::where('status', 'Unregistered')->first();

        if (!$user || !$membership) {
            $this->warn('   ⚠️  No test data available. Skipping order creation test.');
            return;
        }

        try {
            // Create a test payment record
            $payment = Payment::create([
                'user_id' => $user->id,
                'membership_id' => $membership->id,
                'amount' => 1000,
                'fee_type' => 'registration',
                'status' => 'pending',
                'payment_method' => 'ctechpay_card',
                'verification_method' => 'ctechpay_api',
            ]);

            $this->info("   ✅ Test payment record created (ID: {$payment->id})");

            // Test card payment
            $cardData = [
                'amount' => 1000,
                'redirect_url' => route('ctechpay.return', ['payment' => $payment->id]),
                'cancel_url' => route('ctechpay.cancel', ['payment' => $payment->id]),
            ];

            $cardResult = $this->ctechpayService->createCardPayment($cardData);

            if ($cardResult['success']) {
                $this->info('   ✅ Card payment order created successfully!');
                $this->line("   Order Reference: {$cardResult['order_reference']}");

                // Update payment record
                $payment->update([
                    'ctechpay_order_reference' => $cardResult['order_reference'],
                    'ctechpay_payment_url' => $cardResult['payment_page_url'],
                    'ctechpay_response_data' => $cardResult['response_data'],
                    'ctechpay_status' => 'PENDING',
                ]);

                $orderReference = $cardResult['order_reference'];
            } else {
                $this->error("   ❌ Card payment order creation failed: {$cardResult['error']}");
                $orderReference = null;
            }

            // Only testing card payments now
            $this->line('✅ Card payment testing completed.');

            return $orderReference;

        } catch (\Exception $e) {
            $this->error("   ❌ Order creation exception: {$e->getMessage()}");
        }
    }

    private function testStatusCheck()
    {
        $this->info('🔍 Testing Status Check...');

        $orderRef = $this->option('order-ref');

        if (!$orderRef) {
            // Try to find a recent order reference
            $payment = Payment::whereNotNull('ctechpay_order_reference')
                ->orderBy('created_at', 'desc')
                ->first();

            if ($payment) {
                $orderRef = $payment->ctechpay_order_reference;
                $this->line("   Using order reference from database: {$orderRef}");
            } else {
                $this->warn('   ⚠️  No order reference available for status check.');
                return;
            }
        }

        try {
            $result = $this->ctechpayService->checkPaymentStatus($orderRef);

            if ($result['success']) {
                $this->info('   ✅ Status check successful!');
                $this->line("   Status: {$result['status']}");

                if (isset($result['response_data'])) {
                    $data = $result['response_data'];
                    if (isset($data['currency_code'])) {
                        $this->line("   Currency: {$data['currency_code']}");
                    }
                    if (isset($data['formatted_amount'])) {
                        $this->line("   Amount: {$data['formatted_amount']}");
                    }
                    if (isset($data['card_holder_name'])) {
                        $this->line("   Card Holder: {$data['card_holder_name']}");
                    }
                }
            } else {
                $this->error("   ❌ Status check failed: {$result['error']}");

                if (isset($result['auth_failed']) && $result['auth_failed']) {
                    $this->warn('   ⚠️  This appears to be an authentication issue with the status API.');
                    $this->warn('   ⚠️  Contact Ctechpay support about this issue.');
                }
            }
        } catch (\Exception $e) {
            $this->error("   ❌ Status check exception: {$e->getMessage()}");
        }
    }
}
