<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class PaymentException extends Exception
{
    protected $paymentProvider;
    protected $paymentId;
    protected $errorCode;
    protected $userMessage;

    public function __construct(
        string $message = 'Payment processing failed',
        string $userMessage = 'There was an issue processing your payment. Please try again or contact support.',
        string $paymentProvider = null,
        string $paymentId = null,
        string $errorCode = null,
        int $code = 0,
        \Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);

        $this->userMessage = $userMessage;
        $this->paymentProvider = $paymentProvider;
        $this->paymentId = $paymentId;
        $this->errorCode = $errorCode;
    }

    /**
     * Get the user-friendly message
     */
    public function getUserMessage(): string
    {
        return $this->userMessage;
    }

    /**
     * Get the payment provider
     */
    public function getPaymentProvider(): ?string
    {
        return $this->paymentProvider;
    }

    /**
     * Get the payment ID
     */
    public function getPaymentId(): ?string
    {
        return $this->paymentId;
    }

    /**
     * Get the error code
     */
    public function getErrorCode(): ?string
    {
        return $this->errorCode;
    }

    /**
     * Render the exception into an HTTP response.
     */
    public function render(Request $request): Response|\Illuminate\Http\JsonResponse
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'Payment Error',
                'message' => $this->getUserMessage(),
                'payment_provider' => $this->getPaymentProvider(),
                'payment_id' => $this->getPaymentId(),
                'error_code' => $this->getErrorCode(),
            ], 422);
        }

        return response()->view('errors.payment', [
            'message' => $this->getUserMessage(),
            'paymentProvider' => $this->getPaymentProvider(),
            'paymentId' => $this->getPaymentId(),
        ], 422);
    }

    /**
     * Report the exception.
     */
    public function report(): void
    {
        \Log::error('Payment Exception', [
            'message' => $this->getMessage(),
            'user_message' => $this->getUserMessage(),
            'payment_provider' => $this->getPaymentProvider(),
            'payment_id' => $this->getPaymentId(),
            'error_code' => $this->getErrorCode(),
            'user_id' => auth()->id(),
            'trace' => $this->getTraceAsString(),
        ]);
    }

    /**
     * Create a payment initiation failure exception
     */
    public static function initiationFailed(
        string $provider,
        string $reason = 'Payment initiation failed'
    ): self {
        return new self(
            "Payment initiation failed for {$provider}: {$reason}",
            'Unable to start payment process. Please try again or use a different payment method.',
            $provider,
            null,
            'INITIATION_FAILED'
        );
    }

    /**
     * Create a payment verification failure exception
     */
    public static function verificationFailed(
        string $provider,
        string $paymentId,
        string $reason = 'Payment verification failed'
    ): self {
        return new self(
            "Payment verification failed for {$provider} payment {$paymentId}: {$reason}",
            'Unable to verify payment status. Please contact support with your payment reference.',
            $provider,
            $paymentId,
            'VERIFICATION_FAILED'
        );
    }

    /**
     * Create a payment processing failure exception
     */
    public static function processingFailed(
        string $provider,
        string $paymentId,
        string $reason = 'Payment processing failed'
    ): self {
        return new self(
            "Payment processing failed for {$provider} payment {$paymentId}: {$reason}",
            'Payment could not be processed. Please try again or contact support.',
            $provider,
            $paymentId,
            'PROCESSING_FAILED'
        );
    }

    /**
     * Create a webhook processing failure exception
     */
    public static function webhookFailed(
        string $provider,
        string $reason = 'Webhook processing failed'
    ): self {
        return new self(
            "Webhook processing failed for {$provider}: {$reason}",
            'Payment notification processing failed. Your payment status will be updated shortly.',
            $provider,
            null,
            'WEBHOOK_FAILED'
        );
    }
}
