const Ziggy = {"url":"http:\/\/127.0.0.1:8000","port":8000,"defaults":{},"routes":{"home":{"uri":"\/","methods":["GET","HEAD"]},"ctechpay.mock":{"uri":"ctechpay\/redirect\/{orderReference}","methods":["GET","HEAD"],"parameters":["orderReference"]},"payments.ctechpay.return":{"uri":"payments\/ctechpay\/return","methods":["GET","HEAD"]},"makePayment.store":{"uri":"makePayment","methods":["POST"]},"payments.ctechpay.initiate":{"uri":"payments\/ctechpay\/initiate","methods":["POST"]},"payments.verify-manually":{"uri":"payments\/{payment}\/verify-manually","methods":["POST"],"parameters":["payment"],"bindings":{"payment":"id"}},"test.payment":{"uri":"test-payment","methods":["POST"]},"test.payment.result":{"uri":"test-payment-result","methods":["GET","HEAD"]},"test.changes":{"uri":"test-changes","methods":["GET","HEAD"]},"test.return":{"uri":"test-return\/{orderRef}","methods":["GET","HEAD"],"parameters":["orderRef"]},"test.email":{"uri":"test-email","methods":["GET","HEAD"]},"test.roles":{"uri":"test-roles","methods":["GET","HEAD"]},"test.payment.failure":{"uri":"test-payment-failure\/{orderRef}","methods":["GET","HEAD"],"parameters":["orderRef"]},"test.direct.failure":{"uri":"test-direct-failure","methods":["GET","HEAD"]},"notification.redirect":{"uri":"notification-redirect\/{type}\/{id}","methods":["GET","HEAD"],"parameters":["type","id"]},"dashboard":{"uri":"dashboard","methods":["GET","HEAD"]},"fee-settings.history":{"uri":"fee-settings\/history\/{feeType}","methods":["GET","HEAD"],"parameters":["feeType"]},"fee-settings.index":{"uri":"fee-settings","methods":["GET","HEAD"]},"fee-settings.create":{"uri":"fee-settings\/create","methods":["GET","HEAD"]},"fee-settings.store":{"uri":"fee-settings","methods":["POST"]},"fee-settings.show":{"uri":"fee-settings\/{fee_setting}","methods":["GET","HEAD"],"parameters":["fee_setting"]},"fee-settings.edit":{"uri":"fee-settings\/{fee_setting}\/edit","methods":["GET","HEAD"],"parameters":["fee_setting"]},"fee-settings.update":{"uri":"fee-settings\/{fee_setting}","methods":["PUT","PATCH"],"parameters":["fee_setting"]},"fee-settings.destroy":{"uri":"fee-settings\/{fee_setting}","methods":["DELETE"],"parameters":["fee_setting"]},"admin.users":{"uri":"admin\/users","methods":["GET","HEAD"]},"admin.users.show":{"uri":"admin\/users\/{user}","methods":["GET","HEAD"],"parameters":["user"],"bindings":{"user":"id"}},"admin.users.edit":{"uri":"admin\/users\/{user}\/edit","methods":["GET","HEAD"],"parameters":["user"],"bindings":{"user":"id"}},"admin.users.update":{"uri":"admin\/users\/{user}","methods":["PUT"],"parameters":["user"],"bindings":{"user":"id"}},"admin.users.patch":{"uri":"admin\/users\/{user}","methods":["PATCH"],"parameters":["user"],"bindings":{"user":"id"}},"admin.users.destroy":{"uri":"admin\/users\/{user}","methods":["DELETE"],"parameters":["user"]},"admin.createUser":{"uri":"admin\/create-user","methods":["GET","HEAD"]},"admin.storeUser":{"uri":"admin\/create-user","methods":["POST"]},"admin.users.unpaidMemberships":{"uri":"admin\/users\/{user}\/unpaid-memberships","methods":["GET","HEAD"],"parameters":["user"]},"admin.users.membershipSummary":{"uri":"admin\/users\/{user}\/membership-summary","methods":["GET","HEAD"],"parameters":["user"]},"memberships.markPaid":{"uri":"memberships\/{membership}\/mark-paid","methods":["PUT"],"parameters":["membership"]},"users":{"uri":"users","methods":["GET","HEAD"]},"routes.index":{"uri":"routes","methods":["GET","HEAD"]},"routes.store":{"uri":"routes","methods":["POST"]},"routes.update":{"uri":"routes\/{route}\/update","methods":["POST"],"parameters":["route"],"bindings":{"route":"id"}},"routes.destroy":{"uri":"routes\/{route}\/delete","methods":["POST"],"parameters":["route"],"bindings":{"route":"id"}},"routes.deactivate":{"uri":"routes\/{route}\/deactivate","methods":["POST"],"parameters":["route"],"bindings":{"route":"id"}},"routes.activate":{"uri":"routes\/{route}\/activate","methods":["POST"],"parameters":["route"],"bindings":{"route":"id"}},"routes.export":{"uri":"routes\/export","methods":["GET","HEAD"]},"payments.ctechpay.cancel":{"uri":"payments\/ctechpay\/cancel","methods":["GET","HEAD"]},"admin.users.archive":{"uri":"admin\/users\/{user}\/archive","methods":["PUT"],"parameters":["user"],"bindings":{"user":"id"}},"admin.users.unarchive":{"uri":"admin\/users\/{user}\/unarchive","methods":["PUT"],"parameters":["user"],"bindings":{"user":"id"}},"drivers.index":{"uri":"drivers","methods":["GET","HEAD"]},"drivers.create":{"uri":"drivers\/create","methods":["GET","HEAD"]},"drivers.store":{"uri":"drivers","methods":["POST"]},"drivers.show":{"uri":"drivers\/{driver}","methods":["GET","HEAD"],"parameters":["driver"],"bindings":{"driver":"id"}},"drivers.edit":{"uri":"drivers\/{driver}\/edit","methods":["GET","HEAD"],"parameters":["driver"],"bindings":{"driver":"id"}},"drivers.update":{"uri":"drivers\/{driver}","methods":["PUT","PATCH"],"parameters":["driver"],"bindings":{"driver":"id"}},"drivers.destroy":{"uri":"drivers\/{driver}","methods":["DELETE"],"parameters":["driver"],"bindings":{"driver":"id"}},"drivers.unarchive":{"uri":"drivers\/{driver}\/unarchive","methods":["PATCH"],"parameters":["driver"],"bindings":{"driver":"id"}},"drivers.direct-clear":{"uri":"drivers\/{driver}\/direct-clear","methods":["PATCH"],"parameters":["driver"],"bindings":{"driver":"id"}},"drivers.request-clearance":{"uri":"drivers\/{driver}\/request-clearance","methods":["GET","HEAD"],"parameters":["driver"],"bindings":{"driver":"id"}},"drivers.store-clearance-request":{"uri":"drivers\/{driver}\/request-clearance","methods":["POST"],"parameters":["driver"],"bindings":{"driver":"id"}},"drivers.clearance-requests.index":{"uri":"drivers\/clearance-requests","methods":["GET","HEAD"]},"drivers.clearance-requests.show":{"uri":"drivers\/clearance-requests\/{clearanceRequest}","methods":["GET","HEAD"],"parameters":["clearanceRequest"],"bindings":{"clearanceRequest":"id"}},"drivers.clearance-requests.process":{"uri":"drivers\/clearance-requests\/{clearanceRequest}\/process","methods":["PATCH"],"parameters":["clearanceRequest"],"bindings":{"clearanceRequest":"id"}},"drivers.clearance-requests.destroy":{"uri":"drivers\/clearance-requests\/{clearanceRequest}","methods":["DELETE"],"parameters":["clearanceRequest"],"bindings":{"clearanceRequest":"id"}},"drivers.history":{"uri":"drivers\/{driver}\/history","methods":["GET","HEAD"],"parameters":["driver"],"bindings":{"driver":"id"}},"minibuses.transfer-requests.index":{"uri":"minibuses\/transfer-requests","methods":["GET","HEAD"]},"minibuses.transfer-requests.show":{"uri":"minibuses\/transfer-requests\/{transferRequest}","methods":["GET","HEAD"],"parameters":["transferRequest"]},"minibuses.transfer-requests.destroy":{"uri":"minibuses\/transfer-requests\/{transferRequest}","methods":["DELETE"],"parameters":["transferRequest"]},"minibuses.index":{"uri":"minibuses","methods":["GET","HEAD"]},"minibuses.create":{"uri":"minibuses\/create","methods":["GET","HEAD"]},"minibuses.store":{"uri":"minibuses","methods":["POST"]},"minibuses.show":{"uri":"minibuses\/{minibus}","methods":["GET","HEAD"],"parameters":["minibus"],"bindings":{"minibus":"id"}},"minibuses.edit":{"uri":"minibuses\/{minibus}\/edit","methods":["GET","HEAD"],"parameters":["minibus"],"bindings":{"minibus":"id"}},"minibuses.update":{"uri":"minibuses\/{minibus}","methods":["PUT"],"parameters":["minibus"],"bindings":{"minibus":"id"}},"minibuses.destroy":{"uri":"minibuses\/{minibus}","methods":["DELETE"],"parameters":["minibus"],"bindings":{"minibus":"id"}},"minibuses.archive":{"uri":"minibuses\/{minibus}\/archive","methods":["POST"],"parameters":["minibus"],"bindings":{"minibus":"id"}},"minibuses.unarchive":{"uri":"minibuses\/{minibus}\/unarchive","methods":["POST"],"parameters":["minibus"],"bindings":{"minibus":"id"}},"minibuses.transfer":{"uri":"minibuses\/{minibus}\/transfer","methods":["GET","HEAD"],"parameters":["minibus"],"bindings":{"minibus":"id"}},"minibuses.transfer.process":{"uri":"minibuses\/{minibus}\/transfer","methods":["POST"],"parameters":["minibus"],"bindings":{"minibus":"id"}},"minibuses.transfer.request":{"uri":"minibuses\/{minibus}\/transfer\/request","methods":["GET","HEAD"],"parameters":["minibus"],"bindings":{"minibus":"id"}},"minibuses.transfer.request.store":{"uri":"minibuses\/{minibus}\/transfer\/request","methods":["POST"],"parameters":["minibus"],"bindings":{"minibus":"id"}},"minibuses.history":{"uri":"minibuses\/{minibus}\/history","methods":["GET","HEAD"],"parameters":["minibus"],"bindings":{"minibus":"id"}},"minibuses.history.page":{"uri":"minibuses\/{minibus}\/history-page","methods":["GET","HEAD"],"parameters":["minibus"],"bindings":{"minibus":"id"}},"misconducts.index":{"uri":"misconducts","methods":["GET","HEAD"]},"misconducts.show":{"uri":"misconducts\/{misconduct}","methods":["GET","HEAD"],"parameters":["misconduct"],"bindings":{"misconduct":"id"}},"users.misconducts":{"uri":"users\/{user}\/misconducts","methods":["GET","HEAD"],"parameters":["user"],"bindings":{"user":"id"}},"drivers.misconducts":{"uri":"drivers\/{driver}\/misconducts","methods":["GET","HEAD"],"parameters":["driver"],"bindings":{"driver":"id"}},"misconducts.create":{"uri":"misconducts\/create","methods":["GET","HEAD"]},"misconducts.store":{"uri":"misconducts","methods":["POST"]},"misconducts.edit":{"uri":"misconducts\/{misconduct}\/edit","methods":["GET","HEAD"],"parameters":["misconduct"],"bindings":{"misconduct":"id"}},"misconducts.update":{"uri":"misconducts\/{misconduct}","methods":["PUT"],"parameters":["misconduct"],"bindings":{"misconduct":"id"}},"misconducts.destroy":{"uri":"misconducts\/{misconduct}","methods":["DELETE"],"parameters":["misconduct"],"bindings":{"misconduct":"id"}},"misconduct-analytics.index":{"uri":"misconduct-analytics","methods":["GET","HEAD"]},"misconduct-analytics.report":{"uri":"misconduct-analytics\/report","methods":["GET","HEAD"]},"misconducts.update-resolution":{"uri":"misconducts\/{misconduct}\/resolution","methods":["PATCH"],"parameters":["misconduct"],"bindings":{"misconduct":"id"}},"misconducts.update-severity":{"uri":"misconducts\/{misconduct}\/severity","methods":["PATCH"],"parameters":["misconduct"],"bindings":{"misconduct":"id"}},"password.edit":{"uri":"settings\/password","methods":["GET","HEAD"]},"password.update":{"uri":"settings\/password","methods":["PUT"]},"appearance":{"uri":"settings\/appearance","methods":["GET","HEAD"]},"login":{"uri":"login","methods":["GET","HEAD"]},"password.request":{"uri":"forgot-password","methods":["GET","HEAD"]},"password.email":{"uri":"forgot-password","methods":["POST"]},"password.reset":{"uri":"reset-password\/{token}","methods":["GET","HEAD"],"parameters":["token"]},"password.store":{"uri":"reset-password","methods":["POST"]},"verification.notice":{"uri":"verify-email","methods":["GET","HEAD"]},"verification.verify":{"uri":"verify-email\/{id}\/{hash}","methods":["GET","HEAD"],"parameters":["id","hash"]},"verification.send":{"uri":"email\/verification-notification","methods":["POST"]},"password.confirm":{"uri":"confirm-password","methods":["GET","HEAD"]},"logout":{"uri":"logout","methods":["POST"]},"membership.management":{"uri":"membership-management","methods":["GET","HEAD"]},"membership.management.create":{"uri":"membership-management\/create","methods":["GET","HEAD"]},"membership.management.store":{"uri":"membership-management\/create","methods":["POST"]},"membership.management.summary":{"uri":"membership-management\/{user}","methods":["GET","HEAD"],"parameters":["user"]},"membership.management.edit":{"uri":"membership-management\/{user}\/edit","methods":["GET","HEAD"],"parameters":["user"],"bindings":{"user":"id"}},"membership.management.update":{"uri":"membership-management\/{user}","methods":["PATCH"],"parameters":["user"],"bindings":{"user":"id"}},"memberships.destroy":{"uri":"memberships\/{membership}","methods":["DELETE"],"parameters":["membership"],"bindings":{"membership":"id"}},"my.membership":{"uri":"my-membership","methods":["GET","HEAD"]},"storage.local":{"uri":"storage\/{path}","methods":["GET","HEAD"],"wheres":{"path":".*"},"parameters":["path"]}}};
if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy };
