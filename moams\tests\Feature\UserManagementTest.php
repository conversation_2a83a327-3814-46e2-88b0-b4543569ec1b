<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class UserManagementTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        // Create roles
        foreach ([
            'association manager',
            'association clerk',
            'minibus owner',
            'system admin',
        ] as $role) {
            Role::firstOrCreate(['name' => $role]);
        }
    }

    public function test_minibus_owner_requires_joining_date()
    {
        $admin = User::factory()->create();
        $admin->assignRole('system admin');
        $this->actingAs($admin);

        $response = $this->post('/admin/create-user', [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'gender' => 'male',
            'district' => 'Lilongwe',
            'village' => 'Area 25',
            'email' => '<EMAIL>',
            'phone_number' => '0999123456',
            'password' => 'password',
            'password_confirmation' => 'password',
            'role' => 'minibus owner',
            'commitment_statement' => 'I commit...',
            // 'joining_date' => missing on purpose
        ]);

        $response->assertSessionHasErrors(['joining_date']);
        $this->assertDatabaseMissing('users', ['email' => '<EMAIL>']);
    }

    public function test_minibus_owner_can_be_created_with_joining_date()
    {
        $admin = User::factory()->create();
        $admin->assignRole('system admin');
        $this->actingAs($admin);

        $response = $this->post('/admin/create-user', [
            'first_name' => 'Jane',
            'last_name' => 'Smith',
            'gender' => 'female',
            'district' => 'Blantyre',
            'village' => 'Ndirande',
            'email' => '<EMAIL>',
            'phone_number' => '0888123456',
            'password' => 'password',
            'password_confirmation' => 'password',
            'role' => 'minibus owner',
            'commitment_statement' => 'I commit...',
            'joining_date' => now()->toDateString(),
        ]);

        $response->assertSessionHasNoErrors();
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertDatabaseHas('members', [
            'joining_date' => now()->toDateString(),
            'user_id' => $user->id,
        ]);
    }

    public function test_other_roles_do_not_require_joining_date()
    {
        $admin = User::factory()->create();
        $admin->assignRole('system admin');
        $this->actingAs($admin);

        $response = $this->post('/admin/create-user', [
            'first_name' => 'Clerk',
            'last_name' => 'User',
            'gender' => 'male',
            'district' => 'Mzuzu',
            'village' => 'Chibavi',
            'email' => '<EMAIL>',
            'phone_number' => '0999988776',
            'password' => 'password',
            'password_confirmation' => 'password',
            'role' => 'association clerk',
        ]);

        $response->assertSessionHasNoErrors();
    }
}