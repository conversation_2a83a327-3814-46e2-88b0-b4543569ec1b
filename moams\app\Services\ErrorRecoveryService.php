<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Throwable;
use Closure;

class ErrorRecoveryService
{
    /**
     * Retry a callback with exponential backoff
     */
    public static function retry(
        Closure $callback,
        int $maxAttempts = 3,
        int $baseDelay = 1000,
        array $retryableExceptions = []
    ) {
        $attempt = 1;

        while ($attempt <= $maxAttempts) {
            try {
                return $callback();
            } catch (Throwable $exception) {
                // Check if this exception is retryable
                if (!self::isRetryableException($exception, $retryableExceptions)) {
                    throw $exception;
                }

                // Don't retry on the last attempt
                if ($attempt === $maxAttempts) {
                    throw $exception;
                }

                // Calculate delay with exponential backoff
                $delay = $baseDelay * pow(2, $attempt - 1);

                Log::warning("Retrying operation after failure", [
                    'attempt' => $attempt,
                    'max_attempts' => $maxAttempts,
                    'delay_ms' => $delay,
                    'exception' => get_class($exception),
                    'message' => $exception->getMessage(),
                ]);

                // Wait before retrying
                usleep($delay * 1000); // Convert to microseconds

                $attempt++;
            }
        }
    }

    /**
     * Check if an exception is retryable
     */
    private static function isRetryableException(Throwable $exception, array $retryableExceptions): bool
    {
        // Default retryable exceptions
        $defaultRetryable = [
            \Illuminate\Database\QueryException::class,
            \GuzzleHttp\Exception\ConnectException::class,
            \GuzzleHttp\Exception\RequestException::class,
            \Illuminate\Http\Client\ConnectionException::class,
            \Illuminate\Http\Client\RequestException::class,
        ];

        $retryableExceptions = array_merge($defaultRetryable, $retryableExceptions);

        foreach ($retryableExceptions as $retryableClass) {
            if ($exception instanceof $retryableClass) {
                return true;
            }
        }

        // Check for specific error messages that indicate temporary issues
        $retryableMessages = [
            'connection timeout',
            'connection refused',
            'temporary failure',
            'service unavailable',
            'too many connections',
            'deadlock',
            'lock wait timeout',
        ];

        $message = strtolower($exception->getMessage());
        foreach ($retryableMessages as $retryableMessage) {
            if (str_contains($message, $retryableMessage)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Execute with circuit breaker pattern
     */
    public static function circuitBreaker(
        string $serviceName,
        Closure $callback,
        int $failureThreshold = 5,
        int $timeoutSeconds = 60
    ) {
        $cacheKey = "circuit_breaker:{$serviceName}";
        $circuitState = Cache::get($cacheKey, ['state' => 'closed', 'failures' => 0, 'last_failure' => null]);

        // Check if circuit is open
        if ($circuitState['state'] === 'open') {
            $timeSinceLastFailure = time() - $circuitState['last_failure'];

            if ($timeSinceLastFailure < $timeoutSeconds) {
                throw new \Exception("Circuit breaker is open for service: {$serviceName}. Try again later.");
            } else {
                // Move to half-open state
                $circuitState['state'] = 'half-open';
                Cache::put($cacheKey, $circuitState, now()->addMinutes(10));
            }
        }

        try {
            $result = $callback();

            // Success - reset circuit breaker
            if ($circuitState['state'] !== 'closed') {
                Cache::put($cacheKey, ['state' => 'closed', 'failures' => 0, 'last_failure' => null], now()->addMinutes(10));
                Log::info("Circuit breaker reset for service: {$serviceName}");
            }

            return $result;

        } catch (Throwable $exception) {
            // Increment failure count
            $circuitState['failures']++;
            $circuitState['last_failure'] = time();

            // Open circuit if threshold reached
            if ($circuitState['failures'] >= $failureThreshold) {
                $circuitState['state'] = 'open';
                Log::warning("Circuit breaker opened for service: {$serviceName}", [
                    'failures' => $circuitState['failures'],
                    'threshold' => $failureThreshold,
                ]);
            }

            Cache::put($cacheKey, $circuitState, now()->addMinutes(10));
            throw $exception;
        }
    }

    /**
     * Execute with fallback mechanism
     */
    public static function withFallback(Closure $primary, Closure $fallback, array $fallbackExceptions = [])
    {
        try {
            return $primary();
        } catch (Throwable $exception) {
            // Check if we should use fallback for this exception
            if (self::shouldUseFallback($exception, $fallbackExceptions)) {
                Log::info("Using fallback mechanism", [
                    'exception' => get_class($exception),
                    'message' => $exception->getMessage(),
                ]);

                return $fallback();
            }

            throw $exception;
        }
    }

    /**
     * Check if fallback should be used for this exception
     */
    private static function shouldUseFallback(Throwable $exception, array $fallbackExceptions): bool
    {
        // Default exceptions that should trigger fallback
        $defaultFallbackExceptions = [
            \Illuminate\Database\QueryException::class,
            \GuzzleHttp\Exception\ConnectException::class,
            \Illuminate\Http\Client\ConnectionException::class,
        ];

        $fallbackExceptions = array_merge($defaultFallbackExceptions, $fallbackExceptions);

        foreach ($fallbackExceptions as $fallbackClass) {
            if ($exception instanceof $fallbackClass) {
                return true;
            }
        }

        return false;
    }

    /**
     * Database transaction with retry
     */
    public static function transactionWithRetry(Closure $callback, int $maxAttempts = 3)
    {
        return self::retry(function () use ($callback) {
            return DB::transaction($callback);
        }, $maxAttempts, 100, [
            \Illuminate\Database\QueryException::class,
        ]);
    }

    /**
     * HTTP request with retry and circuit breaker
     */
    public static function httpRequestWithRecovery(
        string $serviceName,
        Closure $httpRequest,
        Closure $fallback = null,
        int $maxRetries = 3
    ) {
        return self::circuitBreaker($serviceName, function () use ($httpRequest, $fallback, $maxRetries) {
            if ($fallback) {
                return self::withFallback(
                    function () use ($httpRequest, $maxRetries) {
                        return self::retry($httpRequest, $maxRetries, 1000, [
                            \GuzzleHttp\Exception\ConnectException::class,
                            \GuzzleHttp\Exception\RequestException::class,
                            \Illuminate\Http\Client\ConnectionException::class,
                        ]);
                    },
                    $fallback
                );
            } else {
                return self::retry($httpRequest, $maxRetries, 1000, [
                    \GuzzleHttp\Exception\ConnectException::class,
                    \GuzzleHttp\Exception\RequestException::class,
                    \Illuminate\Http\Client\ConnectionException::class,
                ]);
            }
        });
    }

    /**
     * File operation with retry
     */
    public static function fileOperationWithRetry(Closure $fileOperation, int $maxAttempts = 3)
    {
        return self::retry($fileOperation, $maxAttempts, 500, [
            \Illuminate\Contracts\Filesystem\FileNotFoundException::class,
            \RuntimeException::class,
        ]);
    }

    /**
     * Cache operation with fallback
     */
    public static function cacheWithFallback(string $key, Closure $fallback, int $ttl = 3600)
    {
        try {
            $value = Cache::get($key);
            if ($value !== null) {
                return $value;
            }

            $value = $fallback();
            Cache::put($key, $value, now()->addSeconds($ttl));

            return $value;

        } catch (Throwable $exception) {
            Log::warning("Cache operation failed, using fallback", [
                'key' => $key,
                'exception' => get_class($exception),
                'message' => $exception->getMessage(),
            ]);

            return $fallback();
        }
    }

    /**
     * Get circuit breaker status for monitoring
     */
    public static function getCircuitBreakerStatus(string $serviceName): array
    {
        $cacheKey = "circuit_breaker:{$serviceName}";
        return Cache::get($cacheKey, ['state' => 'closed', 'failures' => 0, 'last_failure' => null]);
    }

    /**
     * Reset circuit breaker manually
     */
    public static function resetCircuitBreaker(string $serviceName): void
    {
        $cacheKey = "circuit_breaker:{$serviceName}";
        Cache::put($cacheKey, ['state' => 'closed', 'failures' => 0, 'last_failure' => null], now()->addMinutes(10));
        Log::info("Circuit breaker manually reset for service: {$serviceName}");
    }
}
