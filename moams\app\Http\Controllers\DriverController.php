<?php

namespace App\Http\Controllers;

use App\Models\Driver;
use App\Models\User;
use App\Http\Requests\StoreDriverRequest;
use App\Http\Requests\UpdateDriverRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Illuminate\Support\Facades\Storage;

class DriverController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        $userRole = $user->roles->first()->name ?? null;

        $query = Driver::with('minibusOwner');

        // Filter based on user role
        if ($userRole === 'minibus owner') {
            $query->where('owner_id', $user->id)
                ->where('archived', false); // Only show active drivers for minibus owners
        }

        // Handle search
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                    ->orWhere('last_name', 'like', "%{$search}%")
                    ->orWhere('phone_number', 'like', "%{$search}%")
                    ->orWhere('district', 'like', "%{$search}%")
                    ->orWhere('village_town', 'like', "%{$search}%")
                    ->orWhereRaw("CONCAT(first_name, ' ', last_name) like ?", ["%{$search}%"]);
            });
        }

        // Handle status filter (only for association clerks)
        if ($userRole === 'association clerk' && $request->has('status') && $request->status !== 'all') {
            if ($request->status === 'active') {
                $query->where('archived', false);
            } elseif ($request->status === 'archived') {
                $query->where('archived', true);
            }
        }

        $drivers = $query->orderBy('created_at', 'desc')->paginate(10);

        // Transform the paginated data
        $drivers->getCollection()->transform(function ($driver) {
            return array_merge(
                $driver->toArray(),
                [
                    'minibus_owner' => $driver->minibusOwner
                        ? [
                            'id' => $driver->minibusOwner->id,
                            'user' => [
                                'first_name' => $driver->minibusOwner->first_name,
                                'last_name' => $driver->minibusOwner->last_name,
                            ],
                        ]
                        : null
                ]
            );
        });

        // Get clearance requests for association clerks
        $clearanceRequests = null;
        if ($userRole === 'association clerk') {
            $clearanceRequests = \App\Models\DriverClearanceRequest::with(['driver', 'owner', 'processedBy'])
                ->orderBy('created_at', 'desc')
                ->get();
        }

        return Inertia::render('driverManagement/index-driver', [
            'drivers' => $drivers,
            'clearanceRequests' => $clearanceRequests,
            'userRole' => $userRole,
            'auth' => [
                'user' => $user,
            ],
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $user = auth()->user();
        $userRole = $user->roles->first()->name ?? null;

        if (!in_array($userRole, ['minibus owner', 'association clerk'])) {
            abort(403, 'You do not have permission to create drivers.');
        }

        return Inertia::render('driverManagement/create-driver', [
            'userRole' => $userRole,
            'auth' => [
                'user' => $user,
            ],
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreDriverRequest $request)
    {
        $user = auth()->user();
        $userRole = $user->roles->first()->name ?? null;

        if (!in_array($userRole, ['minibus owner', 'association clerk'])) {
            abort(403, 'You do not have permission to create drivers.');
        }

        try {
            DB::transaction(function () use ($request, $user, $userRole) {
                $driverData = $request->validated();

                if ($userRole === 'minibus owner') {
                    $driverData['owner_id'] = $user->id;
                } else {
                    $driverData['owner_id'] = $request->owner_id;
                }

                // Handle license file upload
                if ($request->hasFile('license')) {
                    $file = $request->file('license');
                    $hash = md5_file($file->getRealPath());
                    $existing = Driver::where('license', 'like', "%$hash%")
                        ->first();
                    if ($existing) {
                        $driverData['license'] = $existing->license;
                    } else {
                        $filename = $hash . '.' . $file->getClientOriginalExtension();
                        $path = $file->storeAs('licenses', $filename, 'public');
                        $driverData['license'] = $path;
                    }
                }

                $driver = Driver::create($driverData);

                \App\Models\DriverEmploymentHistory::create([
                    'driver_id' => $driver->id,
                    'previous_owner_id' => null,
                    'new_owner_id' => $driver->owner_id,
                    'employment_change_type' => 'hired',
                    'status' => 'active',
                    'reason' => 'Initial hire',
                    'employment_start_date' => now(),
                    'employment_end_date' => null,
                ]);
            });

            return redirect('/drivers')
                ->with('success', 'Driver created successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to create driver. Please try again.');
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Driver $driver)
    {
        $user = auth()->user();
        $userRole = $user->roles->first()->name ?? null;

        if ($userRole === 'minibus owner' && $driver->owner_id !== $user->id) {
            abort(403, 'You can only view your own drivers.');
        }

        $driver->load('minibusOwner');
        $employmentHistories = $driver->employmentHistories()
            ->with(['previousOwner', 'newOwner'])
            ->orderByDesc('employment_start_date')
            ->get()
            ->map(function ($history) {
                return [
                    'id' => $history->id,
                    'employment_change_type' => $history->employment_change_type,
                    'status' => $history->status,
                    'reason' => $history->reason,
                    'employment_start_date' => $history->employment_start_date?->toDateString(),
                    'employment_end_date' => $history->employment_end_date?->toDateString(),
                    'previous_owner' => $history->previousOwner
                        ? [
                            'id' => $history->previousOwner->id,
                            'first_name' => $history->previousOwner->first_name,
                            'last_name' => $history->previousOwner->last_name,
                        ] : null,
                    'new_owner' => $history->newOwner
                        ? [
                            'id' => $history->newOwner->id,
                            'first_name' => $history->newOwner->first_name,
                            'last_name' => $history->newOwner->last_name,
                        ] : null,
                ];
            });

        return Inertia::render('driverManagement/show-driver', [
            'driver' => array_merge(
                $driver->toArray(),
                [
                    'minibus_owner' => $driver->minibusOwner
                        ? [
                            'id' => $driver->minibusOwner->id,
                            'user' => [
                                'first_name' => $driver->minibusOwner->first_name,
                                'last_name' => $driver->minibusOwner->last_name,
                            ],
                        ]
                        : null
                ]
            ),
            'employmentHistories' => $employmentHistories,
            'userRole' => $userRole,
            'auth' => [
                'user' => $user,
            ],
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Driver $driver)
    {
        $user = auth()->user();
        $userRole = $user->roles->first()->name ?? null;

        if ($userRole !== 'association clerk') {
            abort(403, 'Only association clerks can edit drivers.');
        }

        $driver->load('minibusOwner');

        $currentOwner = $driver->minibusOwner
            ? [
                'id' => (string) $driver->minibusOwner->id,
                'name' => $driver->minibusOwner->first_name . ' ' . $driver->minibusOwner->last_name,
                'email' => $driver->minibusOwner->email,
            ]
            : null;

        return Inertia::render('driverManagement/edit-driver', [
            'driver' => $driver,
            'currentOwner' => $currentOwner,
            'userRole' => $userRole,
            'auth' => [
                'user' => $user,
            ],
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateDriverRequest $request, Driver $driver)
    {
        $user = auth()->user();
        $userRole = $user->roles->first()->name ?? null;

        // Only association clerks can update drivers
        if ($userRole !== 'association clerk') {
            abort(403, 'Only association clerks can update drivers.');
        }

        try {
            DB::transaction(function () use ($request, $driver) {
                $driverData = $request->validated();
                // Handle license file upload
                if ($request->hasFile('license')) {
                    $file = $request->file('license');
                    $path = $file->store('licenses', 'public');
                    $driverData['license'] = $path;
                }
                $driver->update($driverData);
            });

            return redirect()->route('drivers.show', $driver->id)
                ->with('success', 'Driver updated successfully!');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update driver. Please try again.');
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Driver $driver)
    {
        $user = auth()->user();
        $userRole = $user->roles->first()->name ?? null;

        // Only association clerks can delete drivers
        if ($userRole !== 'association clerk') {
            abort(403, 'Only association clerks can delete drivers.');
        }

        try {
            $driver->delete();
            return redirect('/drivers')
                ->with('success', 'Driver deleted successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to delete driver. Please try again.');
        }
    }

    /**
     * Unarchive a driver (for association clerks)
     */
    public function unarchive(Driver $driver)
    {
        $user = auth()->user();
        $userRole = $user->roles->first()->name ?? null;

        // Only association clerks can unarchive drivers
        if ($userRole !== 'association clerk') {
            abort(403, 'Only association clerks can unarchive drivers.');
        }

        try {
            $driver->update(['archived' => false]);
            return redirect('/drivers')
                ->with('success', 'Driver unarchived successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to unarchive driver. Please try again.');
        }
    }
}
