<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use App\Models\User;
use App\Models\Payment;
use App\Notifications\PaymentSuccessNotification;

class TestEmail extends Command
{
    protected $signature = 'test:email';
    protected $description = 'Test email functionality';

    public function handle()
    {
        $this->info('Testing email functionality...');

        try {
            // Test basic email sending
            $this->info('1. Testing basic email...');
            Mail::raw('This is a test email from MOAMS', function($message) {
                $message->to('<EMAIL>')
                        ->subject('MOAMS Test Email');
            });
            $this->info('✓ Basic email sent successfully');

            // Test notification email
            $this->info('2. Testing payment notification...');
            
            // Get a user and payment for testing
            $user = User::first();
            if (!$user) {
                $this->error('No users found in database');
                return;
            }

            $payment = Payment::first();
            if (!$payment) {
                $this->error('No payments found in database');
                return;
            }

            // Send notification
            $user->notify(new PaymentSuccessNotification($payment, false));
            $this->info('✓ Payment notification sent successfully');

            $this->info('All email tests completed successfully!');
            $this->info('Check your Mailtrap inbox for the test emails.');

        } catch (\Exception $e) {
            $this->error('Email test failed: ' . $e->getMessage());
            $this->error('Stack trace: ' . $e->getTraceAsString());
        }
    }
}
