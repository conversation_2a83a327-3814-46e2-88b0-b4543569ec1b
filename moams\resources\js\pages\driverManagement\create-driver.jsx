import { useState, useCallback, useEffect } from 'react';
import { Head, useForm } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowLeft } from 'lucide-react';
import { Link } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { MinibusOwnerCombobox } from '@/components/ui/minibus-owner-combobox';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';

export default function CreateDriver({ userRole, minibusOwners }) {
    const owners = minibusOwners || [];
    const { data, setData, post, processing, errors, reset } = useForm({
        first_name: '',
        last_name: '',
        phone_number: '',
        license_number: '',
        district: '',
        village_town: '',
        owner_id: '',
        minibus_id: '',
        license: null,
    });

    const [selectedOwner, setSelectedOwner] = useState(null);
    const [routes, setRoutes] = useState([]);
    const [selectedRoute, setSelectedRoute] = useState(null);
    const [minibuses, setMinibuses] = useState([]);
    const [selectedMinibus, setSelectedMinibus] = useState(null);
    const [lastLicenseFile, setLastLicenseFile] = useState(null);

    // Fetch routes on mount
    useEffect(() => {
        fetch('/api/vehicle-routes')
            .then(res => res.json())
            .then(data => setRoutes(data));
    }, []);

    // Fetch minibuses on mount
    useEffect(() => {
        fetch('/api/minibuses')
            .then(res => res.json())
            .then(data => setMinibuses(data));
    }, []);

    const handleFileChange = (e) => {
        const file = e.target.files[0];
        if (
            lastLicenseFile &&
            file &&
            file.name === lastLicenseFile.name &&
            file.size === lastLicenseFile.size &&
            file.lastModified === lastLicenseFile.lastModified
        ) {
            alert("You have already selected this file. Please choose a different file.");
            e.target.value = "";
            return;
        }
        setLastLicenseFile(file);
        setData('license', file);
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        post(route('drivers.store'), {
            forceFormData: true
        });
    };

    const districts = [
        'Lilongwe', 'Blantyre', 'Mzuzu', 'Zomba', 'Kasungu', 'Ntcheu',
        'Mangochi', 'Salima', 'Dowa', 'Ntchisi', 'Dedza', 'Nkhotakota',
        'Mchinji', 'Nkhatabay', 'Rumphi', 'Chitipa', 'Karonga', 'Thyolo',
        'Mulanje', 'Phalombe', 'Chikwawa', 'Nsanje', 'Balaka', 'Machinga'
    ];

    const breadcrumbs = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: userRole === 'minibus owner' ? 'My Drivers' : 'Driver Management', href: '/drivers' },
        { title: 'Add Driver', href: '/drivers/create' },
    ];

    const fetchOwners = useCallback(async (search) => {
        const res = await fetch(`/api/minibus-owners?search=${encodeURIComponent(search)}`);
        return (await res.json()).filter(owner => owner.id !== null && owner.id !== undefined && owner.id !== "");
    }, []);

    const fetchMinibusesForOwner = useCallback(async (ownerId) => {
        try {
            const res = await fetch(`/api/minibuses/available-for-owner/${ownerId}`);
            if (res.ok) {
                const data = await res.json();
                setMinibuses(data);
            } else {
                setMinibuses([]);
            }
        } catch (error) {
            console.error('Error fetching minibuses:', error);
            setMinibuses([]);
        }
    }, []);

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={userRole === 'minibus owner' ? 'Add My Driver' : 'Add New Driver'} />
            <div className="p-8">
                <div className="space-y-6">
                    <div className="flex items-center space-x-4">
                        <Link href={route('drivers.index')}>
                            <Button variant="outline" size="sm">
                                <span className="block sm:hidden">
                                    <Tooltip>
                                        <TooltipTrigger asChild>
                                            <span><ArrowLeft className="h-4 w-4" /></span>
                                        </TooltipTrigger>
                                        <TooltipContent>Back to Drivers</TooltipContent>
                                    </Tooltip>
                                </span>
                                <span className="hidden sm:flex items-center">
                                    <ArrowLeft className="mr-2 h-4 w-4" />
                                    Back to Drivers
                                </span>
                            </Button>
                        </Link>
                        <p className="text-muted-foreground">
                            Enter the driver's information below
                        </p>
                    </div>

                    <div className="max-w-2xl">
                        <Card>
                            <CardHeader>
                                <CardTitle>Driver Information</CardTitle>
                                <CardDescription>
                                    Fill in the driver's personal and contact details
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <form onSubmit={handleSubmit} className="space-y-6">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div className="space-y-2">
                                            <Label htmlFor="first_name">First Name *</Label>
                                            <Input
                                                id="first_name"
                                                value={data.first_name || ""}
                                                onChange={(e) => setData('first_name', e.target.value)}
                                                placeholder="Enter first name"
                                                className={errors.first_name ? 'border-red-500' : ''}
                                            />
                                            {errors.first_name && (
                                                <p className="text-sm text-red-500">{errors.first_name}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="last_name">Last Name *</Label>
                                            <Input
                                                id="last_name"
                                                value={data.last_name || ""}
                                                onChange={(e) => setData('last_name', e.target.value)}
                                                placeholder="Enter last name"
                                                className={errors.last_name ? 'border-red-500' : ''}
                                            />
                                            {errors.last_name && (
                                                <p className="text-sm text-red-500">{errors.last_name}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="phone_number">Phone Number *</Label>
                                            <Input
                                                id="phone_number"
                                                value={data.phone_number || ""}
                                                onChange={(e) => setData('phone_number', e.target.value)}
                                                placeholder="Enter phone number"
                                                className={errors.phone_number ? 'border-red-500' : ''}
                                            />
                                            {errors.phone_number && (
                                                <p className="text-sm text-red-500">{errors.phone_number}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="license_number">License Number *</Label>
                                            <Input
                                                id="license_number"
                                                value={data.license_number || ""}
                                                onChange={(e) => setData('license_number', e.target.value)}
                                                placeholder="Enter license number"
                                                className={errors.license_number ? 'border-red-500' : ''}
                                            />
                                            {errors.license_number && (
                                                <p className="text-sm text-red-500">{errors.license_number}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="license">Driver's License (PDF/Image) *</Label>
                                            <Input
                                                id="license"
                                                type="file"
                                                accept=".pdf,.jpg,.jpeg,.png"
                                                onChange={handleFileChange}
                                                className={errors.license ? 'border-red-500' : ''}
                                            />
                                            {errors.license && (
                                                <p className="text-sm text-red-500">{errors.license}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="district">District *</Label>
                                            <Select
                                                value={data.district || ""}
                                                onValueChange={(value) => setData('district', value)}
                                            >
                                                <SelectTrigger className={errors.district ? 'border-red-500' : ''}>
                                                    <SelectValue placeholder="Select district" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {districts.map((district) => (
                                                        <SelectItem key={district} value={district}>
                                                            {district}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            {errors.district && (
                                                <p className="text-sm text-red-500">{errors.district}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="village_town">Village/Town *</Label>
                                            <Input
                                                id="village_town"
                                                value={data.village_town || ""}
                                                onChange={(e) => setData('village_town', e.target.value)}
                                                placeholder="Enter village or town"
                                                className={errors.village_town ? 'border-red-500' : ''}
                                            />
                                            {errors.village_town && (
                                                <p className="text-sm text-red-500">{errors.village_town}</p>
                                            )}
                                        </div>

                                        {userRole === 'association clerk' && (
                                            <div className="space-y-2">
                                                <Label htmlFor="owner_id">Minibus Owner *</Label>
                                                <MinibusOwnerCombobox
                                                    value={selectedOwner || null}
                                                    onChange={owner => {
                                                        setSelectedOwner(owner);
                                                        setData('owner_id', owner?.id || '');
                                                        // Reset minibus selection when owner changes
                                                        setSelectedMinibus(null);
                                                        setData('minibus_id', '');
                                                        // Fetch minibuses for the selected owner
                                                        if (owner?.id) {
                                                            fetchMinibusesForOwner(owner.id);
                                                        } else {
                                                            setMinibuses([]);
                                                        }
                                                    }}
                                                    fetchOwners={fetchOwners}
                                                    placeholder="Select minibus owner..."
                                                />
                                                {errors.owner_id && (
                                                    <p className="text-sm text-red-500">{errors.owner_id}</p>
                                                )}
                                            </div>
                                        )}

                                        {/* Minibus Selection - Show for both roles */}


                                        <div className="space-y-2">
                                            <Label htmlFor="minibus_id">Minibus *</Label>
                                            <Select
                                                value={data.minibus_id || ""}
                                                onValueChange={(value) => {
                                                    setData('minibus_id', value);
                                                    setSelectedMinibus(minibuses.find(m => m.id.toString() === value));
                                                }}
                                            >
                                                <SelectTrigger className={errors.minibus_id ? 'border-red-500' : ''}>
                                                    <SelectValue placeholder="Select minibus" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {minibuses.map((minibus) => (
                                                        <SelectItem key={minibus.id} value={minibus.id.toString()}>
                                                            {minibus.number_plate}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            {errors.minibus_id && (
                                                <p className="text-sm text-red-500">{errors.minibus_id}</p>
                                            )}
                                        </div>
                                    </div>

                                    <div className="flex justify-end space-x-4 pt-6">
                                        <Link href={route('drivers.index')}>
                                            <Button type="button" variant="outline">
                                                Cancel
                                            </Button>
                                        </Link>
                                        <Button type="submit" disabled={processing} className="bg-blue-600 hover:bg-blue-700">
                                            {processing ? 'Creating...' : 'Create Driver'}
                                        </Button>
                                    </div>
                                </form>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}