# Ctechpay Integration Setup Guide

This guide will help you set up the real Ctechpay payment gateway integration for your student project demonstration.

## Prerequisites

1. **Ctechpay Account**: You need to have a Ctechpay merchant account
2. **API Credentials**: Obtain your API key and registration details from Ctechpay
3. **Laravel Application**: Ensure your MOAMS application is running

## Step 1: Get Ctechpay Credentials

Contact Ctechpay to get your merchant credentials:
- **API Key**: Your unique API token for authentication
- **Registration**: Your merchant registration identifier
- **Documentation**: Access to their API documentation

## Step 2: Configure Environment Variables

Add the following to your `.env` file:

```env
# Ctechpay Payment Gateway Configuration
CTECHPAY_API_KEY=your_actual_api_key_here
CTECHPAY_REGISTRATION=your_registration_id_here
CTECHPAY_ENVIRONMENT=production
```

**Important**: Replace the placeholder values with your actual Ctechpay credentials.

## Step 3: Test the Integration

Run the integration test command to verify everything is working:

```bash
php artisan ctechpay:test-integration
```

This will:
- ✅ Check your configuration
- 🌐 Test API connectivity
- 📦 Create a test payment order
- 🔍 Test payment status checking

## Step 4: Understanding the Payment Flow

### For Card Payments:
1. User selects "Card Payment" option
2. System creates payment order with Ctechpay
3. User is redirected to Ctechpay payment page
4. User enters card details on Ctechpay's secure page
5. After payment, user is redirected back to your application
6. System verifies payment status with Ctechpay API

### Card Payments Only:
The system now only supports card payments through Ctechpay for a streamlined user experience.

## Step 5: Testing Payment Flow

### Test Card Payment:
1. Go to membership summary page
2. Click "Pay Online" for any unpaid membership
3. Select "Card Payment"
4. Enter amount and click "Pay"
5. You'll be redirected to real Ctechpay payment page
6. Use test card details (if available) or real card for testing

### Test Card Payment:
1. Go to membership summary page
2. Click "Pay Online" for any unpaid membership
3. Enter payment amount
4. Click "Pay" to be redirected to Ctechpay
5. Complete payment on the secure payment page

## Step 6: Webhook Configuration (Optional)

For real-time payment notifications, configure webhooks:

1. In your Ctechpay merchant dashboard, set webhook URL to:
   ```
   https://yourdomain.com/payments/ctechpay/webhook
   ```

2. Ensure your application is accessible from the internet for webhooks to work.

## Step 7: Production Deployment

### Security Checklist:
- ✅ Use HTTPS in production
- ✅ Keep API credentials secure
- ✅ Enable webhook signature validation
- ✅ Monitor payment logs
- ✅ Test all payment scenarios

### Environment Setup:
```env
APP_ENV=production
APP_DEBUG=false
CTECHPAY_ENVIRONMENT=production
```

## Troubleshooting

### Common Issues:

1. **"Missing configuration" error**:
   - Check your `.env` file has all required Ctechpay variables
   - Restart your Laravel application after adding variables

2. **"API connectivity failed" error**:
   - Verify your API credentials are correct
   - Check your internet connection
   - Ensure Ctechpay API is accessible

3. **"Payment not found" error**:
   - Check database connection
   - Verify payment record was created
   - Check application logs for errors

4. **Webhook not working**:
   - Ensure your application is accessible from internet
   - Check webhook URL in Ctechpay dashboard
   - Verify CSRF protection is disabled for webhook route

### Debug Commands:

```bash
# Test configuration
php artisan ctechpay:test-integration

# Check specific order status
php artisan ctechpay:test-integration --order-ref=YOUR_ORDER_REF

# View application logs
tail -f storage/logs/laravel.log

# Check routes
php artisan route:list --name=ctechpay
```

## Student Project Demonstration

For your department demonstration:

1. **Prepare Test Data**: Create test users and memberships
2. **Demo Script**: Prepare a script showing the complete payment flow
3. **Show Both Methods**: Demonstrate both card and Airtel Money payments
4. **Error Handling**: Show how the system handles payment failures
5. **Admin View**: Show how clerks can view payment status

### Demo Checklist:
- [ ] Test user account created
- [ ] Unpaid membership available
- [ ] Internet connection stable
- [ ] Ctechpay credentials working
- [ ] Payment flow tested end-to-end
- [ ] Error scenarios prepared
- [ ] Admin dashboard ready

## Support

If you encounter issues:
1. Check the application logs: `storage/logs/laravel.log`
2. Review Ctechpay API documentation
3. Contact Ctechpay support for API-related issues
4. Check Laravel documentation for framework issues

## Security Notes

- Never commit API credentials to version control
- Use environment variables for all sensitive data
- Enable HTTPS in production
- Monitor payment transactions regularly
- Keep Laravel and dependencies updated

---

**Note**: This integration uses the real Ctechpay API. All payments processed will be actual transactions. Use small amounts for testing and ensure you have proper authorization for any test payments.
