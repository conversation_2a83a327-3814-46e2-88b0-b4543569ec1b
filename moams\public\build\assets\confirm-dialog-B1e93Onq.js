import{j as s,B as o}from"./app-DL-qYY5V.js";import{D as x,a as j,b as D,c as f,d as C,e as g,f as p}from"./dialog-CXtul0Wy.js";function v({open:n,title:t="Are you sure?",description:a="This action cannot be undone.",onConfirm:r,onCancel:e,confirmText:l="Confirm",cancelText:c="Cancel",loading:i=!1,confirmVariant:d="destructive",children:h}){return s.jsx(x,{open:n,onOpenChange:u=>{!u&&e&&e()},children:s.jsxs(j,{showCloseButton:!1,children:[s.jsxs(D,{children:[s.jsx(f,{children:t}),s.jsx(C,{children:a})]}),h,s.jsxs(g,{children:[s.jsx(p,{asChild:!0,children:s.jsx(o,{type:"button",variant:"outline",onClick:e,disabled:i,children:c})}),s.jsx(o,{type:"button",variant:d,onClick:r,disabled:i,children:i?"Processing...":l})]})]})})}export{v as C};
