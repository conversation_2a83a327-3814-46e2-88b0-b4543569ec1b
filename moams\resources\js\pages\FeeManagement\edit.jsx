import React, { useState } from 'react';
import { Link, useForm } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import InputError from '@/components/input-error';
import { ArrowLeft, DollarSign, Calendar } from 'lucide-react';

export default function EditFeeSetting({ feeSetting, currentFee }) {
    const { data, setData, patch, processing, errors } = useForm({
        amount: feeSetting.amount || '',
        description: feeSetting.description || '',
        effective_from: feeSetting.effective_from ? new Date(feeSetting.effective_from).toISOString().split('T')[0] : '',
    });

    const breadcrumbs = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Fee Management', href: '/fee-settings' },
        { title: `Edit ${feeSetting.fee_type} Fee`, href: `/fee-settings/${feeSetting.id}/edit` },
    ];

    const handleSubmit = (e) => {
        e.preventDefault();
        patch(route('fee-settings.update', feeSetting.id));
    };

    // Check if the entered amount matches the current fee and it's not just a description update
    const isAmountSameAsCurrent = () => {
        if (!currentFee) return false;

        const currentAmount = parseFloat(currentFee.amount);
        const enteredAmount = parseFloat(data.amount);
        const originalEffectiveFrom = feeSetting.effective_from ? new Date(feeSetting.effective_from).toISOString().split('T')[0] : '';

        // If amounts are the same
        if (currentAmount && enteredAmount && currentAmount === enteredAmount) {
            // Allow if it's the same fee setting with the same effective date (description-only update)
            const isSameFeeSettingWithSameDate = currentFee.id === feeSetting.id && data.effective_from === originalEffectiveFrom;
            return !isSameFeeSettingWithSameDate;
        }

        return false;
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <div className="p-2 sm:p-4 md:p-8">
                <div className="flex flex-col sm:flex-row gap-2 sm:gap-4 items-center mb-4 sm:mb-6">
                    <div className="flex items-center gap-2">
                        <Link href="/fee-settings">
                            <Button variant="outline" className="w-full sm:w-auto">
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                Back to Fee Management
                            </Button>
                        </Link>
                        <span className="text-muted-foreground text-base font-medium">
                            Update the {feeSetting.fee_type} fee setting.
                        </span>
                    </div>
                </div>

                <div className="w-full max-w-2xl mx-auto">
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                Edit {feeSetting.fee_type.charAt(0).toUpperCase() + feeSetting.fee_type.slice(1)} Fee
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={handleSubmit} className="space-y-6">
                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="fee_type">Fee Type</Label>
                                        <div className="flex items-center gap-2 p-3 border rounded-md bg-gray-50">
                                            <span className="font-medium capitalize">{feeSetting.fee_type}</span>
                                        </div>
                                        <p className="text-sm text-gray-500">
                                            Fee type cannot be changed once created.
                                        </p>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="current_amount">Current Amount</Label>
                                        <div className="flex items-center gap-2 p-3 border rounded-md bg-gray-50">
                                            <span className="font-semibold"><span className='text-green-600'>MK</span> {feeSetting.amount}</span>
                                        </div>
                                        <p className="text-sm text-gray-500">
                                            Current active amount for this fee type.
                                        </p>
                                    </div>
                                </div>

                                {/* Amount */}
                                <div className="space-y-2">
                                    <Label htmlFor="amount">New Amount (MK) *</Label>
                                    <Input
                                        id="amount"
                                        type="number"
                                        step="0.01"
                                        min="0"
                                        value={data.amount}
                                        onChange={(e) => setData('amount', e.target.value)}
                                        placeholder="Enter new amount"
                                        className={errors.amount ? 'border-red-500' : ''}
                                    />
                                    <InputError message={errors.amount} className="mt-2" />
                                    {isAmountSameAsCurrent() && (
                                        <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                                            <p className="text-sm text-yellow-800">
                                                ⚠️ This amount is the same as the current {feeSetting.fee_type} fee (MK {currentFee?.amount ? parseFloat(currentFee.amount).toLocaleString() : 'N/A'}).
                                                The fee amount must be different from the current active fee unless you're only updating the description.
                                            </p>
                                        </div>
                                    )}
                                </div>

                                {/* Effective From */}
                                <div className="space-y-2">
                                    <Label htmlFor="effective_from">Effective From *</Label>
                                    <Input
                                        id="effective_from"
                                        type="date"
                                        value={data.effective_from}
                                        onChange={(e) => setData('effective_from', e.target.value)}
                                        className={errors.effective_from ? 'border-red-500' : ''}
                                    />
                                    <InputError message={errors.effective_from} className="mt-2" />
                                    <p className="text-sm text-gray-600">
                                        Changing this date will create a new fee setting
                                    </p>
                                </div>

                                {/* Description */}
                                <div className="space-y-2">
                                    <Label htmlFor="description">Description (Optional)</Label>
                                    <Textarea
                                        id="description"
                                        value={data.description}
                                        onChange={(e) => setData('description', e.target.value)}
                                        placeholder="Enter a description for this fee change"
                                        rows={3}
                                        className={errors.description ? 'border-red-500' : ''}
                                    />
                                    <InputError message={errors.description} className="mt-2" />
                                </div>

                                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                    <h4 className="font-medium text-blue-900 mb-2">Important Note</h4>
                                    <p className="text-sm text-blue-700">
                                        If you change the effective date, a new fee setting will be created.
                                        The current fee will remain active until the new effective date is reached.
                                    </p>
                                </div>

                                <div className="flex flex-col sm:flex-row gap-3 pt-4">
                                    <Button
                                        type="submit"
                                        disabled={processing}
                                        className="flex-1 bg-blue-500 hover:bg-blue-600"
                                    >
                                        {processing ? 'Updating...' : 'Update Fee Setting'}
                                    </Button>
                                    <Link href="/fee-settings">
                                        <Button variant="outline" className="w-full sm:w-auto">
                                            Cancel
                                        </Button>
                                    </Link>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
} 