<?php

namespace App\Notifications;

use App\Models\DriverClearanceRequest;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class DriverClearanceRequestNotification extends Notification
{
    // Made synchronous to avoid serialization issues

    public $clearanceRequest;

    /**
     * Create a new notification instance.
     */
    public function __construct(DriverClearanceRequest $clearanceRequest)
    {
        $this->clearanceRequest = $clearanceRequest;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via($notifiable): array
    {
        return ['database']; // Temporarily disabled email due to service limits
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable): MailMessage
    {
        $driver = $this->clearanceRequest->driver;
        $owner = $this->clearanceRequest->owner;

        $notificationData = [
            'badge' => ['type' => 'warning', 'text' => 'New Request'],
            'greeting' => 'Hello,',
            'message' => 'A new driver clearance request has been submitted and requires your review. As an association clerk/manager, please process this request at your earliest convenience.',
            'details' => [
                [
                    'title' => 'Driver Information',
                    'icon' => '🚗',
                    'items' => [
                        ['label' => 'Driver Name', 'value' => $driver->first_name . ' ' . $driver->last_name],
                        ['label' => 'License Number', 'value' => $driver->license_number ?? 'Not provided'],
                        ['label' => 'Phone', 'value' => $driver->phone_number ?? 'Not provided']
                    ]
                ],
                [
                    'title' => 'Current Owner Information',
                    'icon' => '👤',
                    'items' => [
                        ['label' => 'Owner Name', 'value' => $owner->first_name . ' ' . $owner->last_name],
                        ['label' => 'Email', 'value' => $owner->email],
                        ['label' => 'Phone', 'value' => $owner->phone_number ?? 'Not provided']
                    ]
                ],
                [
                    'title' => 'Request Details',
                    'icon' => '📝',
                    'items' => [
                        ['label' => 'Reason', 'value' => $this->clearanceRequest->reason],
                        ['label' => 'Submitted', 'value' => $this->clearanceRequest->created_at->format('F j, Y \a\t g:i A')],
                        ['label' => 'Status', 'value' => ucfirst($this->clearanceRequest->status)]
                    ]
                ]
            ],
            'action' => [
                'url' => route('notification.redirect', ['type' => 'driver-clearance', 'id' => $this->clearanceRequest->id]),
                'text' => 'Review Request'
            ],
            'additionalMessages' => [
                ['content' => 'Please review this request and take appropriate action. The minibus owner will be notified once you process the request.']
            ]
        ];

        return (new MailMessage)
            ->subject('New Driver Clearance Request')
            ->view('emails.general-notification', [
                'notificationData' => $notificationData,
                'subject' => 'New Driver Clearance Request',
                'logo' => asset('assets/logos/MoamLogo.png')
            ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray($notifiable): array
    {
        $driver = $this->clearanceRequest->driver;
        $owner = $this->clearanceRequest->owner;

        return [
            'clearance_request_id' => $this->clearanceRequest->id,
            'driver_id' => $driver->id,
            'driver_name' => "{$driver->first_name} {$driver->last_name}",
            'owner_name' => "{$owner->first_name} {$owner->last_name}",
            'reason' => $this->clearanceRequest->reason,
        ];
    }
}
