import{u as _,r as h,j as e,H as k,L as u,B as a,C as r,a as c,b as o,c as t,k as R,h as S,e as j}from"./app-DL-qYY5V.js";import{L}from"./label-e3QxUH-L.js";import{T as A}from"./textarea-SHrtPYpi.js";import{A as P,b as B,c as H,d as O}from"./app-layout-YqstQnqE.js";import{C as E}from"./confirm-dialog-B1e93Onq.js";import{A as p}from"./arrow-left-DCW23wrL.js";import"./index-BTzg1GwG.js";import"./index-1tPPImJd.js";import"./app-logo-icon-BnXlkpcX.js";import"./bus-CKTBKI0T.js";import"./dollar-sign-Cx0-nQIX.js";import"./users-DNGXY-sJ.js";import"./clipboard-list-CxRumT8v.js";import"./dialog-CXtul0Wy.js";function Z({driver:s,userRole:f,pendingRequest:d}){const{data:N,setData:v,post:b,processing:m,errors:i}=_({reason:""}),[g,l]=h.useState(!1),[C,x]=h.useState(!1),w=n=>{n.preventDefault(),b(route("drivers.store-clearance-request",s.id))},y=()=>{l(!0)},D=()=>l(!1),q=async()=>{x(!0),await j.delete(route("drivers.clearance-requests.destroy",d.id),{onSuccess:()=>j.visit(route("drivers.show",s.id))}),x(!1),l(!1)},T=[{title:"Dashboard",href:"/dashboard"},{title:f==="minibus owner"?"My Drivers":"Driver Management",href:"/drivers"},{title:"Request Clearance",href:"#"}];return e.jsxs(P,{breadcrumbs:T,children:[e.jsx(k,{title:`Request Clearance - ${s.first_name} ${s.last_name}`}),e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{className:"flex items-center space-x-4 mb-8",children:[e.jsx(u,{href:route("drivers.show",s.id),children:e.jsxs(a,{variant:"outline",size:"sm",children:[e.jsx("span",{className:"block sm:hidden",children:e.jsxs(B,{children:[e.jsx(H,{asChild:!0,children:e.jsx("span",{children:e.jsx(p,{className:"h-4 w-4"})})}),e.jsx(O,{children:"Back to Driver"})]})}),e.jsxs("span",{className:"hidden sm:flex items-center",children:[e.jsx(p,{className:"mr-2 h-4 w-4"}),"Back to Driver"]})]})}),e.jsxs("p",{className:"text-muted-foreground",children:["Submit a clearance request for ",s.first_name," ",s.last_name]})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-8",children:[e.jsx("div",{className:"lg:col-span-2",children:e.jsxs(r,{className:"mb-8",children:[e.jsx(c,{children:e.jsx(o,{children:"Driver Information"})}),e.jsx(t,{className:"space-y-4",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Name"}),e.jsxs("p",{className:"text-sm",children:[s.first_name," ",s.last_name]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Phone Number"}),e.jsx("p",{className:"text-sm",children:s.phone_number})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"District"}),e.jsx("p",{className:"text-sm",children:s.district})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Village/Town"}),e.jsx("p",{className:"text-sm",children:s.village_town})]})]})})]})}),e.jsxs("div",{children:[e.jsxs(r,{className:"mb-8",children:[e.jsxs(c,{children:[e.jsx(o,{children:"Clearance Request"}),e.jsx(R,{children:"Provide a reason for requesting driver clearance"})]}),e.jsx(t,{children:e.jsxs("form",{onSubmit:w,className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(L,{htmlFor:"reason",children:"Reason for Clearance *"}),e.jsx(A,{id:"reason",value:N.reason,onChange:n=>v("reason",n.target.value),placeholder:"Please provide a detailed reason for requesting driver clearance...",rows:6,className:i.reason?"border-red-500":""}),i.reason&&e.jsx("p",{className:"text-sm text-red-500",children:i.reason})]}),e.jsxs("div",{className:"flex justify-end space-x-4",children:[e.jsx(u,{href:route("drivers.show",s.id),children:e.jsx(a,{type:"button",variant:"outline",children:"Cancel"})}),e.jsx(a,{type:"submit",disabled:m,children:m?"Submitting...":"Submit Request"})]})]})})]}),d&&e.jsxs(r,{className:"mb-8",children:[e.jsx(c,{children:e.jsx(o,{children:"Quick Actions"})}),e.jsx(t,{children:e.jsx(a,{type:"button",variant:"destructive",onClick:y,className:"w-full",children:"Delete Pending Clearance Request"})})]}),e.jsx(r,{className:"mt-4 border-yellow-200 bg-yellow-50",children:e.jsx(t,{className:"pt-6",children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx(S,{className:"h-5 w-5 text-yellow-600 mt-0.5"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("h4",{className:"text-sm font-medium text-yellow-800",children:"Important Notice"}),e.jsx("p",{className:"text-sm text-yellow-700",children:"Once clearance is approved, the driver will be archived and removed from your active drivers list. You will need to contact an association clerk to unarchive the driver if you want to rehire them."})]})]})})})]})]})]})}),e.jsx(E,{open:g,title:"Delete Pending Clearance Request?",description:"This action cannot be undone and will permanently remove the clearance request.",confirmText:"Delete",confirmVariant:"destructive",loading:C,onCancel:D,onConfirm:q})]})}export{Z as default};
