import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { User, Bus, Users, DollarSign } from 'lucide-react';

export default function OwnerDashboard({ user, minibuses = [], drivers = [], unpaidMemberships = [], currentFees }) {
    return (
        <>
            {/* Personal Details (full width) */}
            <Card className="shadow-md mb-6">
                <CardHeader>
                    <CardTitle className="flex items-center gap-2"><User className="h-5 w-5 text-blue-600" /> Personal Details</CardTitle>
                </CardHeader>
                <CardContent>
                    <p className="text-gray-500 mb-2">Your basic profile information.</p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div><span className="font-semibold">First Name:</span> {user.first_name || 'N/A'}</div>
                        <div><span className="font-semibold">Last Name:</span> {user.last_name || 'N/A'}</div>
                        <div><span className="font-semibold">Gender:</span> {user.gender || 'N/A'}</div>
                        <div><span className="font-semibold">District:</span> {user.district || 'N/A'}</div>
                    </div>
                </CardContent>
            </Card>
            <Card className="shadow-md mb-6">
                <CardHeader>
                    <CardTitle className="flex items-center gap-2"><DollarSign className="h-5 w-5 text-green-600" /> Current Fees</CardTitle>
                </CardHeader>
                <CardContent>
                    <p className="text-gray-500 mb-2">Below are the current registration and affiliation fees set by the association.</p>
                    <div className="flex gap-8">
                        <div className="text-center">
                            <div className="text-xl font-bold text-green-700">{currentFees?.registration ? `MK ${currentFees.registration}` : 'N/A'}</div>
                            <div className="text-gray-600">Registration Fee</div>
                        </div>
                        <div className="text-center">
                            <div className="text-xl font-bold text-green-700">{currentFees?.affiliation ? `MK ${currentFees.affiliation}` : 'N/A'}</div>
                            <div className="text-gray-600">Affiliation Fee</div>
                        </div>
                    </div>
                </CardContent>
            </Card>
            {/* Statistics cards grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {/* Minibus Statistics */}
                <Card className="shadow-md">
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2"><Bus className="h-5 w-5 text-blue-600" /> Minibus Statistics</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <p className="text-gray-500 mb-2">Overview of your registered minibuses.</p>
                        <div className="flex gap-8">
                            <div className="text-center"><div className="text-xl font-bold">{minibuses?.length ?? 0}</div><div className="text-gray-600">All</div></div>
                        </div>
                    </CardContent>
                </Card>
                {/* Driver Statistics */}
                <Card className="shadow-md">
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2"><Users className="h-5 w-5 text-blue-600" /> Driver Statistics</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <p className="text-gray-500 mb-2">Summary of your drivers.</p>
                        <div className="flex gap-8">
                            <div className="text-center"><div className="text-xl font-bold">{drivers?.length ?? 0}</div><div className="text-gray-600">All</div></div>
                        </div>
                    </CardContent>
                </Card>
                {/* Unpaid Memberships */}
                {/*  <Card className="shadow-md">
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2"><DollarSign className="h-5 w-5 text-yellow-600" /> Unpaid Memberships</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <p className="text-gray-500 mb-2">Memberships that require payment.</p>
                        <div className="flex gap-8">
                            <div className="text-center"><div className="text-xl font-bold text-yellow-600">{unpaidMemberships?.length ?? 0}</div><div className="text-gray-600">Unpaid</div></div>
                        </div>
                    </CardContent>
                </Card>*/}
                {/* Add any other relevant info/statistics here as a Card in the grid */}
            </div>
        </>
    );
} 