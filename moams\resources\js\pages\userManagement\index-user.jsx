import { Head, Link, usePage, router } from '@inertiajs/react';
import { Plus, Eye, Edit, Trash2, Search, Filter, ChevronRight, Home, ChevronDown, Archive, RotateCcw } from 'lucide-react';
import { useState } from 'react';

import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import AppLayout from '@/layouts/app-layout';
import ConfirmDialog from '@/components/ui/confirm-dialog';
import Pagination from '@/components/ui/pagination';

export default function UserManagement() {
    const [searchTerm, setSearchTerm] = useState('');
    const [roleFilter, setRoleFilter] = useState('all');
    const [statusFilter, setStatusFilter] = useState('active');
    const [confirmDialog, setConfirmDialog] = useState({ open: false, action: null, user: null });
    const [loading, setLoading] = useState(false);

    try {
        const { users, statistics } = usePage().props;

        // Handle both old array format and new paginated format
        const usersData = users?.data || users || [];
        const paginationData = users?.data ? users : null;

        // If users is undefined or null, show loading state
        if (!users) {
            return (
                <AppLayout>
                    <Head title="User Management" />
                    <div className="container mx-auto px-4 py-8">
                        <div className="text-center">Loading...</div>
                    </div>
                </AppLayout>
            );
        }

        const handleAction = (action, user) => {
            setConfirmDialog({ open: true, action, user });
        };
        const handleCancel = () => setConfirmDialog({ open: false, action: null, user: null });
        const handleConfirm = async () => {
            if (!confirmDialog.user) return;
            setLoading(true);
            const { id, first_name, last_name, archived_at } = confirmDialog.user;
            if (confirmDialog.action === 'archive' || confirmDialog.action === 'unarchive') {
                await router.put(`/admin/users/${id}/${confirmDialog.action}`);
            }
            setLoading(false);
            setConfirmDialog({ open: false, action: null, user: null });
        };

        const breadcrumbs = [
            {
                title: 'Dashboard',
                href: '/dashboard',
            },
            {
                title: 'User Management',
                href: '/admin/users',
            },
        ];

        // Handle search and filter changes
        const handleSearch = (value) => {
            setSearchTerm(value);
            router.get('/admin/users', {
                search: value,
                role: roleFilter,
                status: statusFilter,
            }, {
                preserveState: true,
                preserveScroll: true,
            });
        };

        const handleRoleFilter = (value) => {
            setRoleFilter(value);
            router.get('/admin/users', {
                search: searchTerm,
                role: value,
                status: statusFilter,
            }, {
                preserveState: true,
                preserveScroll: true,
            });
        };

        const handleStatusFilter = (value) => {
            setStatusFilter(value);
            router.get('/admin/users', {
                search: searchTerm,
                role: roleFilter,
                status: value,
            }, {
                preserveState: true,
                preserveScroll: true,
            });
        };

        const getRoleBadgeColor = (roleName) => {
            switch (roleName) {
                case 'system admin':
                    return 'bg-red-100 text-red-800 border-red-200';
                case 'minibus owner':
                    return 'bg-green-100 text-green-800 border-green-200';
                case 'association clerk':
                    return 'bg-purple-100 text-purple-800 border-purple-200';
                case 'association manager':
                    return 'bg-orange-100 text-orange-800 border-orange-200';
                default:
                    return 'bg-gray-100 text-gray-800 border-gray-200';
            }
        };

        const formatDate = (dateString) => {
            return new Date(dateString).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        };

        // Count unarchived system admins
        const unarchivedAdmins = usersData.filter(u => u.roles && u.roles.some(r => r.name === 'system admin') && !u.archived_at);
        const isLastUnarchivedAdmin = (user) => user.roles && user.roles.some(r => r.name === 'system admin') && unarchivedAdmins.length === 1;

        return (
            <AppLayout breadcrumbs={breadcrumbs}>
                <Head title="User Management" />

                <div className="w-full max-w-full overflow-hidden">
                    <div className="container mx-auto px-4 py-8">
                        {/* Header */}
                        <div className="flex justify-between items-center mb-8">
                            <div>
                                <p className="text-gray-600 dark:text-gray-400">
                                    Manage all system users, their roles, and permissions
                                </p>
                            </div>
                            <Link href="/admin/create-user">
                                <Button className="bg-blue-600 hover:bg-blue-700">
                                    <Plus className="h-4 w-4 mr-2" />
                                    Create User
                                </Button>
                            </Link>
                        </div>

                        {/* Filters and Search */}
                        <Card className="mb-6">
                            <CardHeader>
                                <CardTitle className="text-lg">Filters & Search</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                                    <div>
                                        <Label htmlFor="search">Search Users</Label>
                                        <div className="relative">
                                            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                                            <Input
                                                id="search"
                                                placeholder="Search by name, email, or phone..."
                                                value={searchTerm}
                                                onChange={(e) => handleSearch(e.target.value)}
                                                className="pl-10"
                                            />
                                        </div>
                                    </div>
                                    <div>
                                        <Label htmlFor="role-filter">Filter by Role</Label>
                                        <Select value={roleFilter} onValueChange={handleRoleFilter}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="All roles" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectGroup>
                                                    <SelectItem value="all">All Roles</SelectItem>
                                                    <SelectItem value="system admin">System Admin</SelectItem>
                                                    <SelectItem value="minibus owner">Minibus Owner</SelectItem>
                                                    <SelectItem value="association clerk">Association Clerk</SelectItem>
                                                    <SelectItem value="association manager">Association Manager</SelectItem>
                                                </SelectGroup>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    <div>
                                        <Label htmlFor="status-filter">Account Status</Label>
                                        <Select value={statusFilter} onValueChange={handleStatusFilter}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Active" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectGroup>
                                                    <SelectItem value="active">Active</SelectItem>
                                                    <SelectItem value="inactive">Inactive</SelectItem>
                                                    <SelectItem value="all">All</SelectItem>
                                                </SelectGroup>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    <div className="flex items-end">
                                        <Button
                                            variant="outline"
                                            onClick={() => {
                                                setSearchTerm('');
                                                setRoleFilter('all');
                                                setStatusFilter('active');
                                                // Redirect to clear all filters
                                                router.get('/admin/users');
                                            }}
                                            className="w-full"
                                        >
                                            <Filter className="h-4 w-4 mr-2" />
                                            Clear Filters
                                        </Button>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Users Table Container - Fixed width that fits screen */}
                        <div
                            className="w-full"
                            style={{
                                maxWidth: 'calc(100vw - 2rem)',
                                overflow: 'hidden'
                            }}
                        >
                            <Card className="w-full border-0 shadow-none">
                                <CardHeader className="px-0">
                                    <CardTitle className="flex items-center gap-2">
                                        <Eye className="h-5 w-5" />
                                        Users ({paginationData?.total || usersData.length})
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="p-0">
                                    {usersData.length > 0 ? (
                                        <div className="overflow-x-auto mb-8">
                                            <TooltipProvider>
                                                <table className="min-w-full w-full bg-white border border-gray-200 rounded-lg">
                                                    <thead>
                                                        <tr className="bg-gray-100 text-gray-700">
                                                            <th className="px-4 py-3 text-left font-medium">Name</th>
                                                            <th className="px-4 py-3 text-left font-medium">Email</th>
                                                            <th className="px-4 py-3 text-left font-medium">Phone</th>
                                                            <th className="px-4 py-3 text-left font-medium">Gender</th>
                                                            <th className="px-4 py-3 text-left font-medium">Roles</th>
                                                            <th className="px-4 py-3 text-left font-medium">Status</th>
                                                            <th className="px-4 py-3 text-left font-medium">Created</th>
                                                            <th className="px-4 py-3 text-left font-medium">Actions</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        {usersData.map((user) => (
                                                            <tr key={user.id} className="border-b hover:bg-gray-50">
                                                                <td className="px-4 py-3">
                                                                    {user.first_name || 'Unknown'} {user.last_name || 'User'}
                                                                </td>
                                                                <td className="px-4 py-3">{user.email}</td>
                                                                <td className="px-4 py-3">{user.phone_number || 'N/A'}</td>
                                                                <td className="px-4 py-3 capitalize">{user.gender || 'N/A'}</td>
                                                                <td className="px-4 py-3">
                                                                    <div className="flex flex-wrap gap-1">
                                                                        {user.roles && Array.isArray(user.roles) ? user.roles.map((role) => (
                                                                            <Badge
                                                                                key={role.id}
                                                                                variant="outline"
                                                                                className={`text-xs ${getRoleBadgeColor(role.name)}`}
                                                                            >
                                                                                {role.name}
                                                                            </Badge>
                                                                        )) : (
                                                                            <Badge variant="outline" className="text-xs">
                                                                                No roles assigned
                                                                            </Badge>
                                                                        )}
                                                                    </div>
                                                                </td>
                                                                <td className="px-4 py-3">
                                                                    {user.archived_at ? (
                                                                        <Badge variant="destructive">Archived</Badge>
                                                                    ) : (
                                                                        <Badge className="bg-green-100 text-green-800">Active</Badge>
                                                                    )}
                                                                </td>
                                                                <td className="px-4 py-3">{user.created_at ? formatDate(user.created_at) : 'N/A'}</td>
                                                                <td className="px-4 py-3">
                                                                    <div className="flex items-center gap-2">
                                                                        <Tooltip>
                                                                            <TooltipTrigger asChild>
                                                                                <Link href={`/admin/users/${user.id}`}>
                                                                                    <Button variant="outline" size="sm">
                                                                                        <Eye className="h-4 w-4" />
                                                                                    </Button>
                                                                                </Link>
                                                                            </TooltipTrigger>
                                                                            <TooltipContent>
                                                                                <p>View user details</p>
                                                                            </TooltipContent>
                                                                        </Tooltip>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        ))}
                                                    </tbody>
                                                </table>
                                            </TooltipProvider>
                                        </div>
                                    ) : (
                                        <div className="text-center py-8 px-6">
                                            <Eye className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                                            <h3 className="text-lg font-medium mb-2">No users found</h3>
                                            <p className="text-muted-foreground mb-4">
                                                {searchTerm || roleFilter !== 'all' || statusFilter !== 'all' ?
                                                    "Try adjusting your search or filters" :
                                                    "No users have been registered yet"}
                                            </p>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        </div>

                        {/* Pagination - only show if we have paginated data */}
                        {paginationData && paginationData.total > 0 && paginationData.total > paginationData.per_page && (
                            <div className="mt-6 w-full">
                                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 px-4 py-4 border-t bg-gray-50/50 rounded-b-lg">
                                    <div className="text-sm text-gray-700 text-center sm:text-left">
                                        Showing {paginationData.from} to {paginationData.to} of {paginationData.total} results
                                    </div>
                                    {paginationData.last_page > 1 && (
                                        <Pagination data={paginationData} />
                                    )}
                                </div>
                            </div>
                        )}


                    </div>
                    <ConfirmDialog
                        open={confirmDialog.open}
                        title={
                            confirmDialog.action === 'archive' ? `Archive ${confirmDialog.user?.first_name} ${confirmDialog.user?.last_name}?` :
                                confirmDialog.action === 'unarchive' ? `Unarchive ${confirmDialog.user?.first_name} ${confirmDialog.user?.last_name}?` :
                                    'Are you sure?'
                        }
                        description={
                            confirmDialog.action === 'archive' ? 'This user will be archived and will not be able to access the system.' :
                                confirmDialog.action === 'unarchive' ? 'This user will be restored and regain access to the system.' :
                                    ''
                        }
                        confirmText={
                            confirmDialog.action === 'archive' ? 'Archive' :
                                confirmDialog.action === 'unarchive' ? 'Unarchive' :
                                    'Confirm'
                        }
                        confirmVariant={confirmDialog.action === 'archive' ? 'destructive' : 'default'}
                        loading={loading}
                        onCancel={handleCancel}
                        onConfirm={handleConfirm}
                    />
                </div>
            </AppLayout>
        );
    } catch (error) {
        console.error('Error in UserManagement component:', error);
        return (
            <AppLayout>
                <Head title="User Management" />
                <div className="container mx-auto px-4 py-8">
                    <Card>
                        <CardContent className="p-8 text-center">
                            <h2 className="text-xl font-semibold text-red-600 mb-2">Error Loading Users</h2>
                            <p className="text-gray-600 mb-4">There was an error loading the user management page.</p>
                            <p className="text-sm text-gray-500">Error: {error.message}</p>
                            <Button
                                onClick={() => window.location.reload()}
                                className="mt-4"
                            >
                                Reload Page
                            </Button>
                        </CardContent>
                    </Card>
                </div>
            </AppLayout>
        );
    }
}