import{i as J,d as K,r as d,j as e,H as Q,L as W,B as a,e as c,f as x,h as o,C as Y,a as Z,b as ee,c as se}from"./app-DL-qYY5V.js";import{A as te,B as m}from"./app-layout-YqstQnqE.js";import{S as re,a as ae,b as le,c as ie,d as p}from"./select-Cp8NjZe8.js";import{T as ne}from"./textarea-SHrtPYpi.js";import{C as de}from"./chevron-left-DFeVEtK7.js";import{H as ce}from"./history-Boy80jYJ.js";import{C as h}from"./circle-x-DliR3-rL.js";import{U as $}from"./users-DNGXY-sJ.js";import{F as g}from"./file-text-BcxQqvSb.js";import{D as xe}from"./download-fjRG0KHX.js";import"./index-1tPPImJd.js";import"./index-BTzg1GwG.js";import"./app-logo-icon-BnXlkpcX.js";import"./bus-CKTBKI0T.js";import"./dollar-sign-Cx0-nQIX.js";import"./clipboard-list-CxRumT8v.js";import"./index-uC6ZAdKJ.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oe=[["path",{d:"M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9C18.7 10.6 16 10 16 10s-1.3-1.4-2.2-2.3c-.5-.4-1.1-.7-1.8-.7H5c-.6 0-1.1.4-1.4.9l-1.4 2.9A3.7 3.7 0 0 0 2 12v4c0 .6.4 1 1 1h2",key:"5owen"}],["circle",{cx:"7",cy:"17",r:"2",key:"u2ysq9"}],["path",{d:"M9 17h6",key:"r8uit2"}],["circle",{cx:"17",cy:"17",r:"2",key:"axvx0g"}]],me=J("Car",oe);function Me({misconduct:s}){var y,_,k,S,C,R,D,M,L,T,U,B,E,V,H,O;const{userRoles:r}=K().props,[j,i]=d.useState(!1),[u,v]=d.useState(s.severity),[N,n]=d.useState(""),F=r&&r.includes("association clerk"),A=r&&r.includes("association manager"),I=r&&r.includes("system admin"),b=r&&r.includes("minibus owner"),P=I||F||A,q=t=>{const l={low:"bg-green-100 text-green-800",medium:"bg-orange-100 text-orange-800",high:"bg-red-100 text-red-800"};return e.jsx(m,{className:l[t]||"bg-gray-100 text-gray-800",children:t==null?void 0:t.toUpperCase()})},f=t=>e.jsx(m,{className:t==="resolved"?"bg-green-100 text-green-800":"bg-orange-100 text-orange-800",children:t==="resolved"?"RESOLVED":"UNRESOLVED"}),w=t=>{const l=t>=80?"bg-green-100 text-green-800":t>=60?"bg-orange-100 text-orange-800":"bg-red-100 text-red-800";return e.jsxs(m,{className:l,children:[t,"%"]})},z=async()=>{const t=s.resolution_status==="resolved"?"unresolved":"resolved";try{await c.patch(`/misconducts/${s.id}/resolution`,{resolution_status:t,resolution_notes:t==="resolved"?"Marked as resolved via details page":null})}catch(l){console.error("Error updating resolution status:",l)}},X=async()=>{try{await c.patch(`/misconducts/${s.id}/severity`,{severity:u,severity_notes:N}),i(!1),n("")}catch(t){console.error("Error updating severity:",t)}},G=[{title:"Dashboard",href:"/dashboard"},{title:"Misconduct Management",href:"/misconducts"},{title:"Misconduct Details",href:`/misconducts/${s.id}`}];return e.jsxs(te,{breadcrumbs:G,children:[e.jsx(Q,{title:`Misconduct: ${s.name}`}),e.jsxs("div",{className:"container mx-auto px-4 py-8 max-w-4xl",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center gap-4 mb-6",children:[e.jsxs("div",{className:"flex-1 flex items-center gap-2",children:[e.jsxs(W,{href:"/misconducts",className:"flex items-center px-3 py-1 rounded hover:bg-gray-100 border border-gray-200",children:[e.jsx(de,{className:"h-4 w-4 mr-1"})," Back"]}),e.jsx("div",{children:e.jsx("p",{className:"text-gray-600",children:"View and manage misconduct information"})})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(a,{variant:"outline",onClick:()=>c.visit(`/drivers/${s.offender.id}/misconducts`),children:[e.jsx(ce,{className:"h-4 w-4 mr-2"}),"View Misconduct History"]}),P&&e.jsx(a,{variant:s.resolution_status==="resolved"?"outline":"default",onClick:z,className:s.resolution_status==="resolved"?"text-orange-700 border-orange-300 hover:bg-orange-50":"bg-green-600 text-white hover:bg-green-700",children:s.resolution_status==="resolved"?e.jsxs(e.Fragment,{children:[e.jsx(h,{className:"h-4 w-4 mr-2"}),"Mark Unresolved"]}):e.jsxs(e.Fragment,{children:[e.jsx(x,{className:"h-4 w-4 mr-2"}),"Mark Resolved"]})}),(r.includes("association clerk")||r.includes("system admin"))&&e.jsxs(a,{variant:"outline",onClick:()=>i(!j),className:"text-blue-700 border-blue-300 hover:bg-blue-50",children:[e.jsx(o,{className:"h-4 w-4 mr-2"}),"Review Severity"]})]})]}),j&&e.jsxs(Y,{className:"mb-6 border-blue-200 bg-blue-50",children:[e.jsxs(Z,{children:[e.jsx(ee,{className:"text-lg text-blue-800",children:"Review Severity Level"}),e.jsx("p",{className:"text-sm text-blue-600",children:"As an association clerk, you can review and adjust the severity level assigned by the minibus owner."})]}),e.jsxs(se,{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsxs("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:["Current Severity: ",e.jsx("span",{className:"font-bold text-blue-600",children:(y=s.severity)==null?void 0:y.toUpperCase()}),s.original_severity&&s.original_severity!==s.severity&&e.jsxs("span",{className:"text-sm text-gray-500 ml-2",children:["(Originally: ",(_=s.original_severity)==null?void 0:_.toUpperCase(),")"]})]}),e.jsxs(re,{value:u,onValueChange:v,children:[e.jsx(ae,{className:"w-full",children:e.jsx(le,{placeholder:"Select severity level"})}),e.jsxs(ie,{children:[e.jsx(p,{value:"low",children:e.jsx("div",{className:"flex flex-col",children:e.jsx("span",{className:"font-medium",children:"Low (5 points deducted)"})})}),e.jsx(p,{value:"medium",children:e.jsx("div",{className:"flex flex-col",children:e.jsx("span",{className:"font-medium",children:"Medium (10 points deducted)"})})}),e.jsx(p,{value:"high",children:e.jsx("div",{className:"flex flex-col",children:e.jsx("span",{className:"font-medium",children:"High (20 points deducted)"})})})]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Review Notes (Optional)"}),e.jsx(ne,{value:N,onChange:t=>n(t.target.value),placeholder:"Explain the reason for severity adjustment...",className:"min-h-[80px]"})]}),e.jsxs("div",{className:"flex gap-3",children:[e.jsx(a,{onClick:X,className:"bg-blue-600 hover:bg-blue-700",children:"Update Severity"}),e.jsx(a,{variant:"outline",onClick:()=>{i(!1),v(s.severity),n("")},children:"Cancel"})]})]})]}),e.jsx("div",{className:"bg-white border-b-2 border-gray-200 p-6 mb-8",children:e.jsxs("div",{className:"flex flex-wrap justify-between items-center gap-6",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(o,{className:"h-6 w-6 text-red-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500 uppercase tracking-wide",children:"Severity"}),q(s.severity)]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"h-6 w-6 bg-orange-100 rounded-full flex items-center justify-center",children:e.jsxs("span",{className:"text-orange-600 font-bold text-xs",children:["-",s.points_deducted]})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500 uppercase tracking-wide",children:"Points Deducted"}),e.jsx("p",{className:"text-lg font-bold text-orange-600",children:s.points_deducted})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[s.resolution_status==="resolved"?e.jsx(x,{className:"h-6 w-6 text-green-500"}):e.jsx(h,{className:"h-6 w-6 text-orange-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500 uppercase tracking-wide",children:"Status"}),f(s.resolution_status)]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx($,{className:"h-6 w-6 text-green-500"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-gray-500 uppercase tracking-wide",children:"Trust Score"}),e.jsx("p",{className:"text-sm font-medium",children:w(((k=s.offender)==null?void 0:k.trust_score)||0)})]})]})]})}),e.jsxs("div",{className:"bg-blue-50 p-6 rounded-lg mb-6",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-6",children:[e.jsx(g,{className:"h-6 w-6 text-blue-600"}),e.jsx("h3",{className:"text-xl font-semibold text-blue-900",children:"Misconduct Details"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-blue-600 uppercase tracking-wide font-medium mb-2",children:"Misconduct Type"}),e.jsx("p",{className:"text-lg font-semibold text-blue-900",children:s.name})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-blue-600 uppercase tracking-wide font-medium mb-2",children:"Full Offense Date"}),e.jsx("p",{className:"text-lg text-blue-800 font-medium",children:new Date(s.offense_date).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"})})]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("p",{className:"text-sm text-blue-600 uppercase tracking-wide font-medium mb-3",children:"Description"}),e.jsx("div",{className:"bg-white p-4 rounded-lg border border-blue-200",children:e.jsx("p",{className:"text-blue-900 leading-relaxed",children:s.description||"No description provided."})})]}),s.evidence_file&&e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-blue-600 uppercase tracking-wide font-medium mb-3",children:"Evidence"}),e.jsx("div",{className:"bg-white p-4 rounded-lg border border-blue-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(g,{className:"h-6 w-6 text-blue-600"}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-blue-900",children:s.evidence_file.split("/").pop()}),e.jsx("p",{className:"text-sm text-blue-600",children:"Evidence file"})]})]}),e.jsxs(a,{variant:"outline",size:"sm",onClick:()=>window.open(`/storage/${s.evidence_file}`,"_blank"),className:"text-blue-600 border-blue-300 hover:bg-blue-100",children:[e.jsx(xe,{className:"h-4 w-4 mr-2"}),"View Evidence"]})]})})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-1 mb-8",children:[!b&&e.jsxs("div",{className:"bg-purple-50 p-6",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-4",children:[e.jsx($,{className:"h-5 w-5 text-purple-600"}),e.jsx("h3",{className:"font-semibold text-purple-900",children:"Driver & Vehicle"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-purple-600 uppercase tracking-wide font-medium",children:"Driver Name"}),e.jsxs("p",{className:"text-sm font-semibold text-purple-900 mt-1",children:[(S=s.offender)==null?void 0:S.first_name," ",(C=s.offender)==null?void 0:C.last_name]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-purple-600 uppercase tracking-wide font-medium",children:"Phone Number"}),e.jsx("p",{className:"text-sm text-purple-800 mt-1",children:(R=s.offender)==null?void 0:R.phone_number})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-purple-600 uppercase tracking-wide font-medium",children:"Trust Score"}),e.jsx("div",{className:"mt-1",children:w((D=s.offender)==null?void 0:D.trust_score)})]}),s.minibus&&e.jsxs("div",{className:"pt-4 border-t border-purple-200",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-3",children:[e.jsx(me,{className:"h-4 w-4 text-purple-600"}),e.jsx("p",{className:"text-xs text-purple-600 uppercase tracking-wide font-medium",children:"Vehicle Information"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-purple-500 uppercase tracking-wide",children:"Number Plate"}),e.jsx("p",{className:"text-lg font-bold text-purple-900 mt-1",children:(M=s.minibus)==null?void 0:M.number_plate})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-purple-500 uppercase tracking-wide",children:"Route"}),e.jsx("p",{className:"text-sm text-purple-800 mt-1",children:((T=(L=s.minibus)==null?void 0:L.route)==null?void 0:T.route_name)||"No route assigned"})]})]})]})]})]}),!b&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"bg-green-50 p-6",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-4",children:[s.resolution_status==="resolved"?e.jsx(x,{className:"h-5 w-5 text-green-600"}):e.jsx(h,{className:"h-5 w-5 text-orange-600"}),e.jsx("h3",{className:"font-semibold text-green-900",children:"Status & Resolution"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-green-600 uppercase tracking-wide font-medium",children:"Current Status"}),e.jsx("div",{className:"mt-1",children:f(s.resolution_status)})]}),s.resolution_status==="resolved"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-green-600 uppercase tracking-wide font-medium",children:"Resolved By"}),e.jsx("p",{className:"text-sm text-green-800 font-semibold mt-1",children:s.resolved_by?`${s.resolved_by.first_name} ${s.resolved_by.last_name}`:"System"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-green-600 uppercase tracking-wide font-medium",children:"Resolved Date"}),e.jsx("p",{className:"text-sm text-green-800 mt-1",children:new Date(s.resolved_at).toLocaleDateString()})]})]}),s.severity_reviewed_by&&e.jsxs("div",{className:"pt-4 border-t border-green-200",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-3",children:[e.jsx(o,{className:"h-4 w-4 text-green-600"}),e.jsx("p",{className:"text-xs text-green-600 uppercase tracking-wide font-medium",children:"Severity Review"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-green-500 uppercase tracking-wide",children:"Reviewed By"}),e.jsxs("p",{className:"text-sm text-green-800 font-semibold mt-1",children:[(U=s.severity_reviewed_by)==null?void 0:U.first_name," ",(B=s.severity_reviewed_by)==null?void 0:B.last_name]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-green-500 uppercase tracking-wide",children:"Review Date"}),e.jsx("p",{className:"text-sm text-green-800 mt-1",children:new Date(s.severity_reviewed_at).toLocaleDateString()})]}),s.original_severity&&s.original_severity!==s.severity&&e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-green-500 uppercase tracking-wide",children:"Severity Change"}),e.jsxs("div",{className:"flex items-center gap-2 mt-1",children:[e.jsx("span",{className:"px-2 py-1 bg-red-100 text-red-700 rounded text-xs font-medium",children:(E=s.original_severity)==null?void 0:E.toUpperCase()}),e.jsx("span",{className:"text-green-400",children:"→"}),e.jsx("span",{className:"px-2 py-1 bg-green-100 text-green-700 rounded text-xs font-medium",children:(V=s.severity)==null?void 0:V.toUpperCase()})]})]}),s.severity_review_notes&&e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-green-500 uppercase tracking-wide",children:"Review Notes"}),e.jsx("div",{className:"bg-white p-3 rounded border border-green-200 mt-1",children:e.jsx("p",{className:"text-green-900 text-sm leading-relaxed",children:s.severity_review_notes})})]})]})]})]})]}),e.jsxs("div",{className:"bg-orange-50 p-6",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-4",children:[e.jsx(g,{className:"h-5 w-5 text-orange-600"}),e.jsx("h3",{className:"font-semibold text-orange-900",children:"Report Information"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-orange-600 uppercase tracking-wide font-medium",children:"Reported By"}),e.jsxs("p",{className:"text-sm font-semibold text-orange-900 mt-1",children:[(H=s.reported_by)==null?void 0:H.first_name," ",(O=s.reported_by)==null?void 0:O.last_name]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-orange-600 uppercase tracking-wide font-medium",children:"Report Date"}),e.jsx("p",{className:"text-sm text-orange-800 mt-1",children:new Date(s.created_at).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"})})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-orange-600 uppercase tracking-wide font-medium",children:"Report Time"}),e.jsx("p",{className:"text-sm text-orange-800 mt-1",children:new Date(s.created_at).toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit",hour12:!0})})]})]})]})]})]})]})]})}export{Me as default};
