import React, { useState } from 'react';
import { Head, Link } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Plus, Settings, History, DollarSign, Calendar, User, BarChart3, Edit } from 'lucide-react';

export default function FeeManagementIndex({ currentFees, allActiveFees, feeHistory }) {
    const [showAllRecent, setShowAllRecent] = useState(false);
    const breadcrumbs = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Fee Management', href: '/fee-settings' },
    ];

    const getStatusBadge = (fee) => {
        if (!fee) return <Badge variant="secondary">No Fee Set</Badge>;

        const today = new Date();
        const effectiveFrom = new Date(fee.effective_from);

        if (!fee.is_active) return <Badge variant="secondary">Inactive</Badge>;
        if (effectiveFrom > today) return <Badge variant="outline">Future</Badge>;
        return <Badge className="bg-green-100 text-green-800 border border-green-600">Active</Badge>;
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Fee Management" />

            <div className="container mx-auto px-4 py-8">
                <div className="space-y-8">
                    {/* Header */}
                    <div className="flex flex-col sm:flex-row sm:items-center gap-4 mb-6">
                        <div className="flex-1">
                            <h1 className="text-2xl font-bold text-gray-900">Fee Management</h1>
                            <p className="text-gray-600 mt-1">
                                Manage registration and affiliation fees for the association
                            </p>
                        </div>
                        <Link href={route('fee-settings.create')}>
                            <Button className="bg-blue-600 hover:bg-blue-700">
                                <Plus className="h-4 w-4 mr-2" />
                                Set New Fee
                            </Button>
                        </Link>
                    </div>

                    {/* Current Fees Overview */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {/* Registration Fee */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <DollarSign className="h-5 w-5 text-blue-600" />
                                    Registration Fee
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                {currentFees.registration ? (
                                    <div className="space-y-4">
                                        <div className="flex items-center justify-between">
                                            <span className="text-2xl font-bold text-green-600">
                                                MK {parseFloat(currentFees.registration.amount).toLocaleString()}
                                            </span>
                                            {getStatusBadge(currentFees.registration)}
                                        </div>
                                        <div className="text-sm text-gray-600">
                                            <div className="flex items-center gap-2 mb-1">
                                                <Calendar className="h-4 w-4" />
                                                Effective from: {new Date(currentFees.registration.effective_from).toLocaleDateString()}
                                            </div>
                                            {currentFees.registration.description && (
                                                <p className="mt-2">{currentFees.registration.description}</p>
                                            )}
                                        </div>
                                        <div className="flex gap-2">
                                            <Link href={route('fee-settings.edit', currentFees.registration.id)}>
                                                <Button variant="outline" size="sm" className="bg-blue-50 hover:bg-blue-100 text-blue-700 border-blue-200">
                                                    <Edit className="h-3 w-3 mr-1" />
                                                    Edit
                                                </Button>
                                            </Link>
                                            <Link href={route('fee-settings.history', 'registration')}>
                                                <Button variant="outline" size="sm" className="bg-gray-50 hover:bg-gray-100 text-gray-700 border-gray-200">
                                                    <History className="h-3 w-3 mr-1" />
                                                    History
                                                </Button>
                                            </Link>
                                        </div>
                                    </div>
                                ) : (
                                    <div className="text-center py-6">
                                        <DollarSign className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                                        <p className="text-gray-500 mb-4">No registration fee set</p>
                                        <Link href={route('fee-settings.create')}>
                                            <Button variant="outline">
                                                <Plus className="h-4 w-4 mr-2" />
                                                Set Registration Fee
                                            </Button>
                                        </Link>
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        {/* Affiliation Fee */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <DollarSign className="h-5 w-5 text-green-600" />
                                    Affiliation Fee
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                {currentFees.affiliation ? (
                                    <div className="space-y-4">
                                        <div className="flex items-center justify-between">
                                            <span className="text-2xl font-bold text-green-600">
                                                MK {parseFloat(currentFees.affiliation.amount).toLocaleString()}
                                            </span>
                                            {getStatusBadge(currentFees.affiliation)}
                                        </div>
                                        <div className="text-sm text-gray-600">
                                            <div className="flex items-center gap-2 mb-1">
                                                <Calendar className="h-4 w-4" />
                                                Effective from: {new Date(currentFees.affiliation.effective_from).toLocaleDateString()}
                                            </div>
                                            {currentFees.affiliation.description && (
                                                <p className="mt-2">{currentFees.affiliation.description}</p>
                                            )}
                                        </div>
                                        <div className="flex gap-2">
                                            <Link href={route('fee-settings.edit', currentFees.affiliation.id)}>
                                                <Button variant="outline" size="sm" className="bg-blue-50 hover:bg-blue-100 text-blue-700 border-blue-200">
                                                    <Edit className="h-3 w-3 mr-1" />
                                                    Edit
                                                </Button>
                                            </Link>
                                            <Link href={route('fee-settings.history', 'affiliation')}>
                                                <Button variant="outline" size="sm" className="bg-gray-50 hover:bg-gray-100 text-gray-700 border-gray-200">
                                                    <History className="h-3 w-3 mr-1" />
                                                    History
                                                </Button>
                                            </Link>
                                        </div>
                                    </div>
                                ) : (
                                    <div className="text-center py-6">
                                        <DollarSign className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                                        <p className="text-gray-500 mb-4">No affiliation fee set</p>
                                        <Link href={route('fee-settings.create')}>
                                            <Button variant="outline">
                                                <Plus className="h-4 w-4 mr-2" />
                                                Set Affiliation Fee
                                            </Button>
                                        </Link>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>

                    {/* All Active Fees (Including Future) */}
                    <Card>
                        <CardHeader>
                            <CardTitle>All Active Fee Settings</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-6">
                                {/* Registration Fees */}
                                <div>
                                    <h4 className="font-semibold text-lg mb-3 text-blue-600">Registration Fees</h4>
                                    <div className="space-y-3">
                                        {allActiveFees.registration.length > 0 ? (
                                            allActiveFees.registration.map((fee) => (
                                                <div key={fee.id} className="flex items-center justify-between p-4 border rounded-lg bg-gray-50">
                                                    <div className="flex items-center gap-4">
                                                        <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                                                            <DollarSign className="h-6 w-6 text-blue-600" />
                                                        </div>
                                                        <div>
                                                            <p className="font-medium text-lg">MK {parseFloat(fee.amount).toLocaleString()}</p>
                                                            <p className="text-sm text-gray-600">
                                                                Effective from: {new Date(fee.effective_from).toLocaleDateString()}
                                                            </p>
                                                            {fee.description && (
                                                                <p className="text-sm text-gray-500 mt-1">{fee.description}</p>
                                                            )}
                                                        </div>
                                                    </div>
                                                    <div className="text-right">
                                                        {getStatusBadge(fee)}
                                                        <div className="mt-2">
                                                            <Link href={route('fee-settings.edit', fee.id)}>
                                                                <Button variant="outline" size="sm" className="bg-blue-50 hover:bg-blue-100 text-blue-700 border-blue-200">
                                                                    <Edit className="h-3 w-3 mr-1" />
                                                                    Edit
                                                                </Button>
                                                            </Link>
                                                        </div>
                                                    </div>
                                                </div>
                                            ))
                                        ) : (
                                            <div className="text-center py-6">
                                                <DollarSign className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                                                <p className="text-gray-500">No registration fees set</p>
                                            </div>
                                        )}
                                    </div>
                                </div>

                                {/* Affiliation Fees */}
                                <div>
                                    <h4 className="font-semibold text-lg mb-3 text-green-600">Affiliation Fees</h4>
                                    <div className="space-y-3">
                                        {allActiveFees.affiliation.length > 0 ? (
                                            allActiveFees.affiliation.map((fee) => (
                                                <div key={fee.id} className="flex items-center justify-between p-4 border rounded-lg bg-gray-50">
                                                    <div className="flex items-center gap-4">
                                                        <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                                                            <DollarSign className="h-6 w-6 text-green-600" />
                                                        </div>
                                                        <div>
                                                            <p className="font-medium text-lg">MK {parseFloat(fee.amount).toLocaleString()}</p>
                                                            <p className="text-sm text-gray-600">
                                                                Effective from: {new Date(fee.effective_from).toLocaleDateString()}
                                                            </p>
                                                            {fee.description && (
                                                                <p className="text-sm text-gray-500 mt-1">{fee.description}</p>
                                                            )}
                                                        </div>
                                                    </div>
                                                    <div className="text-right">
                                                        {getStatusBadge(fee)}
                                                        <div className="mt-2">
                                                            <Link href={route('fee-settings.edit', fee.id)}>
                                                                <Button variant="outline" size="sm" className="bg-blue-50 hover:bg-blue-100 text-blue-700 border-blue-200">
                                                                    <Edit className="h-3 w-3 mr-1" />
                                                                    Edit
                                                                </Button>
                                                            </Link>
                                                        </div>
                                                    </div>
                                                </div>
                                            ))
                                        ) : (
                                            <div className="text-center py-6">
                                                <DollarSign className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                                                <p className="text-gray-500">No affiliation fees set</p>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Recent Fee Changes */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Recent Fee Changes</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {([...feeHistory.registration, ...feeHistory.affiliation]
                                    .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
                                    .slice(0, showAllRecent ? undefined : 5)
                                ).map((fee) => (
                                    <div key={fee.id} className="flex items-center justify-between p-3 border rounded-lg">
                                        <div className="flex items-center gap-3">
                                            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                                <DollarSign className="h-5 w-5 text-blue-600" />
                                            </div>
                                            <div>
                                                <p className="font-medium capitalize">{fee.fee_type} Fee</p>
                                                <p className="text-sm text-gray-600">
                                                    MK {parseFloat(fee.amount).toLocaleString()} • Effective {new Date(fee.effective_from).toLocaleDateString()}
                                                </p>
                                            </div>
                                        </div>
                                        <div className="text-right">
                                            <p className="text-sm text-gray-600">
                                                {new Date(fee.created_at).toLocaleDateString()}
                                            </p>
                                            <p className="text-xs text-gray-500">
                                                by {fee.created_by?.first_name} {fee.created_by?.last_name}
                                            </p>
                                        </div>
                                    </div>
                                ))}
                                {(feeHistory.registration.length > 0 || feeHistory.affiliation.length > 0) &&
                                    ([...feeHistory.registration, ...feeHistory.affiliation].length > 5) && (
                                        <div className="flex justify-center pt-2">
                                            <Button variant="outline" size="sm" onClick={() => setShowAllRecent(v => !v)}>
                                                {showAllRecent ? 'Show Less' : 'Show More'}
                                            </Button>
                                        </div>
                                    )}
                                {(feeHistory.registration.length === 0 && feeHistory.affiliation.length === 0) && (
                                    <div className="text-center py-8">
                                        <History className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                                        <p className="text-gray-500">No fee changes recorded yet</p>
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
} 