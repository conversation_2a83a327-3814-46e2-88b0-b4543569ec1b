<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Contracts\Auth\CanResetPassword;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Notifications\Notifiable;

/**
 * @property int $id
 * @property string $first_name
 * @property string $last_name
 * @property string|null $gender
 * @property string|null $district
 * @property string|null $village
 * @property string $email
 * @property string|null $phone_number
 * @property \Carbon\Carbon|null $email_verified_at
 * @property \Carbon\Carbon|null $archived_at
 * @property string $password
 * @property string|null $remember_token
 * @property string|null $commitment_statement
 * @property \Carbon\Carbon|null $joining_date
 * @property string|null $national_id_path
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\Spatie\Permission\Models\Role[] $roles
 */
class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasRoles;
    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'first_name',
        'last_name',
        'gender',
        'district',
        'village',
        'email',
        'phone_number',
        'password',
        'commitment_statement',
        'joining_date',
        'national_id_path',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'archived_at' => 'datetime',
            'joining_date' => 'datetime',
        ];
    }



    /**
     * Get all minibuses owned by this user (user should have the 'minibus_owner' role)
     */
    public function minibuses(): HasMany
    {
        return $this->hasMany(Minibus::class, 'owner_id');
    }

    public function drivers(): HasMany
    {
        return $this->hasMany(Driver::class, 'owner_id');
    }

    public function misconducts(): MorphMany
    {
        return $this->morphMany(Misconduct::class, 'offender');
    }

    public function memberships()
    {
        return $this->hasMany(Membership::class, 'user_id');
    }

    public function isClerk()
    {
        return $this->roles()->where('name', 'clerk')->exists();
    }

    public function scopeNotArchived($query)
    {
        return $query->whereNull('archived_at');
    }

    public function archive()
    {
        $this->archived_at = now();
        $this->save();
    }

    public function unarchive()
    {
        $this->forceFill(['archived_at' => null])->save();
    }
}
