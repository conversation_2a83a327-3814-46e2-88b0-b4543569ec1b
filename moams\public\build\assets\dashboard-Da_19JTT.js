import{d as f,j as e,H as j}from"./app-DL-qYY5V.js";import{A as g}from"./app-layout-YqstQnqE.js";import R from"./OwnerDashboard-BdCAAHt3.js";import D from"./ClerkDashboard-MceZYqJV.js";import q from"./ManagerDashboard-vjn03hsM.js";import F from"./AdminDashboard-DSRUc2oh.js";import"./index-1tPPImJd.js";import"./index-BTzg1GwG.js";import"./app-logo-icon-BnXlkpcX.js";import"./bus-CKTBKI0T.js";import"./dollar-sign-Cx0-nQIX.js";import"./users-DNGXY-sJ.js";import"./clipboard-list-CxRumT8v.js";import"./badge-check-BVA7OvLi.js";const N=[{title:"Dashboard",href:"/dashboard"}];function J(t){var m,c,d,l,u;const{auth:n,userRoles:a=[]}=f().props,i=t.user||(n==null?void 0:n.user),{minibuses:p=[],drivers:b=[],unpaidMemberships:h=[],members:v=[],memberships:w=[],misconducts:y=[],clearanceRequests:k=[],transferRequests:A=[],users:x=[],stats:s={},message:o}=t;let r=null;return a.includes("system admin")?r=e.jsx(F,{user:i,users:x,stats:s}):a.includes("association manager")?r=e.jsx(q,{user:i,stats:s,misconducts:((m=s.misconducts)==null?void 0:m.all)??0,unpaidMemberships:((c=s.memberships)==null?void 0:c.unpaid)??0,currentFees:t.currentFees}):a.includes("association clerk")?r=e.jsx(D,{user:i,stats:s,misconducts:((d=s.misconducts)==null?void 0:d.all)??0,clearanceRequests:((l=s.clearanceRequests)==null?void 0:l.pending)??0,transferRequests:((u=s.transferRequests)==null?void 0:u.pending)??0,currentFees:t.currentFees}):a.includes("minibus owner")?r=e.jsx(R,{user:i,minibuses:p,drivers:b,unpaidMemberships:h,stats:s,currentFees:t.currentFees}):r=e.jsxs("div",{className:"text-center p-8",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-4",children:"Welcome"}),e.jsx("p",{className:"text-gray-600",children:"Your dashboard will appear here."})]}),e.jsxs(g,{breadcrumbs:N,children:[e.jsx(j,{title:"Dashboard"}),e.jsxs("div",{className:"p-4",children:[o&&e.jsx("div",{className:"mb-4 text-center text-red-600 font-semibold",children:o}),r]})]})}export{J as default};
