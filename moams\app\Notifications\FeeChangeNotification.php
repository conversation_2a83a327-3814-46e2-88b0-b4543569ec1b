<?php

namespace App\Notifications;

use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\FeeSetting;

class FeeChangeNotification extends Notification
{
    // Using sync processing and database notifications to avoid email service limits

    public $feeSetting;
    public $changeType;

    /**
     * Create a new notification instance.
     */
    public function __construct(FeeSetting $feeSetting, string $changeType = 'created')
    {
        $this->feeSetting = $feeSetting;
        $this->changeType = $changeType;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database']; // Using database notifications only to avoid email service limits
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $feeType = ucfirst($this->feeSetting->fee_type);
        $amount = number_format($this->feeSetting->amount, 2);
        $effectiveDate = $this->feeSetting->effective_from->format('F j, Y');

        $subject = match ($this->changeType) {
            'created' => "New {$feeType} Fee Set",
            'updated' => "{$feeType} Fee Updated",
            default => "Fee Change Notification"
        };

        $message = match ($this->changeType) {
            'created' => "A new {$feeType} fee of MK {$amount} has been set, effective from {$effectiveDate}.",
            'updated' => "The {$feeType} fee has been updated to MK {$amount}, effective from {$effectiveDate}.",
            default => "The {$feeType} fee has been modified."
        };

        return (new MailMessage)
            ->subject($subject)
            ->greeting("Hello {$notifiable->first_name},")
            ->line($message)
            ->when($this->feeSetting->description, function ($mail) {
                return $mail->line("Reason: {$this->feeSetting->description}");
            })
            ->when($this->feeSetting->createdBy, function ($mail) {
                return $mail->line("This change was made by: {$this->feeSetting->createdBy->first_name} {$this->feeSetting->createdBy->last_name}");
            })
            ->action('View Fee Management', url('/fee-settings'))
            ->line('Thank you for using our system!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'fee_setting_id' => $this->feeSetting->id,
            'fee_type' => $this->feeSetting->fee_type,
            'amount' => $this->feeSetting->amount,
            'effective_from' => $this->feeSetting->effective_from,
            'change_type' => $this->changeType,
            'created_by' => $this->feeSetting->created_by,
            'message' => $this->getNotificationMessage(),
        ];
    }

    /**
     * Get the notification message for database storage
     */
    private function getNotificationMessage(): string
    {
        $feeType = ucfirst($this->feeSetting->fee_type);
        $amount = number_format($this->feeSetting->amount, 2);
        $effectiveDate = $this->feeSetting->effective_from->format('M j, Y');

        return match ($this->changeType) {
            'created' => "New {$feeType} fee of MK {$amount} set, effective {$effectiveDate}",
            'updated' => "{$feeType} fee updated to MK {$amount}, effective {$effectiveDate}",
            default => "{$feeType} fee modified"
        };
    }
}