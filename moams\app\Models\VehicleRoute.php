<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class VehicleRoute extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'status',
    ];

    public function minibuses(): HasMany
    {
        return $this->hasMany(Minibus::class, 'route_id');
    }
}