import{d as S,u as F,l as $,j as e,H as M,B as u,L as U}from"./app-DL-qYY5V.js";import{n as D}from"./navigation-DAA2N51J.js";import{I as t}from"./input-error-C6jcuIY6.js";import{I as o}from"./input-Dm4SEXxy.js";import{L as n}from"./label-e3QxUH-L.js";import{S as j,a as p,b as g,c as f,e as N,d as h}from"./select-Cp8NjZe8.js";import{A as E}from"./app-layout-YqstQnqE.js";import{A as R}from"./arrow-left-DCW23wrL.js";import{L as V}from"./loader-circle-D3J3XKS7.js";import"./index-BTzg1GwG.js";import"./index-1tPPImJd.js";import"./index-uC6ZAdKJ.js";import"./app-logo-icon-BnXlkpcX.js";import"./bus-CKTBKI0T.js";import"./dollar-sign-Cx0-nQIX.js";import"./users-DNGXY-sJ.js";import"./clipboard-list-CxRumT8v.js";const B=["Lilongwe","Blantyre","Mzuzu","Zomba","Kasungu","Ntcheu","Mangochi","Salima","Dowa","Ntchisi","Dedza","Nkhotakota","Mchinji","Nkhatabay","Rumphi","Chitipa","Karonga","Thyolo","Mulanje","Phalombe","Chikwawa","Nsanje","Balaka","Machinga"];function ae(){const{user:s,roles:_,userRoles:b,flash:I}=S().props,{data:i,setData:m,patch:C,processing:l,errors:r}=F({first_name:s.first_name,last_name:s.last_name,gender:s.gender,phone_number:s.phone_number,role:s.roles.length>0?s.roles[0].name:"",district:s.district||"",village:s.village||""}),d=b&&b.includes("association clerk");$.useEffect(()=>{d&&m("role","minibus owner")},[d,m]);const v=[{title:"Dashboard",href:"/dashboard"},{title:"User Management",href:"/admin/users"},{title:`${s.first_name} ${s.last_name}`,href:`/admin/users/${s.id}`},{title:"Edit User",href:`/admin/users/${s.id}/edit`}],c=a=>{const{id:x,value:L}=a.target;m(x,L)},w=a=>{m("gender",a)},y=a=>{m("role",a)},k=a=>{a.preventDefault();const x=d?`/membership-management/${s.id}`:`/admin/users/${s.id}`;C(x)};return e.jsxs(E,{breadcrumbs:v,children:[e.jsx(M,{title:`Edit User - ${s.first_name} ${s.last_name}`}),e.jsxs("div",{className:"p-8",children:[e.jsx("div",{className:"flex justify-between items-center mb-8",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs(u,{variant:"outline",onClick:()=>D(v),className:"w-full sm:w-auto",children:[e.jsx(R,{className:"h-4 w-4 mr-2"}),"Back"]}),e.jsx("div",{children:e.jsxs("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:["Update information for ",s.first_name," ",s.last_name]})})]})}),e.jsx("div",{className:"max-w-2xl",children:e.jsxs("form",{className:"space-y-6",onSubmit:k,children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx(n,{htmlFor:"first_name",children:"First name"}),e.jsx(o,{id:"first_name",type:"text",value:i.first_name,onChange:c,disabled:l,placeholder:"First name"}),e.jsx(t,{message:r.first_name,className:"mt-2"})]}),e.jsxs("div",{children:[e.jsx(n,{htmlFor:"last_name",children:"Last name"}),e.jsx(o,{id:"last_name",type:"text",value:i.last_name,onChange:c,disabled:l,placeholder:"Last name"}),e.jsx(t,{message:r.last_name,className:"mt-2"})]})]}),e.jsxs("div",{children:[e.jsx(n,{htmlFor:"gender",children:"Gender"}),e.jsxs(j,{value:i.gender,onValueChange:w,disabled:l,children:[e.jsx(p,{children:e.jsx(g,{placeholder:"Select gender"})}),e.jsx(f,{children:e.jsxs(N,{children:[e.jsx(h,{value:"male",children:"Male"}),e.jsx(h,{value:"female",children:"Female"})]})})]}),e.jsx(t,{message:r.gender,className:"mt-2"})]}),e.jsxs("div",{className:"grid gap-2",children:[e.jsx(n,{htmlFor:"district",className:"text-gray-700",children:"District"}),e.jsxs(j,{value:i.district,onValueChange:a=>m("district",a),disabled:l,children:[e.jsx(p,{className:r.district?"border-red-500":"",children:e.jsx(g,{placeholder:"Select district"})}),e.jsx(f,{children:B.map(a=>e.jsx(h,{value:a,children:a},a))})]}),e.jsx(t,{className:"mt-2",message:r.district})]}),e.jsxs("div",{className:"grid gap-2",children:[e.jsx(n,{htmlFor:"village",className:"text-gray-700",children:"Village/Town"}),e.jsx(o,{id:"village",className:"mt-1 block w-full",value:i.village,onChange:c,required:!0,autoComplete:"village",placeholder:"Village/Town",disabled:l}),e.jsx(t,{className:"mt-2",message:r.village})]}),e.jsxs("div",{children:[e.jsx(n,{htmlFor:"phone_number",children:"Phone number"}),e.jsx(o,{id:"phone_number",type:"tel",value:i.phone_number,onChange:c,disabled:l,placeholder:"0889... or 0995... or +26599...."}),e.jsx(t,{message:r.phone_number,className:"mt-2"})]}),!d&&e.jsxs("div",{children:[e.jsx(n,{htmlFor:"role",children:"Role"}),e.jsxs(j,{value:i.role,onValueChange:y,disabled:l,children:[e.jsx(p,{children:e.jsx(g,{placeholder:"Select role"})}),e.jsx(f,{children:e.jsx(N,{children:_.map(a=>e.jsx(h,{value:a,children:a},a))})})]}),e.jsx(t,{message:r.role,className:"mt-2"})]}),e.jsxs("div",{className:"flex space-x-4 pt-6",children:[e.jsxs(u,{type:"submit",className:"bg-blue-600 hover:bg-blue-700",disabled:l,children:[l&&e.jsx(V,{className:"h-4 w-4 animate-spin mr-2"}),"Update User"]}),e.jsx(U,{href:d?"/membership-management":`/admin/users/${s.id}`,children:e.jsx(u,{type:"button",variant:"outline",children:"Cancel"})})]})]})})]})]})}export{ae as default};
