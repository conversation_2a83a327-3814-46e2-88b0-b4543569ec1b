import{r as j,j as e,B as J,C as l,a as u,b as f,c as r,e as y}from"./app-DL-qYY5V.js";import{A as W,h as X}from"./app-layout-YqstQnqE.js";import{S as b,a as N,b as v,c as w,d as c}from"./select-Cp8NjZe8.js";import{n as z}from"./navigation-DAA2N51J.js";import{C as G}from"./chevron-left-DFeVEtK7.js";import{C as H}from"./chart-column-DHsDidJk.js";import{D as K}from"./download-fjRG0KHX.js";import{C as Q}from"./calendar-Bzuvt9Ns.js";import{D as Z}from"./dollar-sign-Cx0-nQIX.js";import{a as ee}from"./users-DNGXY-sJ.js";import{T as se}from"./trending-up-CrkLNaAi.js";import"./index-1tPPImJd.js";import"./index-BTzg1GwG.js";import"./app-logo-icon-BnXlkpcX.js";import"./bus-CKTBKI0T.js";import"./clipboard-list-CxRumT8v.js";import"./index-uC6ZAdKJ.js";function Ne({summary:p={},trends:ae=[],feeTypeBreakdown:R=[],paymentMethodBreakdown:A=[],topPayingMembers:L=[],selectedPeriod:k="monthly",selectedYear:F=new Date().getFullYear(),selectedMonth:M=new Date().getMonth()+1,userRoles:te=[]}){const[t,T]=j.useState(k),[i,U]=j.useState(F),[o,_]=j.useState(M),[C,S]=j.useState(!1),D=[{title:"Dashboard",href:"/dashboard"},{title:"Payment Analytics",href:"/payment-analytics"}],$=s=>{T(s);const a=new URLSearchParams({period:s,year:i,...s==="monthly"?{month:o}:{}});y.get(`/payment-analytics?${a.toString()}`)},E=s=>{U(s);const a=new URLSearchParams({period:t,year:s,...t==="monthly"?{month:o}:{}});y.get(`/payment-analytics?${a.toString()}`)},Y=s=>{_(s);const a=new URLSearchParams({period:t,year:i,month:s});y.get(`/payment-analytics?${a.toString()}`)},B=async()=>{var s;try{S(!0);const a=new URLSearchParams({period:t,year:parseInt(i)||new Date().getFullYear(),...t==="monthly"?{month:parseInt(o)||new Date().getMonth()+1}:{}}),d=await fetch(`/payment-analytics/report?${a.toString()}`,{method:"GET",headers:{Accept:"application/pdf","X-Requested-With":"XMLHttpRequest","X-CSRF-TOKEN":((s=document.querySelector('meta[name="csrf-token"]'))==null?void 0:s.getAttribute("content"))||""},credentials:"same-origin"});if(!d.ok){let x="Failed to generate report. Please try again or contact support.";try{const g=await d.json();g.error&&(x=g.error)}catch{try{const h=await d.text();h.includes("Unauthenticated")?x="Please log in again to download the report.":(h.includes("403")||h.includes("Access denied"))&&(x="You do not have permission to download reports.")}catch(h){console.error("Error parsing response:",h)}}console.error("Server error:",d.status,x),alert(x);return}const q=await d.blob(),P=window.URL.createObjectURL(q),m=document.createElement("a");m.href=P,m.download=`financial-report-${t}-${i}${t==="monthly"?`-${o}`:""}.pdf`,document.body.appendChild(m),m.click(),document.body.removeChild(m),window.URL.revokeObjectURL(P)}catch(a){console.error("Error downloading PDF:",a),alert("Failed to download report. Please check your internet connection and try again.")}finally{S(!1)}},n=s=>`MWK ${(Number(s)||0).toLocaleString()}`,I=new Date().getFullYear(),O=Array.from({length:5},(s,a)=>I-a),V=[{value:1,label:"January"},{value:2,label:"February"},{value:3,label:"March"},{value:4,label:"April"},{value:5,label:"May"},{value:6,label:"June"},{value:7,label:"July"},{value:8,label:"August"},{value:9,label:"September"},{value:10,label:"October"},{value:11,label:"November"},{value:12,label:"December"}];return e.jsx(W,{breadcrumbs:D,children:e.jsxs("div",{className:"max-w-7xl mx-auto p-6",children:[e.jsxs("div",{className:"flex flex-col gap-4 mb-6",children:[e.jsx("div",{className:"flex items-center gap-2",children:e.jsxs("button",{className:"flex items-center px-3 py-1 rounded hover:bg-gray-100 border border-gray-200",onClick:()=>z(D),children:[e.jsx(G,{className:"h-4 w-4 mr-1"})," Back"]})}),e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center gap-4 justify-between",children:[e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold text-gray-900 flex items-center gap-2",children:[e.jsx(H,{className:"h-6 w-6 text-blue-600"}),"Payment Analytics"]}),e.jsx("p",{className:"text-gray-600 mt-1",children:"Financial insights and payment trends"})]}),e.jsxs(J,{onClick:B,disabled:C,className:"bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2",children:[e.jsx(K,{className:"h-4 w-4"}),C?"Generating...":"Download Report"]})]})]}),e.jsxs(l,{className:"mb-6",children:[e.jsx(u,{children:e.jsxs(f,{className:"flex items-center gap-2",children:[e.jsx(Q,{className:"h-5 w-5"}),"Report Filters"]})}),e.jsx(r,{children:e.jsxs("div",{className:"flex flex-wrap gap-4",children:[e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Period"}),e.jsxs(b,{value:t,onValueChange:$,children:[e.jsx(N,{className:"w-40",children:e.jsx(v,{})}),e.jsxs(w,{children:[e.jsx(c,{value:"daily",children:"Daily"}),e.jsx(c,{value:"weekly",children:"Weekly"}),e.jsx(c,{value:"monthly",children:"Monthly"}),e.jsx(c,{value:"annually",children:"Annually"})]})]})]}),e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Year"}),e.jsxs(b,{value:i.toString(),onValueChange:s=>E(parseInt(s)),children:[e.jsx(N,{className:"w-32",children:e.jsx(v,{})}),e.jsx(w,{children:O.map(s=>e.jsx(c,{value:s.toString(),children:s},s))})]})]}),t==="monthly"&&e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Month"}),e.jsxs(b,{value:o.toString(),onValueChange:s=>Y(parseInt(s)),children:[e.jsx(N,{className:"w-40",children:e.jsx(v,{})}),e.jsx(w,{children:V.map(s=>e.jsx(c,{value:s.value.toString(),children:s.label},s.value))})]})]})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6",children:[e.jsx(l,{children:e.jsx(r,{className:"p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Revenue"}),e.jsx("p",{className:"text-2xl font-bold text-green-600",children:n(p.total_revenue||0)})]}),e.jsx(Z,{className:"h-8 w-8 text-green-600"})]})})}),e.jsx(l,{children:e.jsx(r,{className:"p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Payments"}),e.jsx("p",{className:"text-2xl font-bold text-blue-600",children:p.total_payments||0})]}),e.jsx(X,{className:"h-8 w-8 text-blue-600"})]})})}),e.jsx(l,{children:e.jsx(r,{className:"p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Registration Fees"}),e.jsx("p",{className:"text-2xl font-bold text-purple-600",children:n(p.registration_revenue||0)})]}),e.jsx(ee,{className:"h-8 w-8 text-purple-600"})]})})}),e.jsx(l,{children:e.jsx(r,{className:"p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Affiliation Fees"}),e.jsx("p",{className:"text-2xl font-bold text-orange-600",children:n(p.affiliation_revenue||0)})]}),e.jsx(se,{className:"h-8 w-8 text-orange-600"})]})})})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6",children:[e.jsxs(l,{children:[e.jsx(u,{children:e.jsx(f,{children:"Fee Type Breakdown"})}),e.jsx(r,{children:e.jsx("div",{className:"space-y-4",children:R.map((s,a)=>e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"capitalize font-medium",children:s.fee_type}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"font-bold",children:n(s.total_paid||0)}),e.jsxs("div",{className:"text-sm text-gray-500",children:[s.count||0," payments"]})]})]},a))})})]}),e.jsxs(l,{children:[e.jsx(u,{children:e.jsx(f,{children:"Payment Method Breakdown"})}),e.jsx(r,{children:e.jsx("div",{className:"space-y-4",children:A.map((s,a)=>e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"capitalize font-medium",children:s.payment_method}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"font-bold",children:n(s.total_amount||0)}),e.jsxs("div",{className:"text-sm text-gray-500",children:[s.count||0," payments"]})]})]},a))})})]})]}),e.jsxs(l,{children:[e.jsx(u,{children:e.jsx(f,{children:"Top Paying Members"})}),e.jsx(r,{children:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"border-b",children:[e.jsx("th",{className:"text-left py-2",children:"Member"}),e.jsx("th",{className:"text-left py-2",children:"Payments"}),e.jsx("th",{className:"text-left py-2",children:"Total Amount"})]})}),e.jsx("tbody",{children:L.map((s,a)=>e.jsxs("tr",{className:"border-b",children:[e.jsx("td",{className:"py-2",children:s.user?`${s.user.first_name} ${s.user.last_name}`:"Unknown User"}),e.jsx("td",{className:"py-2",children:s.payment_count||0}),e.jsx("td",{className:"py-2 font-medium",children:n(s.total_amount||0)})]},a))})]})})})]})]})})}export{Ne as default};
