import{n as m,o as u,r as f,j as l,p as v}from"./app-DL-qYY5V.js";var e=m();const E=u(e);var d=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],h=d.reduce((r,t)=>{const s=v(`Primitive.${t}`),i=f.forwardRef((o,a)=>{const{asChild:n,...c}=o,p=n?s:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),l.jsx(p,{...c,ref:a})});return i.displayName=`Primitive.${t}`,{...r,[t]:i}},{});function D(r,t){r&&e.flushSync(()=>r.dispatchEvent(t))}export{h as P,E as R,D as d,e as r};
