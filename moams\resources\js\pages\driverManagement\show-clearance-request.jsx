import { Head, useForm } from '@inertiajs/react';
import { navigateToLogicalParent } from '@/utils/navigation';
import { ArrowLeft, CheckCircle, XCircle, UserChe<PERSON>, Clock, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import AppLayout from '@/layouts/app-layout';
import InputError from '@/components/input-error';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { route } from 'ziggy-js';
import { router } from '@inertiajs/react';
import ConfirmDialog from '@/components/ui/confirm-dialog';
import { useState } from 'react';

export default function ShowClearanceRequest({ clearanceRequest, userRole, auth }) {
    const { data, setData, post, patch, processing, errors } = useForm({
        action: '',
        admin_notes: '',
    });


    const handleSubmit = (e) => {
        e.preventDefault();
        setProcessingAction(data.action);
        post(route('drivers.clearance-requests.process', clearanceRequest.id), {
            onFinish: () => setProcessingAction(null)
        });
    };

    const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
    const [loading, setLoading] = useState(false);
    // Track which button is being processed
    const [processingAction, setProcessingAction] = useState(null);

    const handleDelete = () => {
        setConfirmDialogOpen(true);
    };
    const handleCancel = () => setConfirmDialogOpen(false);
    const handleConfirm = async () => {
        setLoading(true);
        await window.axios.delete(route('drivers.clearance-requests.destroy', clearanceRequest.id))
            .then(() => router.visit(route('drivers.show', clearanceRequest.driver.id)));
        setLoading(false);
        setConfirmDialogOpen(false);
    };

    const breadcrumbs = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: userRole === 'minibus owner' ? 'My Drivers' : 'Driver Management', href: '/drivers' },
        { title: 'Clearance Requests', href: route('drivers.clearance-requests.index') },
        { title: `Request #${clearanceRequest.id}`, href: route('drivers.clearance-requests.show', clearanceRequest.id) },
    ];

    const getStatusIcon = (status) => {
        switch (status) {
            case 'pending':
                return <Clock className="h-5 w-5 text-amber-500" />;
            case 'approved':
                return <CheckCircle className="h-5 w-5 text-green-500" />;
            case 'rejected':
                return <XCircle className="h-5 w-5 text-red-500" />;
            default:
                return null;
        }
    };

    const getStatusBadge = (status) => {
        switch (status) {
            case 'pending':
                return <Badge variant="outline" className="text-amber-600 border-amber-600">Pending Review</Badge>;
            case 'approved':
                return <Badge variant="default" className="bg-green-600">Approved</Badge>;
            case 'rejected':
                return <Badge variant="destructive">Rejected</Badge>;
            default:
                return null;
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Clearance Request #${clearanceRequest.id}`} />
            <div className="container mx-auto px-4 py-8">
                <div className="max-w-4xl mx-auto">
                    <div className="flex flex-col sm:flex-row gap-2 sm:gap-4 items-center sm:items-center sm:justify-between mb-6">
                        <Button
                            variant="outline"
                            onClick={() => navigateToLogicalParent(breadcrumbs)}
                            className="w-full sm:w-auto"
                        >
                            <span className="block sm:hidden">
                                <Tooltip>
                                    <TooltipTrigger asChild>
                                        <span><ArrowLeft className="h-4 w-4" /></span>
                                    </TooltipTrigger>
                                    <TooltipContent>Back</TooltipContent>
                                </Tooltip>
                            </span>
                            <span className="hidden sm:flex items-center">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Back
                            </span>
                        </Button>
                        <h1 className="text-2xl font-bold text-center sm:text-left">Clearance Request Details</h1>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        {/* Main Content */}
                        <div className="lg:col-span-2 space-y-6">
                            {/* Request Information */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <UserCheck className="h-5 w-5" />
                                        Request Information
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label className="text-sm font-medium text-gray-500">Request ID</label>
                                            <p className="text-lg font-mono">#{clearanceRequest.id}</p>
                                        </div>
                                        <div>
                                            <label className="text-sm font-medium text-gray-500">Status</label>
                                            <div className="flex items-center gap-2">
                                                {getStatusIcon(clearanceRequest.status)}
                                                {getStatusBadge(clearanceRequest.status)}
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">Reason for Clearance</label>
                                        <p className="text-lg mt-1 p-3 bg-gray-50 rounded-md">{clearanceRequest.reason}</p>
                                    </div>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label className="text-sm font-medium text-gray-500">Submitted On</label>
                                            <p className="text-lg">{new Date(clearanceRequest.created_at).toLocaleString()}</p>
                                        </div>
                                        {clearanceRequest.processed_at && (
                                            <div>
                                                <label className="text-sm font-medium text-gray-500">Processed On</label>
                                                <p className="text-lg">{new Date(clearanceRequest.processed_at).toLocaleString()}</p>
                                            </div>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Driver Information */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Driver Information</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label className="text-sm font-medium text-gray-500">Driver Name</label>
                                            <p className="text-lg font-medium">{clearanceRequest.driver.first_name} {clearanceRequest.driver.last_name}</p>
                                        </div>
                                        <div>
                                            <label className="text-sm font-medium text-gray-500">Phone Number</label>
                                            <p className="text-lg">{clearanceRequest.driver.phone_number}</p>
                                        </div>
                                    </div>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label className="text-sm font-medium text-gray-500">District</label>
                                            <p className="text-lg">{clearanceRequest.driver.district}</p>
                                        </div>
                                        <div>
                                            <label className="text-sm font-medium text-gray-500">Village/Town</label>
                                            <p className="text-lg">{clearanceRequest.driver.village_town}</p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Owner Information */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Minibus Owner Information</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label className="text-sm font-medium text-gray-500">Owner Name</label>
                                            <p className="text-lg font-medium">{clearanceRequest.owner.first_name} {clearanceRequest.owner.last_name}</p>
                                        </div>
                                        <div>
                                            <label className="text-sm font-medium text-gray-500">Phone Number</label>
                                            <p className="text-lg">{clearanceRequest.owner.phone_number}</p>
                                        </div>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">Email</label>
                                        <p className="text-lg">{clearanceRequest.owner.email}</p>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Processing Form - Only show for pending requests */}
                            {clearanceRequest.status === 'pending' && (
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Process Request</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <form
                                            onSubmit={e => {
                                                e.preventDefault();
                                                patch(route('drivers.clearance-requests.process', clearanceRequest.id));
                                            }}
                                            className="space-y-4"
                                        >
                                            <input type="hidden" name="action" value={data.action} />
                                            <div>
                                                <Label htmlFor="admin_notes">Administrative Notes (Optional)</Label>
                                                <Textarea
                                                    id="admin_notes"
                                                    value={data.admin_notes}
                                                    onChange={e => setData('admin_notes', e.target.value)}
                                                    placeholder="Add any notes about your decision..."
                                                    className="mt-1"
                                                />
                                                <InputError message={errors.admin_notes} className="mt-1" />
                                            </div>
                                            <div className="flex justify-end gap-4 pt-4">
                                                <Button
                                                    type="button"
                                                    variant="outline"
                                                    onClick={() => navigateToLogicalParent(breadcrumbs)}
                                                    disabled={processing}
                                                >
                                                    Cancel
                                                </Button>
                                                <Button
                                                    type="submit"
                                                    variant="destructive"
                                                    disabled={processing}
                                                    onClick={() => setData('action', 'reject')}
                                                >
                                                    <XCircle className="h-4 w-4 mr-2" />
                                                    {processingAction === 'reject' ? 'Rejecting...' : 'Reject Request'}
                                                </Button>
                                                <Button
                                                    type="submit"
                                                    disabled={processing}
                                                    onClick={() => setData('action', 'approve')}
                                                >
                                                    <CheckCircle className="h-4 w-4 mr-2" />
                                                    {processingAction === 'approve' ? 'Approving...' : 'Approve Request'}
                                                </Button>
                                            </div>
                                        </form>
                                    </CardContent>
                                </Card>
                            )}

                            {/* Processing Information - Show for processed requests */}
                            {clearanceRequest.status !== 'pending' && (
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Processing Information</CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div>
                                            <label className="text-sm font-medium text-gray-500">Processed By</label>
                                            <p className="text-lg">
                                                {clearanceRequest.processed_by ? (
                                                    `${clearanceRequest.processed_by.first_name} ${clearanceRequest.processed_by.last_name}`
                                                ) : (
                                                    'System'
                                                )}
                                            </p>
                                        </div>
                                        {clearanceRequest.admin_notes && (
                                            <div>
                                                <label className="text-sm font-medium text-gray-500">Administrative Notes</label>
                                                <p className="text-lg mt-1 p-3 bg-gray-50 rounded-md">{clearanceRequest.admin_notes}</p>
                                            </div>
                                        )}
                                    </CardContent>
                                </Card>
                            )}
                        </div>

                        {/* Sidebar */}
                        <div className="space-y-6">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Quick Actions</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-3">
                                    <Button
                                        variant="outline"
                                        className="w-full justify-start"
                                        onClick={() => router.visit(route('drivers.show', clearanceRequest.driver.id))}
                                    >
                                        View Driver Details
                                    </Button>
                                    <Button
                                        variant="outline"
                                        className="w-full justify-start"
                                        onClick={() => router.visit(route('drivers.history', clearanceRequest.driver.id))}
                                    >
                                        View Employment History
                                    </Button>
                                    {/* Delete button for clerks (any request) or owner (pending only) */}
                                    {((userRole === 'association clerk') || (userRole === 'minibus owner' && auth?.user?.id === clearanceRequest.owner_id && clearanceRequest.status === 'pending')) && (
                                        <Button
                                            variant="destructive"
                                            className="w-full justify-start"
                                            onClick={handleDelete}
                                        >
                                            <Trash2 className="mr-2 h-4 w-4" />
                                            Delete Request
                                        </Button>
                                    )}
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader>
                                    <CardTitle>Important Notes</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-2">
                                    <div className="text-sm text-gray-600">
                                        <p>• Approving will archive the driver</p>
                                        <p>• The owner will be notified of your decision</p>
                                        <p>• Employment history will be updated</p>
                                        <p>• Driver can be unarchived later if needed</p>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </div>
            <ConfirmDialog
                open={confirmDialogOpen}
                title="Delete Clearance Request?"
                description="This action cannot be undone and will permanently remove the clearance request."
                confirmText="Delete"
                confirmVariant="destructive"
                loading={loading}
                onCancel={handleCancel}
                onConfirm={handleConfirm}
            />
        </AppLayout>
    );
} 