import{u as n,j as e,H as d,L as u,B as c}from"./app-DL-qYY5V.js";import{I as p}from"./input-error-C6jcuIY6.js";import{I as x}from"./input-Dm4SEXxy.js";import{L as f}from"./label-e3QxUH-L.js";import{A as g}from"./auth-layout-TDtItk3e.js";import{L as h}from"./loader-circle-D3J3XKS7.js";import"./index-BTzg1GwG.js";import"./app-logo-icon-BnXlkpcX.js";function E({status:t}){const{data:a,setData:o,post:i,processing:r,errors:l}=n({email:""}),m=s=>{s.preventDefault(),i(route("password.email"))};return e.jsxs(g,{title:"Forgot password",description:"Enter your email to receive a password reset link",children:[e.jsx(d,{title:"Forgot password"}),t&&e.jsx("div",{className:"mb-4 text-center text-sm font-medium text-green-600",children:t}),e.jsx("div",{className:"space-y-6",children:e.jsxs("form",{onSubmit:m,children:[e.jsxs("div",{className:"grid gap-2",children:[e.jsx(f,{htmlFor:"email",children:"Email address"}),e.jsx(x,{id:"email",type:"email",name:"email",autoComplete:"off",value:a.email,autoFocus:!0,onChange:s=>o("email",s.target.value),placeholder:"<EMAIL>"}),e.jsx(p,{message:l.email})]}),e.jsxs("div",{className:"my-4 gap-2 flex items-center justify-between",children:[e.jsx(u,{href:route("login"),className:"h-9 w-full rounded-sm px-5 py-1.5 text-sm bg-gray-400 hover:bg-gray-500 text-center text-white",children:"Return to Log in"}),e.jsxs(c,{className:"w-full bg-blue-400 hover:bg-blue-500 cursor-pointer",disabled:r,children:[r&&e.jsx(h,{className:"h-4 w-4 animate-spin"}),"Send"]})]})]})})]})}export{E as default};
