<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Membership;
use Carbon\Carbon;

class AnnualMembershipRenewal extends Command
{
    protected $signature = 'memberships:renewal';
    protected $description = 'Handle annual membership renewal for minibus owners';

    public function handle()
    {
        $today = Carbon::today();
        $minibusOwners = User::role('minibus owner')->get();

        foreach (
            $minibusOwners as $owner
        ) {
            $joiningDate = $owner->joining_date ? Carbon::parse($owner->joining_date) : null;
            if (!$joiningDate)
                continue;

            $currentYear = Carbon::today()->year;
            $startYear = $joiningDate->year;

            for ($year = $startYear; $year <= $currentYear; $year++) {
                $startDate = Carbon::create($year, $joiningDate->month, $joiningDate->day);
                $endDate = $startDate->copy()->addYear()->subDay();

                // Check if a membership exists for this year
                $exists = $owner->memberships()
                    ->whereDate('start_date', $startDate->toDateString())
                    ->whereDate('end_date', $endDate->toDateString())
                    ->exists();
                if (!$exists) {
                    $type = ($year == $startYear) ? 'Registration' : 'Affiliation';
                    $status = ($type === 'Registration') ? 'Unregistered' : ($endDate->lt(Carbon::today()) ? 'Affiliation fee not paid' : 'Need affiliation fee');
                    $owner->memberships()->create([
                        'start_date' => $startDate,
                        'end_date' => $endDate,
                        'type' => $type,
                        'status' => $status,
                    ]);
                }
            }

            // If the previous year's affiliation membership is unpaid and ended, update its status
            $affiliationMemberships = $owner->memberships()
                ->where('type', 'affiliation')
                ->where('status', '!=', 'Affiliation fee paid')
                ->where('end_date', '<', $today)
                ->get();
            foreach ($affiliationMemberships as $membership) {
                $membership->update(['status' => 'Affiliation fee not paid']);
            }
        }

        $this->info('Annual membership renewal process completed.');
    }
}