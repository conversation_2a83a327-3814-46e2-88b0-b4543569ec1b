<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\Member;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Minibus>
 */
class MinibusFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $makesAndModels = [
            ['make' => 'Toyota', 'model' => 'Hiace'],
            ['make' => 'Nissan', 'model' => 'Caravan'],
            ['make' => 'Mazda', 'model' => 'Bongo'],
            ['make' => 'Hyundai', 'model' => 'H-1'],
            ['make' => 'Ford', 'model' => 'Transit'],
        ];
        $selected = $this->faker->randomElement($makesAndModels);
        return [
            'number_plate' => 'DZ ' . $this->faker->unique()->numberBetween(1000, 9999),
            'make' => $selected['make'],
            'model' => $selected['model'],
            'year_of_make' => $this->faker->numberBetween(2000, 2023),
            'main_colour' => $this->faker->safeColorName(),
            'proof_of_ownership' => 'ownership_docs/' . $this->faker->uuid . '.pdf',
            'route_id' => null, // Set to null or assign a VehicleRoute id if needed
            'owner_id' => User::role('minibus owner')->inRandomOrder()->first()?->id,
        ];
    }
}
