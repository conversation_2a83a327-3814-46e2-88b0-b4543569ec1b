import { Head, Link, router } from '@inertiajs/react';
import { navigateToLogicalParent } from '@/utils/navigation';
import { Plus, Eye, Edit, Search, History, UserCheck, ArchiveRestore, ChevronLeft, FileText, ChevronRight } from 'lucide-react';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { route } from 'ziggy-js';
import ConfirmDialog from '@/components/ui/confirm-dialog';


export default function DriverManagement({ drivers, clearanceRequests, userRole, auth }) {
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');

    const [confirmDialog, setConfirmDialog] = useState({ open: false, action: null, driver: null });
    const [loading, setLoading] = useState(false);

    const breadcrumbs = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: userRole === 'minibus owner' ? 'My Drivers' : 'Driver Management', href: '/drivers' },
    ];

    // Handle search and filter changes
    const handleSearch = (value) => {
        setSearchTerm(value);
        // Trigger backend search with filters
        router.get('/drivers', {
            search: value,
            status: statusFilter,
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleStatusFilter = (value) => {
        setStatusFilter(value);
        // Trigger backend search with filters
        router.get('/drivers', {
            search: searchTerm,
            status: value,
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleAction = (action, driver) => {
        setConfirmDialog({ open: true, action, driver });
    };
    const handleCancel = () => setConfirmDialog({ open: false, action: null, driver: null });
    const handleConfirm = async () => {
        setLoading(true);
        if (confirmDialog.action === 'delete') {
            await router.delete(route('drivers.destroy', confirmDialog.driver.id));
        } else if (confirmDialog.action === 'unarchive') {
            await router.patch(route('drivers.unarchive', confirmDialog.driver.id));
        }
        setLoading(false);
        setConfirmDialog({ open: false, action: null, driver: null });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={userRole === 'minibus owner' ? 'My Drivers' : 'Driver Management'} />
            <div className="w-full max-w-full overflow-hidden">
                <div className="container mx-auto px-4 py-8">
                    {/* Header section */}
                    <div className="flex flex-col gap-4 mb-6">
                        <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
                            <button
                                className="flex items-center px-3 py-1 rounded hover:bg-gray-100 border border-gray-200 w-fit"
                                onClick={() => navigateToLogicalParent(breadcrumbs)}
                            >
                                <ChevronLeft className="h-4 w-4 mr-1" /> Back
                            </button>
                            <div className="flex-1 sm:flex-none">
                                <span className="text-muted-foreground text-sm sm:text-base font-medium break-words">
                                    {userRole === 'minibus owner'
                                        ? 'Manage your registered drivers and view their information.'
                                        : 'Browse all drivers and manage driver registrations.'
                                    }
                                </span>
                            </div>
                        </div>

                        {/* Action buttons row */}
                        <div className="flex flex-wrap items-center gap-2 justify-start sm:justify-end">
                            {userRole === 'association clerk' && (
                                <Link href={route('drivers.clearance-requests.index')}>
                                    <Button variant="outline" className="w-fit flex items-center gap-2">
                                        <span className="hidden sm:inline">Pending Clearance Requests</span>
                                        <span className="sm:hidden">Pending</span>
                                        {clearanceRequests?.filter(req => req.status === 'pending').length > 0 && (
                                            <Badge variant="secondary" className="ml-2 bg-red-100 text-red-800 font-bold">
                                                {clearanceRequests.filter(req => req.status === 'pending').length}
                                            </Badge>
                                        )}
                                    </Button>
                                </Link>
                            )}
                            {(userRole === 'association clerk' || userRole === 'minibus owner') && (
                                <Link href={route('drivers.create')}>
                                    <Button className="w-fit flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2">
                                        <Plus className="h-4 w-4" />
                                        Add New Driver
                                    </Button>
                                </Link>
                            )}
                        </div>
                    </div>




                    {/* Search and Filter section */}
                    <Card className="mb-6 w-full">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Search className="h-5 w-5" />
                                Search & Filter
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                                <div>
                                    <Label htmlFor="search">Search Drivers</Label>
                                    <div className="relative">
                                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                        <Input
                                            id="search"
                                            placeholder="Search by name, phone, district..."
                                            value={searchTerm}
                                            onChange={(e) => handleSearch(e.target.value)}
                                            className="pl-10"
                                        />
                                    </div>
                                </div>
                                {/* Status Filter - Only for association clerks */}
                                {userRole === 'association clerk' && (
                                    <div>
                                        <Label htmlFor="status">Filter by Status</Label>
                                        <Select value={statusFilter} onValueChange={handleStatusFilter}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="All statuses" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">All Drivers</SelectItem>
                                                <SelectItem value="active">Active Only</SelectItem>
                                                <SelectItem value="archived">Archived Only</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Drivers Table Container - Fixed width that fits screen */}
                    <div
                        className="w-full"
                        style={{
                            maxWidth: 'calc(100vw - 2rem)',
                            overflow: 'hidden'
                        }}
                    >
                        <Card className="w-full border-0 shadow-none">
                            <CardHeader className="px-0">
                                <CardTitle className="flex items-center gap-2">
                                    <UserCheck className="h-5 w-5" />
                                    {userRole === 'minibus owner' ? 'My Drivers' : 'Drivers'} ({drivers.total || drivers.data?.length || 0})
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="p-0">
                                {drivers.data && drivers.data.length > 0 ? (
                                    <div className="overflow-x-auto mb-8">
                                        <TooltipProvider>
                                            <table className="min-w-full w-full bg-white border border-gray-200 rounded-lg">
                                                <thead>
                                                    <tr className="bg-gray-100 text-gray-700">
                                                        <th className="px-4 py-3 text-left font-medium">Name</th>
                                                        <th className="px-4 py-3 text-left font-medium">Phone</th>
                                                        {userRole !== 'minibus owner' && <th className="px-4 py-3 text-left font-medium">Owner</th>}
                                                        <th className="px-4 py-3 text-left font-medium">Status</th>
                                                        <th className="px-4 py-3 text-left font-medium">Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {drivers.data.map(driver => (
                                                        <tr key={driver.id} className="border-b hover:bg-gray-50 text-sm">
                                                            <td className="font-medium px-4 py-3">
                                                                <div className="truncate max-w-[120px] sm:max-w-none">
                                                                    {driver.first_name} {driver.last_name}
                                                                </div>
                                                            </td>
                                                            <td className="px-4 py-3">
                                                                <div className="truncate max-w-[100px] sm:max-w-none">
                                                                    {driver.phone_number}
                                                                </div>
                                                            </td>
                                                            {userRole !== 'minibus owner' && (
                                                                <td className="px-4 py-3">
                                                                    <div className="truncate max-w-[100px] sm:max-w-none">
                                                                        {driver.minibus_owner ?
                                                                            `${driver.minibus_owner.user?.first_name} ${driver.minibus_owner.user?.last_name}` :
                                                                            'N/A'
                                                                        }
                                                                    </div>
                                                                </td>
                                                            )}
                                                            <td className="px-4 py-3">
                                                                {driver.archived ? (
                                                                    <Badge className="bg-red-100 text-red-800 border border-red-600 text-sm">Archived</Badge>
                                                                ) : (
                                                                    <Badge className="bg-green-100 text-green-800 border border-green-600 text-sm">Active</Badge>
                                                                )}
                                                            </td>
                                                            <td className="px-4 py-3">
                                                                <div className="flex items-center gap-1 sm:gap-2">
                                                                    {/* View Details */}
                                                                    <Tooltip>
                                                                        <TooltipTrigger asChild>
                                                                            <Link href={route('drivers.show', driver.id)}>
                                                                                <Button size="sm" variant="outline" className="h-6 w-6 sm:h-8 sm:w-8 p-0">
                                                                                    <Eye className="h-3 w-3 sm:h-4 sm:w-4" />
                                                                                </Button>
                                                                            </Link>
                                                                        </TooltipTrigger>
                                                                        <TooltipContent>View Details</TooltipContent>
                                                                    </Tooltip>

                                                                    {/* Employment History - Only for association clerks */}
                                                                    {userRole === 'association clerk' && (
                                                                        <Tooltip>
                                                                            <TooltipTrigger asChild>
                                                                                <Link href={route('drivers.history', driver.id)}>
                                                                                    <Button variant="outline" size="sm" className="h-6 w-6 sm:h-8 sm:w-8 p-0">
                                                                                        <History className="h-3 w-3 sm:h-4 sm:w-4" />
                                                                                    </Button>
                                                                                </Link>
                                                                            </TooltipTrigger>
                                                                            <TooltipContent>Employment History</TooltipContent>
                                                                        </Tooltip>
                                                                    )}

                                                                    {/* Edit Driver - Only for association clerks and not archived */}
                                                                    {userRole === 'association clerk' && !driver.archived && (
                                                                        <Tooltip>
                                                                            <TooltipTrigger asChild>
                                                                                <Link href={route('drivers.edit', driver.id)}>
                                                                                    <Button variant="outline" size="sm" className="h-6 w-6 sm:h-8 sm:w-8 p-0">
                                                                                        <Edit className="h-3 w-3 sm:h-4 sm:w-4" />
                                                                                    </Button>
                                                                                </Link>
                                                                            </TooltipTrigger>
                                                                            <TooltipContent>Edit Driver</TooltipContent>
                                                                        </Tooltip>
                                                                    )}

                                                                    {/* Request Clearance - Only for minibus owners and not archived */}
                                                                    {userRole === 'minibus owner' && !driver.archived && driver.user_id === auth.user.id && (
                                                                        <Tooltip>
                                                                            <TooltipTrigger asChild>
                                                                                <Link href={route('drivers.request-clearance', driver.id)}>
                                                                                    <Button size="sm" variant="outline" className="h-6 w-6 sm:h-8 sm:w-8 p-0">
                                                                                        <FileText className="h-3 w-3 sm:h-4 sm:w-4" />
                                                                                    </Button>
                                                                                </Link>
                                                                            </TooltipTrigger>
                                                                            <TooltipContent>Request Clearance</TooltipContent>
                                                                        </Tooltip>
                                                                    )}

                                                                    {/* Unarchive Driver - Only for association clerks and archived drivers */}
                                                                    {driver.archived && userRole === 'association clerk' && (
                                                                        <Tooltip>
                                                                            <TooltipTrigger asChild>
                                                                                <Button
                                                                                    variant="outline"
                                                                                    size="sm"
                                                                                    className="h-6 w-6 sm:h-8 sm:w-8 p-0"
                                                                                    onClick={() => handleAction('unarchive', driver)}
                                                                                >
                                                                                    <ArchiveRestore className="h-3 w-3 sm:h-4 sm:w-4" />
                                                                                </Button>
                                                                            </TooltipTrigger>
                                                                            <TooltipContent>Unarchive Driver</TooltipContent>
                                                                        </Tooltip>
                                                                    )}
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    ))}
                                                </tbody>
                                            </table>
                                        </TooltipProvider>
                                    </div>
                                ) : (
                                    <div className="text-center py-8 px-6">
                                        <UserCheck className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                                        <h3 className="text-lg font-medium mb-2">No drivers found</h3>
                                        <p className="text-muted-foreground mb-4">
                                            {searchTerm || statusFilter !== 'all' ?
                                                "Try adjusting your search or filters" :
                                                userRole === 'minibus owner'
                                                    ? "No drivers have been registered for your minibuses yet"
                                                    : "No drivers have been registered yet"
                                            }
                                        </p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>

                    {/* Pagination */}
                    {drivers && drivers.total > 0 && (
                        <div className="mt-6 w-full">
                            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 px-4 py-4 border-t bg-gray-50/50 rounded-b-lg">
                                <div className="text-sm text-gray-700 text-center sm:text-left">
                                    Showing {drivers.from} to {drivers.to} of {drivers.total} results
                                </div>
                                {drivers.last_page > 1 && (
                                    <div className="flex items-center justify-center sm:justify-end space-x-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => router.get(drivers.prev_page_url)}
                                            disabled={!drivers.prev_page_url}
                                            className="flex items-center gap-1"
                                        >
                                            <ChevronLeft className="h-4 w-4" />
                                            <span className="hidden sm:inline">Previous</span>
                                            <span className="sm:hidden">Prev</span>
                                        </Button>

                                        <span className="text-sm text-gray-600 px-2">
                                            {drivers.current_page} of {drivers.last_page}
                                        </span>

                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => router.get(drivers.next_page_url)}
                                            disabled={!drivers.next_page_url}
                                            className="flex items-center gap-1"
                                        >
                                            <span className="hidden sm:inline">Next</span>
                                            <span className="sm:hidden">Next</span>
                                            <ChevronRight className="h-4 w-4" />
                                        </Button>
                                    </div>
                                )}
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Delete Confirmation Dialog */}
            <ConfirmDialog
                open={confirmDialog.open}
                title={
                    confirmDialog.action === 'delete' ? `Delete ${confirmDialog.driver?.first_name} ${confirmDialog.driver?.last_name}?` :
                        confirmDialog.action === 'unarchive' ? `Unarchive ${confirmDialog.driver?.first_name} ${confirmDialog.driver?.last_name}?` :
                            'Are you sure?'
                }
                description={
                    confirmDialog.action === 'delete' ? 'This action cannot be undone and will permanently remove the driver from the system.' :
                        confirmDialog.action === 'unarchive' ? 'This driver will be restored and regain active status.' :
                            ''
                }
                confirmText={
                    confirmDialog.action === 'delete' ? 'Delete' :
                        confirmDialog.action === 'unarchive' ? 'Unarchive' :
                            'Confirm'
                }
                confirmVariant={confirmDialog.action === 'delete' ? 'destructive' : 'default'}
                loading={loading}
                onCancel={handleCancel}
                onConfirm={handleConfirm}
            />
        </AppLayout>
    );
}
