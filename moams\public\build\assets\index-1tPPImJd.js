import{i as w,r as u,j as b,w as M,q as P}from"./app-DL-qYY5V.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const g=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],z=w("Check",g);function $(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function L(e,t){const n=u.createContext(t),r=i=>{const{children:s,...c}=i,a=u.useMemo(()=>c,Object.values(c));return b.jsx(n.Provider,{value:a,children:s})};r.displayName=e+"Provider";function o(i){const s=u.useContext(n);if(s)return s;if(t!==void 0)return t;throw new Error(`\`${i}\` must be used within \`${e}\``)}return[r,o]}function j(e,t=[]){let n=[];function r(i,s){const c=u.createContext(s),a=n.length;n=[...n,s];const f=d=>{var A;const{scope:m,children:h,...N}=d,v=((A=m==null?void 0:m[e])==null?void 0:A[a])||c,x=u.useMemo(()=>N,Object.values(N));return b.jsx(v.Provider,{value:x,children:h})};f.displayName=i+"Provider";function l(d,m){var v;const h=((v=m==null?void 0:m[e])==null?void 0:v[a])||c,N=u.useContext(h);if(N)return N;if(s!==void 0)return s;throw new Error(`\`${d}\` must be used within \`${i}\``)}return[f,l]}const o=()=>{const i=n.map(s=>u.createContext(s));return function(c){const a=(c==null?void 0:c[e])||i;return u.useMemo(()=>({[`__scope${e}`]:{...c,[e]:a}}),[c,a])}};return o.scopeName=e,[r,C(o,...t)]}function C(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((c,{useScope:a,scopeName:f})=>{const d=a(i)[`__scope${f}`];return{...c,...d}},{});return u.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}var S=globalThis!=null&&globalThis.document?u.useLayoutEffect:()=>{},y=M[" useInsertionEffect ".trim().toString()]||S;function W({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){const[o,i,s]=O({defaultProp:t,onChange:n}),c=e!==void 0,a=c?e:o;{const l=u.useRef(e!==void 0);u.useEffect(()=>{const d=l.current;d!==c&&console.warn(`${r} is changing from ${d?"controlled":"uncontrolled"} to ${c?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),l.current=c},[c,r])}const f=u.useCallback(l=>{var d;if(c){const m=E(l)?l(e):l;m!==e&&((d=s.current)==null||d.call(s,m))}else i(l)},[c,e,i,s]);return[a,f]}function O({defaultProp:e,onChange:t}){const[n,r]=u.useState(e),o=u.useRef(n),i=u.useRef(t);return y(()=>{i.current=t},[t]),u.useEffect(()=>{var s;o.current!==n&&((s=i.current)==null||s.call(i,n),o.current=n)},[n,o]),[n,r,i]}function E(e){return typeof e=="function"}function R(e,t){return u.useReducer((n,r)=>t[n][r]??n,e)}var T=e=>{const{present:t,children:n}=e,r=I(t),o=typeof n=="function"?n({present:r.isPresent}):u.Children.only(n),i=P(r.ref,U(o));return typeof n=="function"||r.isPresent?u.cloneElement(o,{ref:i}):null};T.displayName="Presence";function I(e){const[t,n]=u.useState(),r=u.useRef(null),o=u.useRef(e),i=u.useRef("none"),s=e?"mounted":"unmounted",[c,a]=R(s,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return u.useEffect(()=>{const f=p(r.current);i.current=c==="mounted"?f:"none"},[c]),S(()=>{const f=r.current,l=o.current;if(l!==e){const m=i.current,h=p(f);e?a("MOUNT"):h==="none"||(f==null?void 0:f.display)==="none"?a("UNMOUNT"):a(l&&m!==h?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,a]),S(()=>{if(t){let f;const l=t.ownerDocument.defaultView??window,d=h=>{const v=p(r.current).includes(h.animationName);if(h.target===t&&v&&(a("ANIMATION_END"),!o.current)){const x=t.style.animationFillMode;t.style.animationFillMode="forwards",f=l.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=x)})}},m=h=>{h.target===t&&(i.current=p(r.current))};return t.addEventListener("animationstart",m),t.addEventListener("animationcancel",d),t.addEventListener("animationend",d),()=>{l.clearTimeout(f),t.removeEventListener("animationstart",m),t.removeEventListener("animationcancel",d),t.removeEventListener("animationend",d)}}else a("ANIMATION_END")},[t,a]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:u.useCallback(f=>{r.current=f?getComputedStyle(f):null,n(f)},[])}}function p(e){return(e==null?void 0:e.animationName)||"none"}function U(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function k(e){const[t,n]=u.useState(void 0);return S(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const i=o[0];let s,c;if("borderBoxSize"in i){const a=i.borderBoxSize,f=Array.isArray(a)?a[0]:a;s=f.inlineSize,c=f.blockSize}else s=e.offsetWidth,c=e.offsetHeight;n({width:s,height:c})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}export{z as C,T as P,$ as a,k as b,j as c,S as d,L as e,W as u};
