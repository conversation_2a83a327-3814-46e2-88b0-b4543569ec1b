import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { CreditCard, AlertCircle, Loader2 } from 'lucide-react';
import { route } from 'ziggy-js';
import InputError from './input-error';

export default function CtechpayPaymentModal({
    open,
    onClose,
    membership,
    user,
    amount: initialAmount,
    feeType
}) {
    const [amount, setAmount] = useState(initialAmount || '');
    const [submitting, setSubmitting] = useState(false);
    const [errors, setErrors] = useState({});

    // Get the current fee amount (passed as initialAmount)
    const currentFeeAmount = parseFloat(initialAmount) || 0;

    const handleAmountChange = (e) => {
        const value = e.target.value;

        // Allow empty value for clearing
        if (value === '') {
            setAmount('');
            return;
        }

        // Only allow whole numbers (no decimals)
        if (!/^\d+$/.test(value)) {
            return;
        }

        // Parse the value
        const numericValue = parseInt(value, 10);

        // Allow any amount up to maximum (user-friendly)
        if (numericValue <= 1000000) {
            setAmount(value);
        }
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        setSubmitting(true);
        setErrors({});

        const numericAmount = parseInt(amount, 10);

        // Validate amount before submission
        if (!numericAmount || numericAmount < currentFeeAmount || numericAmount > 1000000) {
            const minAmountFormatted = currentFeeAmount.toLocaleString('en-US', { maximumFractionDigits: 0 });
            setErrors({ amount: `Amount must be between MWK ${minAmountFormatted} and MWK 1,000,000` });
            setSubmitting(false);
            return;
        }

        const paymentData = {
            user_id: user.id,
            membership_id: membership.id,
            amount: numericAmount,
            fee_type: feeType,
            payment_method: 'ctechpay_card',
        };

        // Use fetch for JSON API call
        fetch(route('payments.ctechpay.initiate'), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                'Accept': 'application/json',
            },
            body: JSON.stringify(paymentData),
        })
            .then(async (response) => {
                const data = await response.json();

                if (response.ok && data.success) {
                    // Redirect to payment page
                    window.location.href = data.payment_url;
                } else {
                    // Handle validation errors
                    if (data.errors) {
                        setErrors(data.errors);
                    } else {
                        setErrors({ error: data.error || 'Payment initiation failed' });
                    }
                    setSubmitting(false);
                }
            })
            .catch((error) => {
                console.error('Payment initiation error:', error);
                setErrors({ error: 'Network error. Please try again.' });
                setSubmitting(false);
            });
    };

    const handleClose = () => {
        if (!submitting) {
            setErrors({});
            onClose();
        }
    };



    return (
        <Dialog open={open} onOpenChange={handleClose}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                        <CreditCard className="h-5 w-5" />
                        Online Payment
                    </DialogTitle>
                    <DialogDescription>
                        Pay your {feeType} fee securely using Ctechpay
                    </DialogDescription>
                </DialogHeader>

                <form onSubmit={handleSubmit} className="space-y-4">
                    {/* Payment Amount */}
                    <div className="space-y-2">
                        <Label htmlFor="amount">Amount (MWK)</Label>
                        <Input
                            id="amount"
                            type="number"
                            value={amount}
                            onChange={handleAmountChange}
                            placeholder={`Enter amount (min: ${currentFeeAmount.toLocaleString('en-US', { maximumFractionDigits: 0 })})`}
                            min="1"
                            max="1000000"
                            step="1"
                            required
                            disabled={submitting}
                            className="[appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                        />
                        <div className="text-xs text-gray-500">
                            Minimum amount: MWK {currentFeeAmount.toLocaleString('en-US', { maximumFractionDigits: 0 })}
                        </div>
                        {amount && parseInt(amount, 10) < currentFeeAmount && (
                            <div className="text-xs text-amber-600">
                                The amount is below minimum fee.
                            </div>
                        )}
                        <InputError message={errors.amount} />
                    </div>

                    {/* Payment Method Selection */}
                    <div className="space-y-3">
                        <Label>Payment Method</Label>
                        <div className="grid grid-cols-1 gap-3">
                            {/* Card Payment Option */}
                            <Card className="ring-2 ring-blue-500 bg-blue-50">
                                <CardContent className="p-4">
                                    <div className="flex items-center space-x-3">
                                        <div className="w-4 h-4 rounded-full border-2 border-blue-500 bg-blue-500">
                                            <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5"></div>
                                        </div>
                                        <CreditCard className="h-5 w-5 text-blue-600" />
                                        <div>
                                            <div className="font-medium">Card Payment</div>
                                            <div className="text-sm text-gray-500">
                                                Pay with Visa, Mastercard, or local cards
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                        <InputError message={errors.payment_method} />
                    </div>



                    {/* General Error Message */}
                    {errors.error && (
                        <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                            <AlertCircle className="h-4 w-4 text-red-600" />
                            <span className="text-red-700 text-sm">{errors.error}</span>
                        </div>
                    )}

                    {/* Action Buttons */}
                    <div className="flex justify-end gap-3 pt-4">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={handleClose}
                            disabled={submitting}
                        >
                            Cancel
                        </Button>
                        <Button
                            type="submit"
                            disabled={submitting || !amount || parseInt(amount, 10) < currentFeeAmount}
                            className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
                        >
                            {submitting ? (
                                <>
                                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                    Processing...
                                </>
                            ) : (
                                <>
                                    <CreditCard className="h-4 w-4 mr-2" />
                                    Pay MWK {amount ? parseInt(amount, 10).toLocaleString('en-US') : '0'}
                                </>
                            )}
                        </Button>
                    </div>
                </form>
            </DialogContent>
        </Dialog>
    );
}
