<?php

namespace App\Notifications;

use App\Models\MinibusTransferRequest;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class TransferRequestNotification extends Notification
{
    // Made synchronous to avoid serialization issues

    public $transferRequest;

    public function __construct(MinibusTransferRequest $transferRequest)
    {
        $this->transferRequest = $transferRequest;
    }

    public function via($notifiable): array
    {
        return ['database']; // Temporarily disabled email due to service limits
    }

    public function toMail($notifiable): MailMessage
    {
        $minibus = $this->transferRequest->minibus;
        $owner = $this->transferRequest->owner;

        $details = [
            [
                'title' => 'Minibus Information',
                'icon' => '🚐',
                'items' => [
                    ['label' => 'Number Plate', 'value' => $minibus->number_plate],
                    ['label' => 'Make & Model', 'value' => $minibus->make . ' ' . $minibus->model],
                    ['label' => 'Year', 'value' => $minibus->year ?? 'Not specified'],
                    ['label' => 'Route', 'value' => $minibus->route ?? 'Not specified']
                ]
            ],
            [
                'title' => 'Current Owner Information',
                'icon' => '👤',
                'items' => [
                    ['label' => 'Owner Name', 'value' => $owner->first_name . ' ' . $owner->last_name],
                    ['label' => 'Email', 'value' => $owner->email],
                    ['label' => 'Phone', 'value' => $owner->phone_number ?? 'Not provided']
                ]
            ],
            [
                'title' => 'Transfer Details',
                'icon' => '📝',
                'items' => [
                    ['label' => 'Transfer Type', 'value' => ucfirst($this->transferRequest->transfer_type)],
                    ['label' => 'Submitted', 'value' => $this->transferRequest->created_at->format('F j, Y \a\t g:i A')],
                    ['label' => 'Status', 'value' => ucfirst($this->transferRequest->status)]
                ]
            ]
        ];

        // Add new owner info for internal transfers
        if ($this->transferRequest->transfer_type === 'internal' && $this->transferRequest->new_owner_id) {
            $details[2]['items'][] = [
                'label' => 'New Owner',
                'value' => ($this->transferRequest->newOwner->first_name ?? 'TBD') . ' ' . ($this->transferRequest->newOwner->last_name ?? '')
            ];
        }

        $notificationData = [
            'badge' => ['type' => 'warning', 'text' => 'New Transfer Request'],
            'greeting' => 'Hello,',
            'message' => 'A new minibus transfer request has been submitted and requires your review. As an association clerk/manager, please process this request at your earliest convenience.',
            'details' => $details,
            'action' => [
                'url' => route('notification.redirect', ['type' => 'transfer-request', 'id' => $this->transferRequest->id]),
                'text' => 'Review Transfer Request'
            ],
            'additionalMessages' => [
                ['content' => 'Please review this transfer request and take appropriate action. The minibus owner will be notified once you process the request.']
            ]
        ];

        return (new MailMessage)
            ->subject('New Minibus Transfer Request')
            ->view('emails.general-notification', [
                'notificationData' => $notificationData,
                'subject' => 'New Minibus Transfer Request',
                'logo' => asset('assets/logos/MoamLogo.png')
            ]);
    }

    public function toArray($notifiable): array
    {
        $minibus = $this->transferRequest->minibus;
        $owner = $this->transferRequest->owner;

        return [
            'transfer_request_id' => $this->transferRequest->id,
            'minibus_id' => $minibus->id,
            'minibus_number_plate' => $minibus->number_plate,
            'owner_name' => "{$owner->first_name} {$owner->last_name}",
            'transfer_type' => $this->transferRequest->transfer_type,
            'reason' => $this->transferRequest->reason,
        ];
    }
}