<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class BusinessLogicException extends Exception
{
    protected $errorCode;
    protected $userMessage;
    protected $context;

    public function __construct(
        string $message = 'Business rule violation',
        string $userMessage = 'This action cannot be completed due to business rules.',
        string $errorCode = null,
        array $context = [],
        int $code = 0,
        \Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);

        $this->userMessage = $userMessage;
        $this->errorCode = $errorCode;
        $this->context = $context;
    }

    /**
     * Get the user-friendly message
     */
    public function getUserMessage(): string
    {
        return $this->userMessage;
    }

    /**
     * Get the error code
     */
    public function getErrorCode(): ?string
    {
        return $this->errorCode;
    }

    /**
     * Get the context data
     */
    public function getContext(): array
    {
        return $this->context;
    }

    /**
     * Render the exception into an HTTP response.
     */
    public function render(Request $request): Response|\Illuminate\Http\JsonResponse
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'Business Rule Violation',
                'message' => $this->getUserMessage(),
                'error_code' => $this->getErrorCode(),
                'context' => $this->getContext(),
            ], 422);
        }

        return response()->view('errors.business-logic', [
            'message' => $this->getUserMessage(),
            'errorCode' => $this->getErrorCode(),
            'context' => $this->getContext(),
        ], 422);
    }

    /**
     * Report the exception.
     */
    public function report(): void
    {
        \Log::warning('Business Logic Exception', [
            'message' => $this->getMessage(),
            'user_message' => $this->getUserMessage(),
            'error_code' => $this->getErrorCode(),
            'context' => $this->getContext(),
            'user_id' => auth()->id(),
            'trace' => $this->getTraceAsString(),
        ]);
    }

    /**
     * Create a duplicate record exception
     */
    public static function duplicateRecord(
        string $recordType,
        array $identifiers = []
    ): self {
        $identifierStr = empty($identifiers) ? '' : ' (' . implode(', ', $identifiers) . ')';

        return new self(
            "Duplicate {$recordType} record{$identifierStr}",
            "A {$recordType} with these details already exists. Please check your information and try again.",
            'DUPLICATE_RECORD',
            ['record_type' => $recordType, 'identifiers' => $identifiers]
        );
    }

    /**
     * Create an invalid state transition exception
     */
    public static function invalidStateTransition(
        string $entity,
        string $currentState,
        string $targetState
    ): self {
        return new self(
            "Invalid state transition for {$entity}: {$currentState} -> {$targetState}",
            "This action cannot be performed because the {$entity} is in '{$currentState}' state.",
            'INVALID_STATE_TRANSITION',
            [
                'entity' => $entity,
                'current_state' => $currentState,
                'target_state' => $targetState
            ]
        );
    }

    /**
     * Create an insufficient permissions exception
     */
    public static function insufficientPermissions(
        string $action,
        string $resource = null
    ): self {
        $resourceStr = $resource ? " on {$resource}" : '';

        return new self(
            "Insufficient permissions to {$action}{$resourceStr}",
            "You don't have permission to perform this action. Please contact an administrator if you believe this is an error.",
            'INSUFFICIENT_PERMISSIONS',
            ['action' => $action, 'resource' => $resource]
        );
    }

    /**
     * Create a resource limit exceeded exception
     */
    public static function resourceLimitExceeded(
        string $resource,
        int $limit,
        int $current
    ): self {
        return new self(
            "Resource limit exceeded for {$resource}: {$current}/{$limit}",
            "You have reached the maximum limit for {$resource} ({$limit}). Please remove some items or contact support to increase your limit.",
            'RESOURCE_LIMIT_EXCEEDED',
            ['resource' => $resource, 'limit' => $limit, 'current' => $current]
        );
    }

    /**
     * Create a dependency violation exception
     */
    public static function dependencyViolation(
        string $entity,
        string $dependency,
        array $dependentItems = []
    ): self {
        $itemsStr = empty($dependentItems) ? '' : ' (' . implode(', ', $dependentItems) . ')';

        return new self(
            "Cannot delete {$entity} due to existing {$dependency}{$itemsStr}",
            "This {$entity} cannot be deleted because it has associated {$dependency}. Please remove the dependencies first.",
            'DEPENDENCY_VIOLATION',
            [
                'entity' => $entity,
                'dependency' => $dependency,
                'dependent_items' => $dependentItems
            ]
        );
    }

    /**
     * Create a deadline exceeded exception
     */
    public static function deadlineExceeded(
        string $action,
        string $deadline
    ): self {
        return new self(
            "Deadline exceeded for {$action}: {$deadline}",
            "The deadline for this action has passed ({$deadline}). Please contact support if you need assistance.",
            'DEADLINE_EXCEEDED',
            ['action' => $action, 'deadline' => $deadline]
        );
    }

    /**
     * Create an archived entity exception
     */
    public static function archivedEntity(
        string $entity,
        string $entityId
    ): self {
        return new self(
            "Cannot perform action on archived {$entity} {$entityId}",
            "This {$entity} has been archived and cannot be modified. Please contact an administrator if you need to reactivate it.",
            'ARCHIVED_ENTITY',
            ['entity' => $entity, 'entity_id' => $entityId]
        );
    }
}
