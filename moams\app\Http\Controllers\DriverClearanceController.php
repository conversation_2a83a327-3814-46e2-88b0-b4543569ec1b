<?php

namespace App\Http\Controllers;

use App\Models\Driver;
use App\Models\User;
use App\Models\DriverClearanceRequest;
use App\Models\DriverEmploymentHistory;
use App\Notifications\DriverClearanceRequestNotification;
use App\Notifications\DriverClearanceSuccessNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification;
use Inertia\Inertia;

class DriverClearanceController extends Controller
{
    /**
     * Request driver clearance
     */
    public function requestClearance(Driver $driver)
    {
        $user = auth()->user();
        $userRole = $user->roles->first()->name ?? null;

        // Only minibus owners can request clearance for their drivers
        if ($userRole !== 'minibus owner' || $driver->owner_id !== $user->id) {
            abort(403, 'You can only request clearance for your own drivers.');
        }

        // Find pending clearance request for this driver and user
        $pendingRequest = DriverClearanceRequest::where('driver_id', $driver->id)
            ->where('owner_id', $user->id)
            ->where('status', 'pending')
            ->first();

        return Inertia::render('driverManagement/request-clearance-driver', [
            'driver' => $driver->load('minibusOwner'),
            'pendingRequest' => $pendingRequest,
            'auth' => [
                'user' => $user,
            ],
        ]);
    }

    /**
     * Store driver clearance request
     */
    public function storeClearanceRequest(Request $request, Driver $driver)
    {
        $user = auth()->user();
        $userRole = $user->roles->first()->name ?? null;

        // Only minibus owners can request clearance for their drivers
        if ($userRole !== 'minibus owner' || $driver->owner_id !== $user->id) {
            abort(403, 'You can only request clearance for your own drivers.');
        }

        // Prevent duplicate pending requests
        $existing = DriverClearanceRequest::where('driver_id', $driver->id)
            ->where('owner_id', $user->id)
            ->where('status', 'pending')
            ->first();
        if ($existing) {
            return redirect()->back()->with('error', 'You already have a pending clearance request for this driver.');
        }

        $request->validate([
            'reason' => 'required|string|min:10',
        ]);

        try {
            DB::transaction(function () use ($request, $driver, $user) {
                $clearanceRequest = DriverClearanceRequest::create([
                    'driver_id' => $driver->id,
                    'owner_id' => $user->id,
                    'status' => 'pending',
                    'reason' => $request->reason,
                ]);

                // Notify all association clerks
                $clerks = User::role('association clerk')->get();
                Notification::send($clerks, new DriverClearanceRequestNotification($clearanceRequest));
            });

            return redirect('/drivers')
                ->with('success', 'Clearance request submitted successfully. An association clerk will review your request.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to submit clearance request. Please try again.');
        }
    }

    /**
     * Show clearance requests (for association clerks)
     */
    public function indexClearanceRequests(Request $request)
    {
        $user = auth()->user();
        $userRole = $user->roles->first()->name ?? null;

        if ($userRole !== 'association clerk') {
            abort(403, 'Only association clerks can view clearance requests.');
        }

        $query = DriverClearanceRequest::with(['driver.misconducts', 'owner', 'processedBy']);

        // Apply search filter
        if ($request->has('search') && $request->get('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->whereHas('driver', function ($driverQuery) use ($search) {
                    $driverQuery->where('first_name', 'like', "%{$search}%")
                        ->orWhere('last_name', 'like', "%{$search}%")
                        ->orWhere('phone_number', 'like', "%{$search}%")
                        ->orWhereRaw("CONCAT(first_name, ' ', last_name) like ?", ["%{$search}%"]);
                })
                    ->orWhereHas('owner', function ($ownerQuery) use ($search) {
                        $ownerQuery->where('first_name', 'like', "%{$search}%")
                            ->orWhere('last_name', 'like', "%{$search}%")
                            ->orWhere('email', 'like', "%{$search}%")
                            ->orWhereRaw("CONCAT(first_name, ' ', last_name) like ?", ["%{$search}%"]);
                    })
                    ->orWhere('reason', 'like', "%{$search}%");
            });
        }

        // Apply status filter
        if ($request->has('status') && $request->get('status') !== 'all') {
            $query->where('status', $request->get('status'));
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');

        // Always sort pending requests first, then apply user sorting
        $query->orderByRaw("CASE WHEN status = 'pending' THEN 0 ELSE 1 END");

        $allowedSortFields = ['created_at', 'status', 'processed_at'];
        if (in_array($sortBy, $allowedSortFields)) {
            $query->orderBy($sortBy, $sortDirection);
        } else {
            $query->orderBy('created_at', 'desc');
        }

        // Paginate results
        $clearanceRequests = $query->paginate(10)->withQueryString();

        // Add trust score warnings to each request
        $clearanceRequests->getCollection()->transform(function ($request) {
            if ($request->driver && $request->driver->hasLowTrustScore()) {
                $request->trust_score_warning = true;
                $request->trust_score = $request->driver->trust_score;
            }
            return $request;
        });

        return Inertia::render('driverManagement/clearance-requests', [
            'clearanceRequests' => $clearanceRequests,
            'userRole' => $userRole,
            'filters' => [
                'search' => $request->get('search'),
                'status' => $request->get('status', 'all'),
                'sort_by' => $sortBy,
                'sort_direction' => $sortDirection,
            ],
            'auth' => [
                'user' => $user,
            ],
        ]);
    }

    /**
     * Show specific clearance request
     */
    public function showClearanceRequest(DriverClearanceRequest $clearanceRequest)
    {
        $user = auth()->user();
        $userRole = $user->roles->first()->name ?? null;

        if ($userRole !== 'association clerk') {
            abort(403, 'Only association clerks can view clearance requests.');
        }

        $clearanceRequest->load(['driver', 'owner', 'processedBy']);

        // Load driver's misconduct history and trust score
        $driver = $clearanceRequest->driver;
        $driver->load('misconducts');

        // Check for trust score warnings
        $trustScoreWarning = null;
        if ($driver->hasLowTrustScore()) {
            $trustScoreWarning = "Warning: Driver has a low trust score of {$driver->trust_score}/100. This driver has multiple misconduct reports. Proceed with caution.";
        }

        return Inertia::render('driverManagement/show-clearance-request', [
            'clearanceRequest' => $clearanceRequest,
            'trustScoreWarning' => $trustScoreWarning,
            'userRole' => $userRole,
            'auth' => [
                'user' => $user,
            ],
        ]);
    }

    /**
     * Process driver clearance
     */
    public function processClearance(Request $request, DriverClearanceRequest $clearanceRequest)
    {
        $user = auth()->user();
        $userRole = $user->roles->first()->name ?? null;

        if ($userRole !== 'association clerk') {
            abort(403, 'Only association clerks can process clearance requests.');
        }

        $request->validate([
            'action' => 'required|in:approve,reject',
            'admin_notes' => 'nullable|string',
        ]);

        try {
            DB::transaction(function () use ($request, $clearanceRequest, $user) {
                $clearanceRequest->update([
                    'status' => $request->action === 'approve' ? 'approved' : 'rejected',
                    'admin_notes' => $request->admin_notes,
                    'processed_by' => $user->id,
                    'processed_at' => now(),
                ]);

                if ($request->action === 'approve') {
                    // Archive the driver
                    $clearanceRequest->driver->update(['archived' => true]);

                    // Create employment history record
                    DriverEmploymentHistory::create([
                        'driver_id' => $clearanceRequest->driver_id,
                        'previous_owner_id' => $clearanceRequest->driver->owner_id,
                        'employment_change_type' => 'cleared',
                        'status' => 'completed',
                        'employment_end_date' => now(),
                        'reason' => $clearanceRequest->reason,
                    ]);

                    // Notify the owner of successful clearance
                    $owner = $clearanceRequest->owner;
                    if ($owner && !$owner->archived_at) {
                        try {
                            $owner->notify(new DriverClearanceSuccessNotification($clearanceRequest));
                            session()->flash('success', 'Notification sent to owner successfully.');
                        } catch (\Exception $e) {
                            \Log::error('Failed to send DriverClearanceSuccessNotification', [
                                'error' => $e->getMessage(),
                                'owner_id' => $owner->id,
                            ]);
                            session()->flash('error', 'Failed to send notification to owner.');
                        }
                    } else {
                        \Log::warning('Owner not found or archived for clearance notification', [
                            'owner_id' => $clearanceRequest->owner_id,
                        ]);
                        session()->flash('error', 'Owner not found or archived. Notification not sent.');
                    }
                }
            });

            $message = $request->action === 'approve'
                ? 'Driver clearance approved successfully.'
                : 'Driver clearance request rejected.';

            return redirect('/drivers/clearance-requests')
                ->with('success', $message);
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to process clearance request. Please try again.');
        }
    }

    /**
     * Show driver employment history
     */
    public function history(Driver $driver)
    {
        $user = auth()->user();
        $userRole = $user->roles->first()->name ?? null;

        // Only association clerks can view driver employment history
        if ($userRole !== 'association clerk') {
            abort(403, 'Only association clerks can view driver employment history.');
        }

        $history = DriverEmploymentHistory::where('driver_id', $driver->id)
            ->with(['previousOwner', 'newOwner'])
            ->orderBy('created_at', 'desc')
            ->get();

        return Inertia::render('driverManagement/history-driver', [
            'driver' => $driver->load('minibusOwner'),
            'history' => $history,
            'userRole' => $userRole,
            'auth' => [
                'user' => $user,
            ],
        ]);
    }

    /**
     * Directly clear a driver (for association clerks, no prior request needed)
     */
    public function directClear(Request $request, Driver $driver)
    {
        $user = auth()->user();
        $userRole = $user->roles->first()->name ?? null;

        if ($userRole !== 'association clerk') {
            abort(403, 'Only association clerks can clear drivers directly.');
        }

        $request->validate([
            'reason' => 'required|string|min:10',
            'admin_notes' => 'nullable|string',
        ]);

        try {
            DB::transaction(function () use ($request, $driver, $user) {
                // Create a clearance request and immediately approve it
                $clearanceRequest = DriverClearanceRequest::create([
                    'driver_id' => $driver->id,
                    'owner_id' => $driver->owner_id,
                    'status' => 'approved',
                    'reason' => $request->reason,
                    'admin_notes' => $request->admin_notes,
                    'processed_by' => $user->id,
                    'processed_at' => now(),
                ]);

                // Archive the driver
                $driver->update(['archived' => true]);

                // Create employment history record
                DriverEmploymentHistory::create([
                    'driver_id' => $driver->id,
                    'previous_owner_id' => $driver->owner_id,
                    'employment_change_type' => 'cleared',
                    'status' => 'completed',
                    'employment_end_date' => now(),
                    'reason' => $request->reason,
                ]);

                // Ensure relationships are loaded for notification
                $clearanceRequest->load(['driver', 'owner', 'processedBy']);
                $driver->load('minibusOwner');
                $owner = $driver->minibusOwner;
                if ($owner && !$owner->archived_at) {
                    try {
                        $owner->notify(new DriverClearanceSuccessNotification($clearanceRequest));
                        session()->flash('success', 'Notification sent to owner successfully.');
                    } catch (\Exception $e) {
                        \Log::error('Failed to send DriverClearanceSuccessNotification', [
                            'error' => $e->getMessage(),
                            'owner_id' => $owner->id,
                        ]);
                        session()->flash('error', 'Failed to send notification to owner.');
                    }
                } else {
                    \Log::warning('Owner not found or archived for clearance notification', [
                        'owner_id' => $driver->owner_id,
                    ]);
                    session()->flash('error', 'Owner not found or archived. Notification not sent.');
                }
            });

            return redirect()->route('drivers.show', $driver->id)
                ->with('success', 'Driver cleared and archived successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to clear driver. Please try again.');
        }
    }

    /**
     * Delete a driver clearance request (only by owner and if pending)
     */
    public function destroyClearanceRequest(DriverClearanceRequest $clearanceRequest)
    {
        $user = auth()->user();
        $userRole = $user->roles->first()->name ?? null;

        // Only the owner (minibus owner) can delete their own pending request
        if ($userRole !== 'minibus owner' || $clearanceRequest->owner_id !== $user->id) {
            abort(403, 'You can only delete your own pending clearance requests.');
        }
        if ($clearanceRequest->status !== 'pending') {
            return redirect()->back()->with('error', 'Only pending requests can be deleted.');
        }

        $clearanceRequest->delete();
        return redirect()->route('drivers.show', $clearanceRequest->driver_id)
            ->with('success', 'Clearance request deleted successfully.');
    }
}
