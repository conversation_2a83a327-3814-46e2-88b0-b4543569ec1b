import{u as x,j as s,H as j,B as g}from"./app-DL-qYY5V.js";import{I as t}from"./input-error-C6jcuIY6.js";import{I as i}from"./input-Dm4SEXxy.js";import{L as l}from"./label-e3QxUH-L.js";import{A as b}from"./auth-layout-TDtItk3e.js";import{L as N}from"./loader-circle-D3J3XKS7.js";import"./index-BTzg1GwG.js";import"./app-logo-icon-BnXlkpcX.js";function P({token:d,email:n}){const{data:a,setData:p,post:c,processing:m,errors:e,reset:w}=x({token:d,email:n,password:"",password_confirmation:""}),o=r=>{const{id:f,value:h}=r.target;p(f,h)},u=r=>{r.preventDefault(),c(route("password.store"),{onFinish:()=>w("password","password_confirmation")})};return s.jsxs(b,{title:"Reset password",description:"Please enter your new password below",children:[s.jsx(j,{title:"Reset password"}),s.jsx("form",{onSubmit:u,children:s.jsxs("div",{className:"grid gap-6",children:[s.jsxs("div",{className:"grid gap-2",children:[s.jsx(l,{htmlFor:"email",children:"Email"}),s.jsx(i,{id:"email",type:"email",name:"email",autoComplete:"email",value:a.email,className:"mt-1 block w-full",readOnly:!0,onChange:o}),s.jsx(t,{message:e.email,className:"mt-2"})]}),s.jsxs("div",{className:"grid gap-2",children:[s.jsx(l,{htmlFor:"password",children:"Password"}),s.jsx(i,{id:"password",type:"password",name:"password",autoComplete:"new-password",value:a.password,className:"mt-1 block w-full",autoFocus:!0,onChange:o,placeholder:"Password"}),s.jsx(t,{message:e.password})]}),s.jsxs("div",{className:"grid gap-2",children:[s.jsx(l,{htmlFor:"password_confirmation",children:"Confirm password"}),s.jsx(i,{id:"password_confirmation",type:"password",name:"password_confirmation",autoComplete:"new-password",value:a.password_confirmation,className:"mt-1 block w-full",onChange:o,placeholder:"Confirm password"}),s.jsx(t,{message:e.password_confirmation,className:"mt-2"})]}),s.jsxs(g,{type:"submit",className:"mt-4 w-full bg-blue-400 hover:bg-blue-500",disabled:m,children:[m&&s.jsx(N,{className:"h-4 w-4 animate-spin"}),"Reset password"]})]})})]})}export{P as default};
