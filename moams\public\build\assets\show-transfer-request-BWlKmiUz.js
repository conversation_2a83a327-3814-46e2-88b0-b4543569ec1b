import{j as e,r as l,u as z,B as a,C as x,a as h,b as u,c as p,f as G,e as b,T as v}from"./app-DL-qYY5V.js";import{A as J,B as c}from"./app-layout-YqstQnqE.js";import{T as K}from"./textarea-SHrtPYpi.js";import{L as U}from"./label-e3QxUH-L.js";import{n as Y}from"./navigation-DAA2N51J.js";import{C as Z}from"./confirm-dialog-B1e93Onq.js";import{D as _,a as O,b as P,c as k,d as S}from"./dialog-CXtul0Wy.js";import{A as ee}from"./arrow-left-DCW23wrL.js";import{C as se}from"./circle-x-DliR3-rL.js";import{T as re}from"./trash-2-B_fK6WNg.js";import"./index-1tPPImJd.js";import"./index-BTzg1GwG.js";import"./app-logo-icon-BnXlkpcX.js";import"./bus-CKTBKI0T.js";import"./dollar-sign-Cx0-nQIX.js";import"./users-DNGXY-sJ.js";import"./clipboard-list-CxRumT8v.js";function ve({transferRequest:N,breadcrumbs:w}){if(!N)return e.jsx("div",{children:"Transfer request not found."});const{minibus:o,owner:s,transfer_type:f,status:d,reason:L,created_at:y,id:C,ownership_transfer_certificate:r}=N,[B,j]=l.useState(!1),[F,T]=l.useState(!1),[E,D]=l.useState(!1),[M,m]=l.useState(!1),[n,$]=l.useState(""),{data:g,setData:A,patch:V,processing:t,errors:q,reset:H}=z({action:"",admin_notes:""}),I=()=>{j(!0)},W=()=>j(!1),Q=async()=>{T(!0),await b.delete(v("minibuses.transfer-requests.destroy",C),{onSuccess:()=>b.visit("/minibuses/transfer-requests")}),T(!1),j(!1)},R=i=>{$(i),A("action",i),m(!0)},X=i=>{i.preventDefault(),V(v("minibuses.transfer-requests.process",C),{onSuccess:()=>{m(!1),H()},onError:()=>{}})};return e.jsxs(J,{breadcrumbs:w,children:[e.jsxs("div",{className:"container mx-auto px-2 sm:px-4 py-6 sm:py-8",children:[e.jsx("h1",{className:"text-2xl font-bold mb-1",children:"Minibus Transfer Request Details"}),e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 sm:mb-6 flex-wrap gap-2",children:[e.jsxs(a,{variant:"outline",onClick:()=>Y(w),className:"w-full sm:w-auto",children:[e.jsx(ee,{className:"h-4 w-4 mr-2"}),"Back"]}),e.jsx("p",{className:"text-muted-foreground flex-1 min-w-0 mt-2 sm:mt-0 sm:ml-4",children:"View and manage this minibus transfer request."})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[e.jsxs("div",{className:"lg:col-span-2 space-y-6",children:[e.jsxs(x,{children:[e.jsx(h,{children:e.jsx(u,{children:"Minibus & Transfer Information"})}),e.jsx(p,{className:"space-y-4",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Minibus"}),e.jsx("p",{className:"text-lg font-mono",children:o?o.number_plate:"N/A"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Transfer Type"}),e.jsx("div",{className:"flex items-center gap-2 mt-1",children:f==="internal"?e.jsx(c,{className:"bg-green-100 text-green-800 border-green-200 border border-green-600",children:f}):e.jsx(c,{className:"bg-red-100 text-red-800 border-red-200 border border-red-600",children:f})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Status"}),e.jsx("div",{className:"flex items-center gap-2 mt-1",children:d==="pending"?e.jsx(c,{variant:"outline",className:"text-amber-600 border-amber-600",children:"Pending"}):d==="transferred"?e.jsx(c,{variant:"default",className:"bg-green-600",children:"Transferred"}):e.jsx(c,{variant:"secondary",children:d})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Request Date"}),e.jsx("p",{className:"text-lg",children:y?new Date(y).toLocaleString():"N/A"})]})]})})]}),e.jsxs(x,{children:[e.jsx(h,{children:e.jsx(u,{children:"Owner Information"})}),e.jsxs(p,{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Owner Name"}),e.jsx("p",{className:"text-lg font-medium",children:s?`${s.first_name} ${s.last_name}`:"N/A"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Phone Number"}),e.jsx("p",{className:"text-lg",children:s&&s.phone_number?s.phone_number:"N/A"})]})]}),s&&s.email&&e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Email"}),e.jsx("p",{className:"text-lg",children:s.email})]})]})]}),e.jsxs(x,{children:[e.jsx(h,{children:e.jsx(u,{children:"Reason for Transfer"})}),e.jsx(p,{children:e.jsx("div",{className:"break-all whitespace-pre-line max-w-full text-base",children:L})})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs(x,{children:[e.jsx(h,{children:e.jsx(u,{children:"Quick Actions"})}),e.jsxs(p,{className:"space-y-3",children:[d==="pending"&&e.jsxs(e.Fragment,{children:[e.jsxs(a,{className:"w-full justify-start bg-green-600 hover:bg-green-700 text-white",onClick:()=>R("approve"),disabled:t,children:[e.jsx(G,{className:"h-4 w-4 mr-2"}),t&&g.action==="approve"?"Approving...":"Approve Request"]}),e.jsxs(a,{variant:"destructive",className:"w-full justify-start",onClick:()=>R("reject"),disabled:t,children:[e.jsx(se,{className:"h-4 w-4 mr-2"}),t&&g.action==="reject"?"Rejecting...":"Reject Request"]})]}),o&&e.jsx(a,{className:"w-full justify-start bg-blue-600 hover:bg-blue-700 text-white",onClick:()=>b.visit(v("minibuses.transfer",o.id)),children:"Process Request"}),r&&e.jsx(a,{variant:"outline",className:"w-full justify-start bg-blue-100 text-blue-800 border-blue-300 hover:bg-blue-200",onClick:()=>D(!0),children:"View Transfer Certificate"}),e.jsxs(a,{variant:"destructive",className:"w-full justify-start",onClick:I,children:[e.jsx(re,{className:"h-4 w-4 mr-2"}),"Delete Request"]})]})]}),e.jsx(_,{open:E,onOpenChange:D,children:e.jsxs(O,{className:"max-w-2xl",children:[e.jsxs(P,{children:[e.jsx(k,{children:"Ownership Transfer Certificate"}),e.jsx(S,{children:r&&r.endsWith(".pdf")?"Preview of the uploaded Transfer Certificate (PDF).":"Preview of the uploaded Transfer Certificate (image)."})]}),r&&r.endsWith(".pdf")?e.jsx("iframe",{src:`/storage/${r}`,title:"Transfer Certificate PDF",className:"w-full h-[70vh] border"}):r?e.jsx("img",{src:`/storage/${r}`,alt:"Transfer Certificate",className:"w-full max-h-[70vh] object-contain"}):null]})})]})]})]}),e.jsx(Z,{open:B,title:"Delete Transfer Request?",description:"This action cannot be undone and will permanently remove the transfer request.",confirmText:"Delete",confirmVariant:"destructive",loading:F,onCancel:W,onConfirm:Q}),e.jsx(_,{open:M,onOpenChange:m,children:e.jsxs(O,{className:"max-w-md",children:[e.jsxs(P,{children:[e.jsx(k,{children:n==="approve"?"Approve Transfer Request":"Reject Transfer Request"}),e.jsx(S,{children:n==="approve"?"Are you sure you want to approve this transfer request?":"Are you sure you want to reject this transfer request?"})]}),e.jsxs("form",{onSubmit:X,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(U,{htmlFor:"admin_notes",children:"Admin Notes (Optional)"}),e.jsx(K,{id:"admin_notes",value:g.admin_notes,onChange:i=>A("admin_notes",i.target.value),placeholder:"Add any notes about this decision...",className:"mt-1"}),q.admin_notes&&e.jsx("p",{className:"text-sm text-red-600 mt-1",children:q.admin_notes})]}),e.jsxs("div",{className:"flex gap-2 justify-end",children:[e.jsx(a,{type:"button",variant:"outline",onClick:()=>m(!1),disabled:t,children:"Cancel"}),e.jsx(a,{type:"submit",variant:n==="approve"?"default":"destructive",disabled:t,className:n==="approve"?"bg-green-600 hover:bg-green-700":"",children:t?n==="approve"?"Approving...":"Rejecting...":n==="approve"?"Approve":"Reject"})]})]})]})})]})}export{ve as default};
