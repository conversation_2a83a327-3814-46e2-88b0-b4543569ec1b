<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $minibus_id
 * @property int $owner_id
 * @property int|null $new_owner_id
 * @property string $transfer_type
 * @property string $status
 * @property string|null $reason
 * @property string $ownership_transfer_certificate
 * @property int|null $processed_by
 * @property \Carbon\Carbon|null $processed_at
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property-read \App\Models\Minibus $minibus
 * @property-read \App\Models\User $owner
 * @property-read \App\Models\User|null $newOwner
 * @property-read \App\Models\User|null $processedBy
 */
class MinibusTransferRequest extends Model
{
    use HasFactory;

    protected $fillable = [
        'minibus_id',
        'owner_id',
        'transfer_type',
        'status',
        'reason',
        'ownership_transfer_certificate',
    ];

    public function minibus(): BelongsTo
    {
        return $this->belongsTo(Minibus::class);
    }

    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    public function newOwner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'new_owner_id');
    }

    public function processedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'processed_by');
    }
}