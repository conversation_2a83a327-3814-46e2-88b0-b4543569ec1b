<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Driver;
use Illuminate\Http\Request;

class UserController extends Controller
{
    // GET /api/minibus-owners
    public function searchMinibusOwners(Request $request)
    {
        $search = $request->query('search', '');
        $owners = User::role('minibus owner')
            ->where(function ($q) use ($search) {
                $q->where('first_name', 'like', "%$search%")
                    ->orWhere('last_name', 'like', "%$search%")
                    ->orWhereRaw("CONCAT(first_name, ' ', last_name) like ?", ["%$search%"]);
            })
            ->orderBy('first_name')
            ->limit(20)
            ->get()
            ->map(function ($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->first_name . ' ' . $user->last_name,
                    'email' => $user->email,
                ];
            });
        return response()->json($owners);
    }

    // GET /api/drivers
    public function searchDrivers(Request $request)
    {
        $search = $request->query('search', '');
        $user = auth()->user();
        $userRole = $user->roles->first()->name ?? null;

        $query = Driver::where('archived', false);

        // Filter based on user role
        if ($userRole === 'minibus owner') {
            $query->where('owner_id', $user->id);
        }

        $drivers = $query
            ->where(function ($q) use ($search) {
                $q->where('first_name', 'like', "%$search%")
                    ->orWhere('last_name', 'like', "%$search%")
                    ->orWhere('license_number', 'like', "%$search%")
                    ->orWhereRaw("CONCAT(first_name, ' ', last_name) like ?", ["%$search%"]);
            })
            ->orderBy('first_name')
            ->limit(20)
            ->get()
            ->map(function ($driver) {
                return [
                    'id' => $driver->id,
                    'name' => $driver->first_name . ' ' . $driver->last_name,
                    'phone_number' => $driver->phone_number,
                    'license_number' => $driver->license_number,
                ];
            });
        return response()->json($drivers);
    }
}