<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Payment>
 */
class PaymentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'amount' => $this->faker->randomFloat(2, 100, 5000),
            'fee_type' => $this->faker->randomElement(['registration', 'affiliation']),
            'status' => $this->faker->randomElement(['pending', 'fulfilled']),
            'payment_method' => $this->faker->randomElement(['cash', 'mobile_money', 'bank_transfer']),
            'paid_at' => $this->faker->optional()->dateTimeBetween('-1 year', 'now'),
            'verification_method' => $this->faker->optional()->randomElement(['clerk_manual', 'bank_transfer']),
            'verification_notes' => $this->faker->optional()->sentence(),
        ];
    }
}
