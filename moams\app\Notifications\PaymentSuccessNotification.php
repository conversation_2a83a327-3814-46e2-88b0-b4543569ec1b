<?php

namespace App\Notifications;

use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\Payment;

class PaymentSuccessNotification extends Notification
{
    // Using sync processing and database notifications to avoid email service limits

    protected $payment;
    protected $isForClerk;

    /**
     * Create a new notification instance.
     */
    public function __construct(Payment $payment, bool $isForClerk = false)
    {
        $this->payment = $payment;
        $this->isForClerk = $isForClerk;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database']; // Using database notifications only to avoid email service limits
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        if ($this->isForClerk) {
            return $this->toClerkMail($notifiable);
        }

        return $this->toMemberMail($notifiable);
    }

    /**
     * Get the mail representation for the member.
     */
    private function toMemberMail(object $notifiable): MailMessage
    {
        $membership = $this->payment->membership;
        $feeType = ucfirst($this->payment->fee_type);

        return (new MailMessage)
            ->subject('Payment Confirmation - ' . $feeType . ' Fee')
            ->greeting('Hello ' . $notifiable->first_name . ',')
            ->line('We have successfully received your ' . strtolower($feeType) . ' fee payment.')
            ->line('**Payment Details:**')
            ->line('Amount: MWK ' . number_format($this->payment->amount, 2))
            ->line('Fee Type: ' . $feeType)
            ->line('Payment Method: ' . ucfirst(str_replace('_', ' ', $this->payment->payment_method)))
            ->line('Payment Date: ' . $this->payment->paid_at->format('F j, Y \a\t g:i A'))
            ->line('Membership Period: ' . $membership->start_date->format('Y') . ' - ' . $membership->end_date->format('Y'))
            ->line('Your membership status has been updated accordingly.')
            ->line('Thank you for your payment!')
            ->salutation('Best regards, MOAM Team');
    }

    /**
     * Get the mail representation for the clerk.
     */
    private function toClerkMail(object $notifiable): MailMessage
    {
        $member = $this->payment->user;
        $membership = $this->payment->membership;
        $feeType = ucfirst($this->payment->fee_type);

        return (new MailMessage)
            ->subject('Payment Processed - ' . $member->first_name . ' ' . $member->last_name)
            ->greeting('Hello ' . $notifiable->first_name . ',')
            ->line('A ' . strtolower($feeType) . ' fee payment has been processed.')
            ->line('**Member Details:**')
            ->line('Name: ' . $member->first_name . ' ' . $member->last_name)
            ->line('Email: ' . $member->email)
            ->line('**Payment Details:**')
            ->line('Amount: MWK ' . number_format($this->payment->amount, 2))
            ->line('Fee Type: ' . $feeType)
            ->line('Payment Method: ' . ucfirst(str_replace('_', ' ', $this->payment->payment_method)))
            ->line('Payment Date: ' . $this->payment->paid_at->format('F j, Y \a\t g:i A'))
            ->line('Verification: ' . ($this->payment->verification_method ?? 'Manual'))
            ->line('The member has been notified via email.')
            ->salutation('Best regards, MOAM System');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'payment_id' => $this->payment->id,
            'amount' => $this->payment->amount,
            'fee_type' => $this->payment->fee_type,
            'member_name' => $this->payment->user->first_name . ' ' . $this->payment->user->last_name,
        ];
    }
}
