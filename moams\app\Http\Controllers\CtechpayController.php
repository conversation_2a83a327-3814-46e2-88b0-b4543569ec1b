<?php

namespace App\Http\Controllers;

use App\Models\Payment;
use App\Models\Membership;
use App\Models\User;
use App\Services\CtechpayService;
use App\Http\Requests\CtechpayPaymentRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Inertia\Inertia;

class CtechpayController extends Controller
{
    private $ctechpayService;

    public function __construct(CtechpayService $ctechpayService)
    {
        $this->ctechpayService = $ctechpayService;
    }

    /**
     * Initiate a ctechpay payment
     */
    public function initiatePayment(CtechpayPaymentRequest $request)
    {
        try {
            DB::beginTransaction();

            // Create payment record
            $payment = Payment::create([
                'user_id' => $request->user_id,
                'membership_id' => $request->membership_id,
                'amount' => $request->amount,
                'fee_type' => $request->fee_type,
                'status' => 'pending',
                'payment_method' => $request->payment_method,
                'verification_method' => 'ctechpay_api',
            ]);

            // Prepare payment data
            $paymentData = [
                'amount' => $request->amount,
                'redirect_url' => route('ctechpay.return'),
                'cancel_url' => route('ctechpay.cancel'),
            ];

            // Log the payment initiation for debugging
            Log::info('Initiating real Ctechpay payment', [
                'payment_id' => $payment->id,
                'payment_method' => $request->payment_method,
                'amount' => $request->amount,
                'environment' => $this->ctechpayService->getEnvironment(),
            ]);

            // Create payment order with ctechpay (card payment only)
            $result = $this->ctechpayService->createCardPayment($paymentData);

            if ($result['success']) {
                $payment->update([
                    'ctechpay_order_reference' => $result['order_reference'],
                    'ctechpay_payment_url' => $result['payment_page_url'],
                    'ctechpay_response_data' => $result['response_data'],
                    'ctechpay_status' => 'PENDING',
                ]);

                DB::commit();
                return response()->json([
                    'success' => true,
                    'payment_url' => $result['payment_page_url'],
                    'order_reference' => $result['order_reference'],
                ]);
            } else {
                $payment->update([
                    'ctechpay_error_message' => $result['error'],
                    'ctechpay_status' => 'FAILED',
                ]);

                DB::commit();
                return response()->json([
                    'success' => false,
                    'error' => $result['error'],
                ], 400);
            }
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Ctechpay payment initiation failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Payment initiation failed. Please try again.',
            ], 500);
        }
    }

    /**
     * Handle payment return from ctechpay using order reference
     */
    public function handleReturnByRef(Request $request)
    {
        try {
            Log::info('🔧 HONEST API CHECK: handleReturnByRef called', [
                'url' => $request->fullUrl(),
                'query_params' => $request->query(),
                'timestamp' => now(),
                'controller_version' => 'honest_api_check_v1',
            ]);

            $orderRef = $request->query('ref');

            if (!$orderRef) {
                Log::error('Missing order reference in return URL', [
                    'url' => $request->fullUrl(),
                    'query_params' => $request->query(),
                ]);

                return redirect()->route('dashboard')
                    ->with('error', 'Invalid payment return - missing order reference. Please contact support if you made a payment.');
            }

            $payment = Payment::where('ctechpay_order_reference', $orderRef)->first();

            if (!$payment) {
                Log::error('Payment not found for order reference', [
                    'order_reference' => $orderRef,
                    'url' => $request->fullUrl(),
                ]);

                return redirect()->route('dashboard')
                    ->with('error', 'Payment not found. Please contact support with your order reference: ' . $orderRef);
            }

            Log::info('🔧 HONEST API CHECK: Payment found, attempting API status check', [
                'payment_id' => $payment->id,
                'order_ref' => $orderRef,
                'current_status' => $payment->status,
            ]);

            // First, try to get the payment status from Ctechpay API
            $statusResult = $this->ctechpayService->checkPaymentStatus($orderRef);

            if ($statusResult['success'] && isset($statusResult['response_data']) && is_array($statusResult['response_data'])) {
                Log::info('🔧 HONEST API CHECK: API call successful, processing response', [
                    'payment_id' => $payment->id,
                    'api_status' => $statusResult['response_data']['status'] ?? 'unknown',
                    'response_data' => $statusResult['response_data'],
                ]);

                // Update payment from successful API response
                $this->updatePaymentFromStatusCheck($payment, $statusResult['response_data']);

                // Redirect based on the updated payment status
                $freshPayment = $payment->fresh();

                if ($freshPayment->status === 'fulfilled') {
                    Log::info('🔧 HONEST API CHECK: Payment fulfilled, redirecting to success', ['payment_id' => $payment->id]);
                    return redirect()->route('payments.success', ['payment' => $payment->id])
                        ->with('success', 'Payment completed successfully!');
                } elseif ($freshPayment->status === 'failed') {
                    Log::info('🔧 HONEST API CHECK: Payment failed, redirecting to failed page', ['payment_id' => $payment->id]);
                    return redirect()->route('payments.failed', ['payment' => $payment->id])
                        ->with('error', 'Payment was not completed. Please try again.');
                } else {
                    Log::info('🔧 HONEST API CHECK: Payment status unclear, using verification', [
                        'payment_id' => $payment->id,
                        'status' => $freshPayment->status,
                    ]);
                    return redirect()->route('payments.verification', ['payment' => $payment->id])
                        ->with('info', 'Payment verification needed. Please contact MOAM staff with your order reference.');
                }
            } else {
                // API call failed - use fallback verification
                Log::warning('🔧 HONEST API CHECK: API call failed, using fallback verification', [
                    'payment_id' => $payment->id,
                    'status_result' => $statusResult,
                    'api_error' => $statusResult['error'] ?? 'Unknown API error',
                ]);

                $this->handleFallbackVerification($payment, $request);

                // Redirect based on the updated payment status after fallback
                $freshPayment = $payment->fresh();

                if ($freshPayment->status === 'fulfilled') {
                    return redirect()->route('payments.success', ['payment' => $payment->id])
                        ->with('success', 'Payment completed successfully!');
                } elseif ($freshPayment->status === 'failed') {
                    return redirect()->route('payments.failed', ['payment' => $payment->id])
                        ->with('error', 'Payment was not completed. Please try again.');
                } else {
                    return redirect()->route('payments.verification', ['payment' => $payment->id])
                        ->with('info', 'Payment verification needed. Please contact MOAM staff with your order reference.');
                }
            }

        } catch (\Exception $e) {
            Log::error('Ctechpay return handling failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'url' => $request->fullUrl(),
                'order_ref' => $request->query('ref'),
            ]);

            // Try to find payment by order reference for proper error handling
            $orderRef = $request->query('ref');
            if ($orderRef) {
                $payment = Payment::where('ctechpay_order_reference', $orderRef)->first();
                if ($payment) {
                    return redirect()->route('payments.verification', ['payment' => $payment->id])
                        ->with('error', 'Payment processing error. Please contact support with your order reference.');
                }
            }

            // Fallback to dashboard only if we can't find the payment
            return redirect()->route('dashboard')
                ->with('error', 'Payment processing error. Please contact support.');
        }
    }

    /**
     * Handle payment return from ctechpay
     */
    public function handleReturn(Request $request, Payment $payment)
    {
        try {
            Log::info('FIXED VERSION: handleReturn called', [
                'payment_id' => $payment->id,
                'request_data' => $request->all(),
                'timestamp' => now(),
            ]);

            Log::info('Ctechpay return handler called', [
                'url' => $request->fullUrl(),
                'query_params' => $request->query(),
                'all_params' => $request->all(),
                'payment_id' => $payment->id,
                'order_ref' => $payment->ctechpay_order_reference,
            ]);

            // Check payment status with ctechpay API
            if ($payment->ctechpay_order_reference) {
                $statusResult = $this->ctechpayService->checkPaymentStatus($payment->ctechpay_order_reference);

                if ($statusResult['success'] && isset($statusResult['response_data']) && is_array($statusResult['response_data'])) {
                    Log::info('🔧 API STATUS: Valid response received', [
                        'payment_id' => $payment->id,
                        'api_status' => $statusResult['response_data']['status'] ?? 'unknown',
                    ]);
                    $this->updatePaymentFromStatusCheck($payment, $statusResult['response_data']);
                } else {
                    // API check failed or returned invalid data, use fallback verification
                    Log::warning('🔧 API STATUS: Invalid response - using fallback', [
                        'payment_id' => $payment->id,
                        'status_result' => $statusResult,
                        'response_data_type' => gettype($statusResult['response_data'] ?? null),
                        'will_use_fallback' => true,
                    ]);
                    $this->handleFallbackVerification($payment, $request);
                }
            } else {
                // No order reference, use fallback verification
                $this->handleFallbackVerification($payment, $request);
            }

            // Redirect based on payment status
            $freshPayment = $payment->fresh();

            Log::info('🔧 VERIFICATION FIX: Determining redirect', [
                'payment_id' => $payment->id,
                'payment_status' => $freshPayment->status,
                'controller_version' => 'verification_fix_v1',
            ]);

            if ($freshPayment->status === 'fulfilled') {
                Log::info('🔧 VERIFICATION FIX: Redirecting to success', ['payment_id' => $payment->id]);
                return redirect()->route('payments.success', ['payment' => $payment->id])
                    ->with('success', 'Payment completed successfully!');
            } elseif ($freshPayment->status === 'verification_needed') {
                Log::info('🔧 VERIFICATION FIX: Redirecting to verification', ['payment_id' => $payment->id]);
                return redirect()->route('payments.verification', ['payment' => $payment->id])
                    ->with('info', 'Payment verification needed. Please contact MOAM staff with your order reference.');
            } else {
                Log::info('🔧 VERIFICATION FIX: Redirecting to failed', [
                    'payment_id' => $payment->id,
                    'status' => $freshPayment->status,
                ]);
                return redirect()->route('payments.failed', ['payment' => $payment->id])
                    ->with('error', 'Payment was not completed. Please try again.');
            }
        } catch (\Exception $e) {
            Log::error('Ctechpay payment return handling failed', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return redirect()->route('payments.failed', ['payment' => $payment->id])
                ->with('error', 'An error occurred while processing your payment.');
        }
    }

    /**
     * Handle payment cancellation using order reference
     */
    public function handleCancelByRef(Request $request)
    {
        $orderRef = $request->query('ref');

        if (!$orderRef) {
            Log::error('Ctechpay cancel missing order reference', [
                'url' => $request->fullUrl(),
                'query_params' => $request->query(),
            ]);

            return redirect()->route('dashboard')
                ->with('error', 'Invalid payment cancellation - missing order reference.');
        }

        $payment = Payment::where('ctechpay_order_reference', $orderRef)->first();

        if (!$payment) {
            Log::error('Ctechpay cancel payment not found', [
                'order_reference' => $orderRef,
                'url' => $request->fullUrl(),
            ]);

            return redirect()->route('dashboard')
                ->with('error', 'Payment not found.');
        }

        return $this->handleCancel($request, $payment);
    }

    /**
     * Handle payment cancellation
     */
    public function handleCancel(Request $request, Payment $payment)
    {
        Log::info('Ctechpay payment cancelled', [
            'payment_id' => $payment->id,
            'request_data' => $request->all(),
        ]);

        $payment->update([
            'ctechpay_status' => 'CANCELLED',
            'verification_notes' => 'Payment cancelled by user',
        ]);

        return redirect()->route('payments.cancelled', ['payment' => $payment->id])
            ->with('info', 'Payment was cancelled.');
    }

    /**
     * Handle webhook notifications from ctechpay
     */
    public function handleWebhook(Request $request)
    {
        try {
            Log::info('Ctechpay webhook received', [
                'headers' => $request->headers->all(),
                'payload' => $request->all(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            // Validate webhook signature if available
            if (!$this->ctechpayService->validateWebhookSignature($request->all(), $request->header('X-Signature'))) {
                Log::warning('Ctechpay webhook signature validation failed');
                return response()->json(['status' => 'error', 'message' => 'Invalid signature'], 401);
            }

            $orderReference = $request->input('order_reference');
            if (!$orderReference) {
                Log::warning('Ctechpay webhook missing order_reference');
                return response()->json(['status' => 'error', 'message' => 'Missing order_reference'], 400);
            }

            $payment = Payment::where('ctechpay_order_reference', $orderReference)->first();
            if (!$payment) {
                Log::warning('Ctechpay webhook payment not found', ['order_reference' => $orderReference]);
                return response()->json(['status' => 'error', 'message' => 'Payment not found'], 404);
            }

            // Prevent duplicate processing
            if ($payment->status === 'fulfilled' && $request->input('status') === 'PURCHASED') {
                Log::info('Ctechpay webhook for already fulfilled payment', ['payment_id' => $payment->id]);
                return response()->json(['status' => 'success', 'message' => 'Already processed']);
            }

            // Update payment status from webhook
            $this->updatePaymentFromWebhook($payment, $request->all());

            Log::info('Ctechpay webhook processed successfully', [
                'payment_id' => $payment->id,
                'status' => $request->input('status'),
            ]);

            return response()->json(['status' => 'success']);
        } catch (\Exception $e) {
            Log::error('Ctechpay webhook handling failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all(),
            ]);

            return response()->json(['status' => 'error', 'message' => 'Webhook processing failed'], 500);
        }
    }



    /**
     * Update payment from status check API response
     */
    private function updatePaymentFromStatusCheck(Payment $payment, array $responseData)
    {
        $status = $responseData['status'] ?? 'UNKNOWN';

        $payment->update([
            'ctechpay_status' => $status,
            'ctechpay_response_data' => $responseData,
            'ctechpay_currency_code' => $responseData['currency_code'] ?? null,
            'ctechpay_formatted_amount' => $responseData['formatted_amount'] ?? null,
            'ctechpay_card_holder_name' => $responseData['card_holder_name'] ?? null,
        ]);

        if ($status === 'PURCHASED') {
            $this->markPaymentAsFulfilled($payment, 'ctechpay_api', 'Payment verified via Ctechpay API');
        } elseif (in_array($status, ['FAILED', 'CANCELLED'])) {
            $payment->update([
                'ctechpay_error_message' => $responseData['error'] ?? 'Payment failed',
            ]);
        } elseif (in_array($status, ['UNKNOWN', 'PENDING', 'PROCESSING'])) {
            // For unclear statuses, mark as verification needed
            Log::info('API returned unclear status - marking as verification needed', [
                'payment_id' => $payment->id,
                'api_status' => $status,
                'response_data' => $responseData,
            ]);

            $payment->update([
                'status' => 'verification_needed',
                'verification_notes' => "Payment status unclear from API (status: {$status}). Please contact MOAM staff with your order reference for verification.",
                'verification_method' => 'api_unclear',
            ]);
        }
    }

    /**
     * Update payment from webhook data
     */
    private function updatePaymentFromWebhook(Payment $payment, array $webhookData)
    {
        $status = $webhookData['status'] ?? 'UNKNOWN';

        $payment->update([
            'ctechpay_status' => $status,
            'ctechpay_response_data' => $webhookData,
            'ctechpay_currency_code' => $webhookData['currency_code'] ?? null,
            'ctechpay_formatted_amount' => $webhookData['amount'] ?? null,
        ]);

        if ($status === 'PURCHASED') {
            $this->markPaymentAsFulfilled($payment, 'ctechpay_webhook', 'Payment verified via Ctechpay webhook');
        }
    }

    /**
     * Handle fallback verification when API check fails
     */
    private function handleFallbackVerification(Payment $payment, Request $request)
    {
        Log::info('🔧 HONEST FALLBACK: Starting verification', [
            'payment_id' => $payment->id,
            'order_ref' => $payment->ctechpay_order_reference,
            'query_params' => $request->query(),
            'all_params' => $request->all(),
        ]);

        // Only mark as failed if there are EXPLICIT failure indicators
        $explicitFailure = $request->query('status') === 'FAILED' ||
            $request->query('status') === 'CANCELLED' ||
            $request->query('error') !== null ||
            $request->query('cancelled') === 'true';

        // Only mark as success if there are EXPLICIT success indicators
        $explicitSuccess = $request->query('status') === 'PURCHASED' ||
            $request->query('status') === 'SUCCESS' ||
            $request->query('success') === 'true';

        if ($explicitFailure) {
            Log::info('🔧 HONEST FALLBACK: Explicit failure detected', [
                'payment_id' => $payment->id,
                'failure_indicators' => $request->only(['error', 'cancelled', 'failed', 'status']),
            ]);

            $payment->update([
                'status' => 'failed',
                'ctechpay_status' => 'FAILED',
                'ctechpay_error_message' => $request->query('error', 'Payment failed'),
                'verification_notes' => 'Payment explicitly failed based on return parameters',
                'verification_method' => 'url_params_failed',
            ]);
        } elseif ($explicitSuccess) {
            Log::info('🔧 HONEST FALLBACK: Explicit success detected', [
                'payment_id' => $payment->id,
                'success_indicators' => $request->only(['status', 'success']),
            ]);

            $this->markPaymentAsFulfilled($payment, 'url_params_success', 'Payment explicitly successful based on return parameters');
        } else {
            // NO CLEAR INDICATORS - Always mark as verification needed
            Log::info('🔧 HONEST FALLBACK: No clear indicators - marking as verification needed', [
                'payment_id' => $payment->id,
                'url_params' => $request->query(),
                'all_params' => $request->all(),
                'decision' => 'verification_needed',
                'reasoning' => 'No explicit success or failure indicators - being honest about uncertainty',
            ]);

            // ALWAYS mark as verification needed when status is unclear
            $payment->update([
                'status' => 'verification_needed',
                'ctechpay_status' => 'VERIFICATION_NEEDED',
                'verification_notes' => 'Payment status unclear - no explicit success or failure indicators from Ctechpay. Please contact MOAM staff with your order reference for manual verification.',
                'verification_method' => 'honest_fallback',
            ]);
        }

        Log::info('🔧 HONEST FALLBACK: Verification complete', [
            'payment_id' => $payment->id,
            'final_status' => $payment->fresh()->status,
            'ctechpay_status' => $payment->fresh()->ctechpay_status,
        ]);
    }

    /**
     * Mark payment as fulfilled and update membership
     */
    private function markPaymentAsFulfilled(Payment $payment, string $verificationMethod, string $notes)
    {
        $payment->update([
            'status' => 'fulfilled',
            'paid_at' => now(),
            'verification_method' => $verificationMethod,
            'verification_notes' => $notes,
        ]);

        // Update membership status
        $this->updateMembershipStatus($payment);

        // Send notifications
        $this->sendPaymentNotifications($payment);
    }

    /**
     * Update membership status based on payment
     */
    private function updateMembershipStatus($payment)
    {
        $membership = $payment->membership;
        if (!$membership)
            return;

        if ($payment->fee_type === 'registration') {
            $membership->update(['status' => 'Registered']);
        } elseif ($payment->fee_type === 'affiliation') {
            $membership->update(['status' => 'Affiliation fee paid']);
        }
    }

    /**
     * Send payment notifications
     */
    private function sendPaymentNotifications($payment)
    {
        try {
            // Send notification to the member
            $payment->user->notify(new \App\Notifications\PaymentSuccessNotification($payment, false));

            // Send notification to association clerks
            $clerks = User::role('association clerk')->get();
            foreach ($clerks as $clerk) {
                $clerk->notify(new \App\Notifications\PaymentSuccessNotification($payment, true));
            }
        } catch (\Exception $e) {
            Log::error('Failed to send payment notifications', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}
