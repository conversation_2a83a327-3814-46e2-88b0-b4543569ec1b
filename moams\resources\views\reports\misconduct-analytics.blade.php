<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Misconduct Analytics Report - {{ $month }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
            line-height: 1.4;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #2563eb;
            padding-bottom: 20px;
        }

        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 15px auto;
            display: block;
        }

        .header h1 {
            color: #2563eb;
            margin: 0;
            font-size: 24px;
        }

        .header h2 {
            color: #64748b;
            margin: 5px 0 0 0;
            font-size: 16px;
            font-weight: normal;
        }

        .report-info {
            background-color: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 25px;
            border-left: 4px solid #2563eb;
        }

        .report-info h3 {
            margin: 0 0 10px 0;
            color: #2563eb;
            font-size: 16px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
        }

        .info-label {
            font-weight: bold;
            color: #475569;
        }

        .misconduct-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            background-color: white;
            border: 1px solid #e2e8f0;
        }

        .misconduct-table th {
            background-color: #2563eb;
            color: white;
            padding: 12px 8px;
            text-align: left;
            font-weight: bold;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .misconduct-table td {
            padding: 10px 8px;
            border-bottom: 1px solid #e2e8f0;
            font-size: 14px;
        }

        .misconduct-table tr:nth-child(even) {
            background-color: #f8fafc;
        }

        .misconduct-table tr:hover {
            background-color: #f1f5f9;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .severity-low {
            background-color: #dcfce7;
            color: #166534;
        }

        .severity-medium {
            background-color: #fed7aa;
            color: #9a3412;
        }

        .severity-high {
            background-color: #fecaca;
            color: #991b1b;
        }

        .status-resolved {
            background-color: #dcfce7;
            color: #166534;
        }

        .status-unresolved {
            background-color: #fed7aa;
            color: #9a3412;
        }

        .trust-high {
            color: #16a34a;
            font-weight: bold;
        }

        .trust-medium {
            color: #ea580c;
            font-weight: bold;
        }

        .trust-low {
            color: #dc2626;
            font-weight: bold;
        }

        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
            text-align: center;
            color: #64748b;
            font-size: 12px;
        }

        .page-break {
            page-break-before: always;
        }

        @media print {
            body {
                margin: 0;
                padding: 15px;
            }

            .header {
                margin-bottom: 20px;
                padding-bottom: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <!-- Professional Text Logo - No image dependencies -->
        <div style="width: 120px; height: 60px; margin: 0 auto 15px auto; background-color: #2563eb; border-radius: 12px; position: relative; color: white; font-weight: bold;">
            <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 18px; letter-spacing: 1px; text-align: center; white-space: nowrap;">
                MOAMS
            </div>
        </div>
        <h1>Minibus Owners Association Management System</h1>
        <h2>Misconduct Analytics Report - {{ $month }}</h2>
    </div>

    <div class="report-info">
        <h3>Report Information</h3>
        <div class="info-grid">
            <div class="info-item">
                <span class="info-label">Generated On:</span>
                <span>{{ $generatedAt }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Generated By:</span>
                <span>{{ $generatedBy }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Report Period:</span>
                <span>{{ $month }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Report Type:</span>
                <span>Misconduct Analytics</span>
            </div>
        </div>
    </div>

    <!-- Key Statistics -->
    <div style="text-align: center; margin-bottom: 25px;">
        <table style="width: 100%; margin: 0 auto; border-collapse: separate; border-spacing: 10px;">
            <tr>
                <td style="width: 25%; background-color: #f8fafc; padding: 20px 15px; text-align: center; border: 2px solid #e2e8f0; border-radius: 8px;">
                    <div style="font-size: 24px; font-weight: bold; color: #2563eb; margin-bottom: 5px;">{{ $monthlyStats['total_misconducts'] }}</div>
                    <div style="font-size: 10px; color: #64748b; text-transform: uppercase; letter-spacing: 0.5px;">Total Misconducts</div>
                </td>
                <td style="width: 25%; background-color: #f8fafc; padding: 20px 15px; text-align: center; border: 2px solid #e2e8f0; border-radius: 8px;">
                    <div style="font-size: 24px; font-weight: bold; color: #2563eb; margin-bottom: 5px;">{{ $monthlyStats['unique_drivers'] }}</div>
                    <div style="font-size: 10px; color: #64748b; text-transform: uppercase; letter-spacing: 0.5px;">Affected Drivers</div>
                </td>
                <td style="width: 25%; background-color: #f8fafc; padding: 20px 15px; text-align: center; border: 2px solid #e2e8f0; border-radius: 8px;">
                    <div style="font-size: 24px; font-weight: bold; color: #2563eb; margin-bottom: 5px;">{{ $monthlyStats['resolution_rate'] }}%</div>
                    <div style="font-size: 10px; color: #64748b; text-transform: uppercase; letter-spacing: 0.5px;">Resolution Rate</div>
                </td>
                <td style="width: 25%; background-color: #f8fafc; padding: 20px 15px; text-align: center; border: 2px solid #e2e8f0; border-radius: 8px;">
                    <div style="font-size: 24px; font-weight: bold; color: #2563eb; margin-bottom: 5px;">{{ $monthlyStats['average_trust_score'] }}</div>
                    <div style="font-size: 10px; color: #64748b; text-transform: uppercase; letter-spacing: 0.5px;">Avg Trust Score</div>
                </td>
            </tr>
        </table>
    </div>

    <!-- Severity and Resolution Breakdown -->
    <div style="display: table; width: 100%; margin-bottom: 25px;">
        <div style="display: table-cell; width: 48%; vertical-align: top; padding-right: 2%;">
            <h3 style="color: #2563eb; margin-bottom: 15px; font-size: 16px; border-bottom: 2px solid #2563eb; padding-bottom: 5px;">Severity Breakdown</h3>
            <table class="misconduct-table">
                <thead>
                    <tr>
                        <th>Severity Level</th>
                        <th style="text-align: center;">Count</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($severityBreakdown as $item)
                        <tr>
                            <td>
                                <span class="status-badge severity-{{ strtolower($item['severity']) }}">{{ $item['severity'] }}</span>
                            </td>
                            <td style="text-align: center; font-weight: bold;">{{ $item['count'] }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        <div style="display: table-cell; width: 48%; vertical-align: top; padding-left: 2%;">
            <h3 style="color: #2563eb; margin-bottom: 15px; font-size: 16px; border-bottom: 2px solid #2563eb; padding-bottom: 5px;">Resolution Status</h3>
            <table class="misconduct-table">
                <thead>
                    <tr>
                        <th>Status</th>
                        <th style="text-align: center;">Count</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($resolutionBreakdown as $item)
                        <tr>
                            <td>
                                <span class="status-badge status-{{ strtolower($item['status']) }}">{{ $item['status'] }}</span>
                            </td>
                            <td style="text-align: center; font-weight: bold;">{{ $item['count'] }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>

    <!-- Top Offending Drivers -->
    <h3 style="color: #2563eb; margin-bottom: 15px; font-size: 16px; border-bottom: 2px solid #2563eb; padding-bottom: 5px;">Top Offending Drivers</h3>
    <p style="font-size: 12px; color: #64748b; margin-bottom: 15px; font-style: italic;">
        @if(isset($viewType) && $viewType === 'alltime')
            All-time misconduct data • Trust score reflects all-time performance
        @else
            Misconduct count for {{ $month }} • Trust score reflects all-time performance
        @endif
    </p>
    @if(count($topOffenders) > 0)
        <table class="misconduct-table">
            <thead>
                <tr>
                    <th>Driver Name</th>
                    <th>Trust Score</th>
                    <th>Misconduct Count</th>
                    <th>Minibus</th>
                </tr>
            </thead>
            <tbody>
                @foreach($topOffenders as $driver)
                    <tr>
                        <td style="font-weight: bold;">{{ $driver['name'] }}</td>
                        <td>
                            <span class="trust-{{ $driver['trust_score'] >= 80 ? 'high' : ($driver['trust_score'] >= 60 ? 'medium' : 'low') }}">
                                {{ $driver['trust_score'] }}%
                            </span>
                        </td>
                        <td style="text-align: center; font-weight: bold; color: #dc2626;">{{ $driver['misconduct_count'] }}</td>
                        <td>{{ $driver['minibus'] }}</td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    @else
        <div style="text-align: center; padding: 40px; background-color: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px;">
            <p style="color: #64748b; font-style: italic; margin: 0;">No driver misconducts recorded this month.</p>
        </div>
    @endif

    <!-- Route Statistics and Trends -->
    <div style="display: table; width: 100%; margin-bottom: 25px;">
        <div style="display: table-cell; width: 48%; vertical-align: top; padding-right: 2%;">
            <h3 style="color: #2563eb; margin-bottom: 15px; font-size: 16px; border-bottom: 2px solid #2563eb; padding-bottom: 5px;">Misconducts by Route</h3>
            @if(count($routeStats) > 0)
                <table class="misconduct-table">
                    <thead>
                        <tr>
                            <th>Route Name</th>
                            <th style="text-align: center;">Count</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($routeStats as $route)
                            <tr>
                                <td style="font-weight: bold;">{{ $route->route_name }}</td>
                                <td style="text-align: center; font-weight: bold; color: #dc2626;">{{ $route->misconduct_count }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            @else
                <div style="text-align: center; padding: 20px; background-color: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px;">
                    <p style="color: #64748b; font-style: italic; margin: 0;">No route-specific data available.</p>
                </div>
            @endif
        </div>
        <div style="display: table-cell; width: 48%; vertical-align: top; padding-left: 2%;">
            <h3 style="color: #2563eb; margin-bottom: 15px; font-size: 16px; border-bottom: 2px solid #2563eb; padding-bottom: 5px;">6-Month Trend</h3>
            <table class="misconduct-table">
                <thead>
                    <tr>
                        <th>Month</th>
                        <th style="text-align: center;">Misconducts</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($trends as $trend)
                        <tr>
                            <td style="font-weight: bold;">{{ $trend['month'] }}</td>
                            <td style="text-align: center; font-weight: bold; color: #2563eb;">{{ $trend['count'] }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>

    <div class="footer">
        <p><strong>Minibus Owners Association Management System (MOAMS)</strong></p>
        <p>This report contains confidential information and is intended for authorized personnel only.</p>
        <p>Generated on {{ now()->format('F j, Y \a\t g:i A') }}</p>
    </div>
</body>
</html>
