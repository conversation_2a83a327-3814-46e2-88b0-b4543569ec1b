import React, { useState } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardHeader, CardTitle, CardContent } from '../../components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '../../components/ui/input';
import { Label } from '../../components/ui/label';
import { Link, usePage } from '@inertiajs/react';
import { navigateToLogicalParent } from '@/utils/navigation';
import { ChevronLeft, ChevronDown, Search, Filter, AlertTriangle, User, Plus } from 'lucide-react';
import Pagination from '@/components/ui/pagination';
import { route } from 'ziggy-js';

export default function UserMisconducts({ user, misconducts }) {
    const { userRoles, flash } = usePage().props;
    const isClerk = userRoles && userRoles.includes('association clerk');
    const isManager = userRoles && userRoles.includes('association manager');
    const isAdmin = userRoles && userRoles.includes('system admin');
    const canCreate = isClerk || isAdmin;
    const canEdit = isClerk || isAdmin;


    const [searchTerm, setSearchTerm] = useState('');

    // Filter misconducts by search term
    const filteredMisconducts = misconducts.data.filter(misconduct => {
        const term = searchTerm.toLowerCase();
        return (
            misconduct.name.toLowerCase().includes(term) ||
            misconduct.description?.toLowerCase().includes(term)
        );
    });

    // Use all filtered misconducts since pagination is handled server-side
    const displayedMisconducts = misconducts.data || [];

    const breadcrumbs = [
        { title: 'Misconduct Management', href: route('misconducts.index') },
        { title: `${user.first_name} ${user.last_name}`, href: route('users.misconducts', user.id) },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <div className="w-full max-w-full overflow-hidden">
                <div className="container mx-auto px-4 py-8">
                    {/* Header section */}
                    <div className="flex flex-col gap-4 mb-6">
                        <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
                            <button
                                className="flex items-center px-3 py-1 rounded hover:bg-gray-100 border border-gray-200 w-fit"
                                onClick={() => navigateToLogicalParent(breadcrumbs)}
                            >
                                <ChevronLeft className="h-4 w-4 mr-1" /> Back
                            </button>
                            <div className="flex-1 sm:flex-none">
                                <span className="text-muted-foreground text-sm sm:text-base font-medium break-words">
                                    Misconduct history for {user.first_name} {user.last_name}
                                </span>
                            </div>
                        </div>

                        {/* Action buttons row */}
                        <div className="flex flex-wrap items-center gap-2 justify-start sm:justify-end">
                            {canCreate && (
                                <Link href={route('misconducts.create')}>
                                    <Button className="w-fit flex items-center gap-2 bg-red-600 hover:bg-red-700 text-white px-4 py-2">
                                        <Plus className="h-4 w-4" />
                                        Report New Misconduct
                                    </Button>
                                </Link>
                            )}
                        </div>
                    </div>

                    {/* User Information Card */}
                    <Card className="mb-6">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <User className="h-5 w-5 text-blue-600" />
                                Minibus Owner Information
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <span className="text-sm font-medium text-gray-500">Name</span>
                                    <p className="font-medium">{user.first_name} {user.last_name}</p>
                                </div>
                                <div>
                                    <span className="text-sm font-medium text-gray-500">Email</span>
                                    <p>{user.email}</p>
                                </div>
                                <div>
                                    <span className="text-sm font-medium text-gray-500">Phone</span>
                                    <p>{user.phone_number}</p>
                                </div>
                                <div>
                                    <span className="text-sm font-medium text-gray-500">District</span>
                                    <p>{user.district}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Search Card */}
                    <Card className="mb-6">
                        <CardHeader>
                            <CardTitle className="text-lg">Search Misconducts</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <Label htmlFor="search">Search</Label>
                                    <div className="relative">
                                        <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                                        <Input
                                            id="search"
                                            placeholder="Search by misconduct type or description..."
                                            value={searchTerm}
                                            onChange={e => setSearchTerm(e.target.value)}
                                            className="pl-10"
                                        />
                                    </div>
                                </div>
                                <div className="flex items-end">
                                    <Button
                                        variant="outline"
                                        onClick={() => {
                                            setSearchTerm('');
                                            setDisplayCount(6);
                                        }}
                                        className="w-full"
                                    >
                                        <Filter className="h-4 w-4 mr-2" />
                                        Clear Filters
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Misconduct Summary */}
                    <div className="mb-6">
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <h3 className="text-lg font-medium text-blue-900">Misconduct Summary</h3>
                                    <p className="text-blue-700">
                                        Total misconducts: {misconducts.total} |
                                        Showing: {displayedMisconducts.length} of {filteredMisconducts.length}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Misconducts Table Container - Fixed width that fits screen */}
                    <div
                        className="w-full border-2 border-blue-200 rounded-lg"
                        style={{
                            maxWidth: 'calc(100vw - 2rem)',
                            overflow: 'hidden'
                        }}
                    >
                        <Card className="w-full border-0">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <AlertTriangle className="h-5 w-5" />
                                    Driver Misconducts Reported ({displayedMisconducts.length})
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="p-0">
                                {displayedMisconducts.length === 0 ? (
                                    <div className="text-center py-8 px-6">
                                        <AlertTriangle className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                                        <h3 className="text-lg font-medium mb-2">No driver misconducts reported by this minibus owner</h3>
                                        <p className="text-muted-foreground mb-4">
                                            {searchTerm ?
                                                "Try adjusting your search criteria." :
                                                "This minibus owner has not reported any driver misconducts."
                                            }
                                        </p>
                                    </div>
                                ) : (
                                    <div className="overflow-x-auto">
                                        <Table className="w-full min-w-[600px]">
                                            <TableHeader>
                                                <TableRow>
                                                    <TableHead>Misconduct Type</TableHead>
                                                    <TableHead>Date</TableHead>
                                                    <TableHead>Description</TableHead>
                                                    <TableHead>Actions</TableHead>
                                                </TableRow>
                                            </TableHeader>
                                            <TableBody>
                                                {displayedMisconducts.map(misconduct => (
                                                    <TableRow key={misconduct.id}>
                                                        <TableCell className="font-medium">
                                                            {misconduct.name}
                                                        </TableCell>
                                                        <TableCell>
                                                            {new Date(misconduct.offense_date).toLocaleDateString()}
                                                        </TableCell>
                                                        <TableCell>
                                                            <div className="max-w-xs truncate" title={misconduct.description || 'N/A'}>
                                                                {misconduct.description || 'N/A'}
                                                            </div>
                                                        </TableCell>
                                                        <TableCell>
                                                            <div className="flex items-center gap-2">
                                                                <Link href={route('misconducts.show', misconduct.id)}>
                                                                    <Button variant="outline" size="sm" className="text-blue-700 border-blue-500 hover:bg-blue-50">
                                                                        View Details
                                                                    </Button>
                                                                </Link>
                                                                {canEdit && (
                                                                    <Link href={route('misconducts.edit', misconduct.id)}>
                                                                        <Button variant="secondary" size="sm" className="bg-indigo-100 text-indigo-800 hover:bg-indigo-200">
                                                                            Edit
                                                                        </Button>
                                                                    </Link>
                                                                )}
                                                            </div>
                                                        </TableCell>
                                                    </TableRow>
                                                ))}
                                            </TableBody>
                                        </Table>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>

                    {/* Pagination */}
                    {misconducts.total > 0 && misconducts.total > misconducts.per_page && (
                        <div className="mt-6 w-full">
                            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 px-4 py-4 border-t bg-gray-50/50 rounded-b-lg">
                                <div className="text-sm text-gray-700 text-center sm:text-left">
                                    Showing {misconducts.from} to {misconducts.to} of {misconducts.total} results
                                </div>
                                {misconducts.last_page > 1 && (
                                    <Pagination data={misconducts} />
                                )}
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </AppLayout>
    );
} 