import React from "react";
import {
    Dialog,
    DialogContent,
    Di<PERSON>Header,
    DialogTitle,
    DialogDescription,
    DialogFooter,
    DialogClose,
} from "./dialog";
import { Button } from "./button";

export default function ConfirmDialog({
    open,
    title = "Are you sure?",
    description = "This action cannot be undone.",
    onConfirm,
    onCancel,
    confirmText = "Confirm",
    cancelText = "Cancel",
    loading = false,
    confirmVariant = "destructive",
    children,
}) {
    return (
        <Dialog open={open} onOpenChange={open => { if (!open && onCancel) onCancel(); }}>
            <DialogContent showCloseButton={false}>
                <DialogHeader>
                    <DialogTitle>{title}</DialogTitle>
                    <DialogDescription>{description}</DialogDescription>
                </DialogHeader>
                {children}
                <DialogFooter>
                    <DialogClose asChild>
                        <Button type="button" variant="outline" onClick={onCancel} disabled={loading}>
                            {cancelText}
                        </Button>
                    </DialogClose>
                    <Button type="button" variant={confirmVariant} onClick={onConfirm} disabled={loading}>
                        {loading ? "Processing..." : confirmText}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
} 