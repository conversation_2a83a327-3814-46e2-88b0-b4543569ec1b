import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router } from '@inertiajs/react';
import {
    AlertTriangle,
    RefreshCw,
    Home,
    ArrowLeft,
    Clock,
    Shield,
    FileX,
    CreditCard,
    Database,
    Wifi,
    AlertCircle,
    CheckCircle
} from 'lucide-react';

export default function ErrorPage({
    status = 500,
    title = 'Something went wrong',
    description = 'An unexpected error occurred. Please try again later or contact support if the problem persists.',
    error_type = 'general',
    recoverable = false,
    retryable = false,
    error_id = null,
    timestamp = null
}) {
    const [isPaychanguRedirect, setIsPaychanguRedirect] = useState(false);
    const [correctUrl, setCorrectUrl] = useState('');
    const [retryCount, setRetryCount] = useState(0);
    const [isRetrying, setIsRetrying] = useState(false);
    const [autoRetryCountdown, setAutoRetryCountdown] = useState(0);

    // Get error icon based on error type
    const getErrorIcon = () => {
        switch (error_type) {
            case 'payment':
            case 'payment_redirect':
                return <CreditCard className="w-16 h-16 text-red-500" />;
            case 'file_upload':
                return <FileX className="w-16 h-16 text-red-500" />;
            case 'database':
                return <Database className="w-16 h-16 text-red-500" />;
            case 'network':
                return <Wifi className="w-16 h-16 text-red-500" />;
            case 'unauthorized':
                return <Shield className="w-16 h-16 text-yellow-500" />;
            case 'forbidden':
                return <Shield className="w-16 h-16 text-red-500" />;
            case 'csrf':
                return <Clock className="w-16 h-16 text-orange-500" />;
            case 'throttle':
                return <Clock className="w-16 h-16 text-blue-500" />;
            case 'not_found':
                return <AlertCircle className="w-16 h-16 text-gray-500" />;
            default:
                return <AlertTriangle className="w-16 h-16 text-red-500" />;
        }
    };

    // Get status color based on error type
    const getStatusColor = () => {
        switch (error_type) {
            case 'unauthorized':
                return 'text-yellow-600';
            case 'csrf':
            case 'throttle':
                return 'text-orange-600';
            case 'not_found':
                return 'text-gray-600';
            default:
                return 'text-red-600';
        }
    };

    // Handle retry functionality
    const handleRetry = async () => {
        if (!retryable || isRetrying) return;

        setIsRetrying(true);
        setRetryCount(prev => prev + 1);

        try {
            // Wait a bit before retrying
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Reload the current page
            window.location.reload();
        } catch (error) {
            console.error('Retry failed:', error);
            setIsRetrying(false);
        }
    };

    // Auto-retry for certain error types
    useEffect(() => {
        if (retryable && retryCount === 0 && ['database', 'network', 'csrf'].includes(error_type)) {
            const countdown = 10; // 10 seconds
            setAutoRetryCountdown(countdown);

            const timer = setInterval(() => {
                setAutoRetryCountdown(prev => {
                    if (prev <= 1) {
                        clearInterval(timer);
                        handleRetry();
                        return 0;
                    }
                    return prev - 1;
                });
            }, 1000);

            return () => clearInterval(timer);
        }
    }, [retryable, retryCount, error_type]);

    useEffect(() => {
        // Check if this is a 404 error and if the URL contains payment-related parameters
        if (status === 404 || error_type === 'payment_redirect') {
            const currentUrl = window.location.href;
            const urlParams = new URLSearchParams(window.location.search);
            const txRef = urlParams.get('tx_ref');
            const statusParam = urlParams.get('status');

            // Check if this looks like a payment redirect that's missing the port
            const isPaymentUrl = (
                txRef ||
                statusParam ||
                currentUrl.includes('paychangu') ||
                currentUrl.includes('ctechpay') ||
                currentUrl.includes('/payments/') ||
                (currentUrl.includes('127.0.0.1') && !currentUrl.includes(':8000'))
            );

            if (isPaychanguUrl) {
                setIsPaychanguRedirect(true);

                // Construct the correct URL with port 8000
                let baseUrl = 'http://127.0.0.1:8000';
                let path = '/payments/paychangu/return';

                // If the current URL has a path, try to preserve it
                const currentPath = window.location.pathname;
                if (currentPath.includes('/payments/')) {
                    path = currentPath;
                } else if (currentPath.includes('paychangu')) {
                    path = '/paychangu-redirect';
                }

                // Build the correct URL with parameters
                let fixedUrl = baseUrl + path;
                if (window.location.search) {
                    fixedUrl += window.location.search;
                }

                setCorrectUrl(fixedUrl);
            }
        }
    }, [status, error_type]);

    // Auto-redirect for payment URLs after a short delay
    useEffect(() => {
        if (isPaychanguRedirect && correctUrl) {
            const timer = setTimeout(() => {
                window.location.href = correctUrl;
            }, 3000); // Auto-redirect after 3 seconds

            return () => clearTimeout(timer);
        }
    }, [isPaychanguRedirect, correctUrl]);

    // Render recovery actions based on error type
    const renderRecoveryActions = () => {
        const actions = [];

        // Retry button for retryable errors
        if (retryable) {
            actions.push(
                <Button
                    key="retry"
                    onClick={handleRetry}
                    disabled={isRetrying}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3"
                >
                    {isRetrying ? (
                        <>
                            <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                            Retrying...
                        </>
                    ) : (
                        <>
                            <RefreshCw className="w-4 h-4 mr-2" />
                            Try Again {retryCount > 0 && `(${retryCount})`}
                        </>
                    )}
                </Button>
            );
        }

        // Go back button for recoverable errors
        if (recoverable) {
            actions.push(
                <Button
                    key="back"
                    onClick={() => window.history.back()}
                    variant="outline"
                    className="w-full"
                >
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Go Back
                </Button>
            );
        }

        // Login button for authentication errors
        if (error_type === 'unauthorized') {
            actions.push(
                <Link key="login" href="/login">
                    <Button className="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3">
                        <Shield className="w-4 h-4 mr-2" />
                        Login
                    </Button>
                </Link>
            );
        }

        // Refresh page for CSRF errors
        if (error_type === 'csrf') {
            actions.push(
                <Button
                    key="refresh"
                    onClick={() => window.location.reload()}
                    className="w-full bg-orange-600 hover:bg-orange-700 text-white font-semibold py-3"
                >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Refresh Page
                </Button>
            );
        }

        // Dashboard button (always available)
        actions.push(
            <Link key="dashboard" href="/dashboard">
                <Button variant="outline" className="w-full">
                    <Home className="w-4 h-4 mr-2" />
                    Go to Dashboard
                </Button>
            </Link>
        );

        return actions;
    };

    if (isPaychanguRedirect) {
        return (
            <AppLayout>
                <Head title="Payment Redirect - Page Not Found" />
                <div className="flex flex-col items-center justify-center min-h-[60vh] p-8">
                    <Card className="max-w-lg w-full text-center">
                        <CardHeader>
                            <CardTitle className="text-4xl font-bold text-orange-600 mb-2">404</CardTitle>
                            <h2 className="text-2xl font-semibold mb-2">Payment Redirect Issue</h2>
                        </CardHeader>
                        <CardContent>
                            <p className="text-gray-600 mb-4">
                                It looks like you're being redirected from a payment page, but the URL is missing some information.
                            </p>
                            <p className="text-gray-600 mb-6">
                                Don't worry! We can fix this for you.
                            </p>

                            <div className="space-y-4">
                                <Button
                                    onClick={() => window.location.href = correctUrl}
                                    className="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3"
                                >
                                    🔗 Click Here to Continue to Payment Results
                                </Button>

                                <p className="text-sm text-gray-500">
                                    You will be automatically redirected in a few seconds...
                                </p>

                                <div className="pt-4 border-t">
                                    <Link href="/dashboard">
                                        <Button variant="outline" className="w-full">
                                            Or Go to Dashboard
                                        </Button>
                                    </Link>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </AppLayout>
        );
    }

    return (
        <AppLayout>
            <Head title={title} />
            <div className="flex flex-col items-center justify-center min-h-[60vh] p-8">
                <Card className="max-w-2xl w-full">
                    <CardHeader className="text-center">
                        <div className="flex justify-center mb-4">
                            {getErrorIcon()}
                        </div>
                        <CardTitle className={`text-4xl font-bold mb-2 ${getStatusColor()}`}>
                            {status}
                        </CardTitle>
                        <h2 className="text-2xl font-semibold mb-2 text-gray-800">{title}</h2>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        <p className="text-gray-600 text-center text-lg">{description}</p>

                        {/* Auto-retry countdown */}
                        {autoRetryCountdown > 0 && (
                            <Alert className="border-blue-200 bg-blue-50">
                                <Clock className="h-4 w-4 text-blue-600" />
                                <AlertDescription className="text-blue-800">
                                    Automatically retrying in {autoRetryCountdown} seconds...
                                </AlertDescription>
                            </Alert>
                        )}

                        {/* Error details for debugging (only show error_id) */}
                        {error_id && (
                            <div className="text-center">
                                <p className="text-sm text-gray-500">
                                    Error ID: <code className="bg-gray-100 px-2 py-1 rounded text-xs">{error_id}</code>
                                </p>
                                {timestamp && (
                                    <p className="text-xs text-gray-400 mt-1">
                                        {new Date(timestamp).toLocaleString()}
                                    </p>
                                )}
                            </div>
                        )}

                        {/* Recovery actions */}
                        <div className="space-y-3">
                            {renderRecoveryActions().map((action, index) => (
                                <div key={index}>{action}</div>
                            ))}
                        </div>

                        {/* Additional help text based on error type */}
                        {error_type === 'database' && (
                            <Alert className="border-yellow-200 bg-yellow-50">
                                <Database className="h-4 w-4 text-yellow-600" />
                                <AlertDescription className="text-yellow-800">
                                    This appears to be a temporary database issue. The system will automatically retry shortly.
                                </AlertDescription>
                            </Alert>
                        )}

                        {error_type === 'network' && (
                            <Alert className="border-blue-200 bg-blue-50">
                                <Wifi className="h-4 w-4 text-blue-600" />
                                <AlertDescription className="text-blue-800">
                                    Please check your internet connection and try again.
                                </AlertDescription>
                            </Alert>
                        )}

                        {error_type === 'file_upload' && (
                            <Alert className="border-orange-200 bg-orange-50">
                                <FileX className="h-4 w-4 text-orange-600" />
                                <AlertDescription className="text-orange-800">
                                    Make sure your file is under 5MB and in a supported format (PDF, JPG, PNG).
                                </AlertDescription>
                            </Alert>
                        )}

                        {error_type === 'payment' && (
                            <Alert className="border-red-200 bg-red-50">
                                <CreditCard className="h-4 w-4 text-red-600" />
                                <AlertDescription className="text-red-800">
                                    If you were charged, please contact support with your transaction details.
                                </AlertDescription>
                            </Alert>
                        )}

                        {retryCount > 2 && (
                            <Alert className="border-gray-200 bg-gray-50">
                                <AlertCircle className="h-4 w-4 text-gray-600" />
                                <AlertDescription className="text-gray-800">
                                    If the problem persists, please contact <NAME_EMAIL> or try again later.
                                </AlertDescription>
                            </Alert>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}