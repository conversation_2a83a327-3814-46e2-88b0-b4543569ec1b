import React, { useState, useCallback } from 'react';
import { useForm } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Bus } from 'lucide-react';
import { Link } from '@inertiajs/react';
import InputError from '@/components/input-error';
import { Label } from '@/components/ui/label';
import { MinibusOwnerCombobox } from '@/components/ui/minibus-owner-combobox';
import { route } from 'ziggy-js';

export default function TransferMinibus({ minibus, minibusOwners, userRole }) {
    const owners = minibusOwners || [];
    const [selectedOwner, setSelectedOwner] = useState(null);
    const { data, setData, post, processing, errors } = useForm({
        new_owner_id: '',
        transfer_type: 'internal',
    });

    const breadcrumbs = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: userRole === 'minibus owner' ? 'My Minibuses' : 'Minibus Management', href: '/minibuses' },
        { title: minibus.number_plate, href: `/minibuses/${minibus.id}` },
        { title: 'Transfer Ownership', href: `/minibuses/${minibus.id}/transfer` },
    ];

    const handleTransferTypeChange = (value) => {
        setData(data => ({
            ...data,
            transfer_type: value,
            new_owner_id: value === 'external' ? '' : data.new_owner_id
        }));
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        post(route('minibuses.transfer.process', minibus.id));
    };

    const fetchOwners = useCallback(async (search) => {
        const res = await fetch(`/api/minibus-owners?search=${encodeURIComponent(search)}`);
        // Exclude the current owner from the options
        return (await res.json()).filter(owner =>
            owner.id !== null &&
            owner.id !== undefined &&
            owner.id !== "" &&
            String(owner.id) !== String(minibus.owner?.user?.id)
        );
    }, [minibus.owner?.user?.id]);

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <div className="p-2 sm:p-4 md:p-8">
                <div className="flex flex-col sm:flex-row gap-2 sm:gap-4 items-center sm:items-center sm:justify-between mb-4 sm:mb-6">
                    <Button variant="outline" onClick={() => window.history.back()} className="w-full sm:w-auto">
                        <ArrowLeft className="h-4 w-4 mr-2" />
                        Back
                    </Button>
                    <h1 className="text-lg sm:text-xl md:text-2xl font-bold text-center sm:text-left">Transfer Minibus Ownership</h1>
                </div>

                <div className="w-full max-w-3xl mx-auto">
                    <Card className="p-4 sm:p-6">
                        <div className="mb-6">
                            <h3 className="text-base sm:text-lg md:text-xl font-semibold mb-3">
                                <Bus className="inline-block text-gray-500 mr-2 h-5 w-5 align-text-bottom" />
                                {minibus.number_plate}
                            </h3>
                            <p className="text-sm sm:text-base text-gray-600">
                                Current Owner: {minibus.owner?.user?.first_name} {minibus.owner?.user?.last_name}
                            </p>
                        </div>

                        <form onSubmit={handleSubmit} className="space-y-6">
                            <div className="space-y-2">
                                <Label htmlFor="transfer_type">Transfer Type</Label>
                                <select
                                    id="transfer_type"
                                    value={data.transfer_type}
                                    onChange={e => handleTransferTypeChange(e.target.value)}
                                    className="mt-1 block w-full px-3 py-2 text-base border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                                    disabled={processing}
                                >
                                    <option value="internal">Internal Transfer (To Another Member)</option>
                                    <option value="external">External Transfer (To Non-Member)</option>
                                </select>
                                <InputError message={errors.transfer_type} />
                            </div>

                            {data.transfer_type === 'internal' && (
                                <div className="space-y-2">
                                    <Label htmlFor="new_owner_id">New Owner</Label>
                                    <MinibusOwnerCombobox
                                        value={selectedOwner || null}
                                        onChange={owner => {
                                            setSelectedOwner(owner);
                                            setData('new_owner_id', owner?.id || '');
                                        }}
                                        fetchOwners={fetchOwners}
                                        placeholder="Select new owner..."
                                    />
                                    <InputError message={errors.new_owner_id} />
                                </div>
                            )}

                            {data.transfer_type === 'external' && (
                                <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                                    <p className="text-sm text-yellow-700">
                                        Note: External transfer will mark this minibus as transferred to a non-member and archive it. Only association clerks will be able to access it until the new owner joins the association.
                                    </p>
                                </div>
                            )}

                            <div className="flex justify-end space-x-3 pt-4">
                                <Link href={route('minibuses.show', minibus.id)}>
                                    <Button type="button" variant="outline" disabled={processing}>
                                        Cancel
                                    </Button>
                                </Link>
                                <Button
                                    type="submit"
                                    disabled={processing || (!data.new_owner_id && data.transfer_type === 'internal')}
                                    className="bg-green-500 hover:bg-green-600 text-white disabled:bg-green-300"
                                >
                                    {processing ? 'Processing...' : 'Transfer Ownership'}
                                </Button>
                            </div>
                        </form>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
} 