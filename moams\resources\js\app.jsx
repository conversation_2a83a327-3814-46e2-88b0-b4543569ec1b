import '../css/app.css';

import { createInertiaApp } from '@inertiajs/react';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';
import { createRoot } from 'react-dom/client';
import { initializeTheme } from './hooks/use-appearance';
import { route } from 'ziggy-js';
import ErrorBoundary from './components/ErrorBoundary';

const appName = import.meta.env.VITE_APP_NAME || 'Moams';

createInertiaApp({
    title: (title) => `${title} - ${appName}`,
    resolve: (name) => resolvePageComponent(`./pages/${name}.jsx`, import.meta.glob('./pages/**/*.jsx')),
    setup({ el, App, props }) {
        const root = createRoot(el);
        try {
            root.render(
                <ErrorBoundary
                    errorContext={{
                        app: 'main',
                        page: props.initialPage?.component,
                        props: Object.keys(props.initialPage?.props || {}),
                    }}
                >
                    <App {...props} />
                </ErrorBoundary>
            );
        } catch (error) {
            console.error('Error rendering Inertia app:', error);

            // Fallback error UI if even the error boundary fails
            root.render(
                <div style={{
                    color: 'red',
                    padding: 20,
                    fontFamily: 'monospace',
                    backgroundColor: '#fee',
                    border: '1px solid #fcc',
                    borderRadius: '4px',
                    margin: '20px',
                }}>
                    <h1 style={{ color: '#c00', marginBottom: '10px' }}>Critical App Error</h1>
                    <p style={{ marginBottom: '10px' }}>
                        The application failed to start. Please refresh the page or contact support.
                    </p>
                    <details>
                        <summary style={{ cursor: 'pointer', marginBottom: '10px' }}>Error Details</summary>
                        <pre style={{
                            backgroundColor: '#fff',
                            padding: '10px',
                            border: '1px solid #ddd',
                            borderRadius: '4px',
                            overflow: 'auto',
                            fontSize: '12px',
                        }}>
                            {error && error.toString()}
                            {error && error.stack && '\n\nStack Trace:\n' + error.stack}
                        </pre>
                    </details>
                    <button
                        onClick={() => window.location.reload()}
                        style={{
                            backgroundColor: '#007cba',
                            color: 'white',
                            border: 'none',
                            padding: '10px 20px',
                            borderRadius: '4px',
                            cursor: 'pointer',
                            marginTop: '10px',
                        }}
                    >
                        Reload Page
                    </button>
                </div>
            );
        }
    },
    progress: {
        color: '#4B5563',
    },
});

// This will set light / dark mode on load...
initializeTheme();
