import React from 'react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertCircle, CreditCard, User, Calendar, DollarSign, Phone, Mail, Copy } from 'lucide-react';
import { Link } from '@inertiajs/react';
import { route } from 'ziggy-js';

export default function PaymentVerification({ payment, associationClerk }) {
    const [copied, setCopied] = React.useState(false);

    const copyOrderReference = () => {
        if (payment.ctechpay_order_reference) {
            navigator.clipboard.writeText(payment.ctechpay_order_reference);
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        }
    };

    // Define breadcrumbs for payment verification
    const breadcrumbs = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'My Membership', href: '/my-membership' },
        { title: 'Payment Verification', href: '#' },
    ];

    if (!payment) {
        return (
            <AppLayout title="Payment Not Found" breadcrumbs={breadcrumbs}>
                <div className="max-w-2xl mx-auto py-8 px-4 text-center">
                    <h1 className="text-2xl font-bold text-red-600 mb-4">Payment Not Found</h1>
                    <p className="text-gray-600 mb-4">The payment information could not be loaded.</p>
                    <Link href={route('dashboard')} className="text-blue-600 hover:underline">
                        Return to Dashboard
                    </Link>
                </div>
            </AppLayout>
        );
    }

    return (
        <AppLayout title="Payment Verification Needed" breadcrumbs={breadcrumbs}>
            <div className="max-w-2xl mx-auto py-8 px-4">
                {/* Verification Header */}
                <div className="text-center mb-8">
                    <div className="flex justify-center mb-4">
                        <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center">
                            <AlertCircle className="h-10 w-10 text-yellow-600" />
                        </div>
                    </div>
                    <h1 className="text-3xl font-bold text-yellow-700 mb-2">
                        Payment Verification Needed
                    </h1>
                    <p className="text-gray-600">
                        We couldn't automatically verify your payment status. Please contact MOAM staff for assistance.
                    </p>
                </div>

                {/* Important Notice */}
                <Card className="mb-6 border-yellow-200 bg-yellow-50">
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2 text-yellow-800">
                            <AlertCircle className="h-5 w-5" />
                            Important Information
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                        <p className="text-yellow-800">
                            Your payment may have been processed successfully, but we couldn't automatically confirm the status.
                            This sometimes happens due to technical issues with the payment gateway.
                        </p>
                        <p className="text-yellow-800 font-medium">
                            Please contact MOAM staff with your order reference number below to verify your payment status.
                        </p>
                    </CardContent>
                </Card>

                {/* Payment Details Card */}
                <Card className="mb-6">
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <CreditCard className="h-5 w-5" />
                            Payment Details
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="flex items-center gap-3">
                                <DollarSign className="h-5 w-5 text-gray-600" />
                                <div>
                                    <div className="text-sm text-gray-600">Amount</div>
                                    <div className="font-semibold">
                                        MWK {parseFloat(payment.amount).toLocaleString()}
                                    </div>
                                </div>
                            </div>

                            <div className="flex items-center gap-3">
                                <User className="h-5 w-5 text-gray-600" />
                                <div>
                                    <div className="text-sm text-gray-600">Fee Type</div>
                                    <div className="font-semibold capitalize">
                                        {payment.fee_type}
                                    </div>
                                </div>
                            </div>

                            <div className="flex items-center gap-3">
                                <CreditCard className="h-5 w-5 text-gray-600" />
                                <div>
                                    <div className="text-sm text-gray-600">Payment Method</div>
                                    <div className="font-semibold">
                                        {payment.payment_method === 'ctechpay_card' ? 'Card Payment' : payment.payment_method}
                                    </div>
                                </div>
                            </div>

                            <div className="flex items-center gap-3">
                                <Calendar className="h-5 w-5 text-gray-600" />
                                <div>
                                    <div className="text-sm text-gray-600">Date & Time</div>
                                    <div className="font-semibold">
                                        {new Date(payment.created_at).toLocaleString()}
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Order Reference - Most Important */}
                        {payment.ctechpay_order_reference && (
                            <div className="pt-4 border-t border-gray-200">
                                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                    <div className="text-sm text-blue-800 font-medium mb-2">
                                        Order Reference Number (Important!)
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <span className="font-mono text-lg font-bold text-blue-900 bg-white px-3 py-2 rounded border flex-1">
                                            {payment.ctechpay_order_reference}
                                        </span>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={copyOrderReference}
                                            className="flex items-center gap-1"
                                        >
                                            <Copy className="h-4 w-4" />
                                            {copied ? 'Copied!' : 'Copy'}
                                        </Button>
                                    </div>
                                    <div className="text-xs text-blue-700 mt-2">
                                        Provide this reference number to MOAM staff for payment verification
                                    </div>
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Contact Information */}
                <Card className="mb-6">
                    <CardHeader>
                        <CardTitle>Contact MOAM Staff</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            <p className="text-gray-600 mb-4">
                                Please contact MOAM staff using any of the methods below. Have your order reference number ready.
                            </p>

                            {associationClerk ? (
                                <div className="space-y-3">
                                    <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                                        <Phone className="h-5 w-5 text-blue-600" />
                                        <div>
                                            <div className="font-medium">
                                                Phone - {associationClerk.first_name} {associationClerk.last_name}
                                            </div>
                                            <div className="text-sm text-gray-600">
                                                Call association clerk during business hours
                                            </div>
                                            <div className="text-blue-600 font-medium">{associationClerk.phone_number}</div>
                                        </div>
                                    </div>

                                    <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                                        <Mail className="h-5 w-5 text-blue-600" />
                                        <div>
                                            <div className="font-medium">
                                                Email - {associationClerk.first_name} {associationClerk.last_name}
                                            </div>
                                            <div className="text-sm text-gray-600">
                                                Send an email with your order reference
                                            </div>
                                            <a href={`mailto:${associationClerk.email}`} className="text-blue-600 font-medium hover:underline">
                                                {associationClerk.email}
                                            </a>
                                        </div>
                                    </div>

                                    <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                                        <User className="h-5 w-5 text-blue-600" />
                                        <div>
                                            <div className="font-medium">Visit Office</div>
                                            <div className="text-sm text-gray-600">
                                                Visit MOAM office with your order reference number
                                            </div>
                                            <div className="text-blue-600 font-medium">MOAM Office, Blantyre</div>
                                        </div>
                                    </div>
                                </div>
                            ) : (
                                <div className="space-y-3">
                                    <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                                        <Phone className="h-5 w-5 text-blue-600" />
                                        <div>
                                            <div className="font-medium">Phone</div>
                                            <div className="text-sm text-gray-600">
                                                Call MOAM office during business hours
                                            </div>
                                            <div className="text-blue-600 font-medium">Contact information not available</div>
                                        </div>
                                    </div>

                                    <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                                        <Mail className="h-5 w-5 text-blue-600" />
                                        <div>
                                            <div className="font-medium">Email</div>
                                            <div className="text-sm text-gray-600">
                                                Send an email with your order reference
                                            </div>
                                            <div className="text-blue-600 font-medium">Contact information not available</div>
                                        </div>
                                    </div>

                                    <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                                        <User className="h-5 w-5 text-blue-600" />
                                        <div>
                                            <div className="font-medium">Visit Office</div>
                                            <div className="text-sm text-gray-600">
                                                Visit MOAM office with your order reference number
                                            </div>
                                            <div className="text-blue-600 font-medium">MOAM Office, Blantyre</div>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>

                {/* What to Expect */}
                <Card className="mb-6">
                    <CardHeader>
                        <CardTitle>What to Expect</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-3">
                            <div className="flex items-start gap-3">
                                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                    <span className="text-blue-600 text-sm font-semibold">1</span>
                                </div>
                                <div>
                                    <div className="font-medium">Quick Verification</div>
                                    <div className="text-sm text-gray-600">
                                        MOAM staff will check your payment status using your order reference number.
                                    </div>
                                </div>
                            </div>

                            <div className="flex items-start gap-3">
                                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                    <span className="text-blue-600 text-sm font-semibold">2</span>
                                </div>
                                <div>
                                    <div className="font-medium">Status Update</div>
                                    <div className="text-sm text-gray-600">
                                        Your payment status will be updated manually once verified.
                                    </div>
                                </div>
                            </div>

                            <div className="flex items-start gap-3">
                                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                    <span className="text-blue-600 text-sm font-semibold">3</span>
                                </div>
                                <div>
                                    <div className="font-medium">Confirmation</div>
                                    <div className="text-sm text-gray-600">
                                        You'll receive confirmation once your payment is verified and processed.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-3 justify-center">
                    <Button asChild variant="outline">
                        <Link href={route('dashboard')}>
                            Return to Dashboard
                        </Link>
                    </Button>

                    <Button asChild>
                        <Link href={route('my.membership')}>
                            View My Membership
                        </Link>
                    </Button>
                </div>

                {/* Support Information */}
                <div className="mt-8 text-center text-sm text-gray-500">
                    <p>
                        This verification process ensures accurate payment tracking and prevents any issues with your membership status.
                    </p>
                </div>
            </div>
        </AppLayout>
    );
}
