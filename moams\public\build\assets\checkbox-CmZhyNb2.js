import{r as i,j as o,q as N,g as L}from"./app-DL-qYY5V.js";import{c as O,P as H,u as G,a as j,b as K,C as U}from"./index-1tPPImJd.js";import{u as X}from"./index-uC6ZAdKJ.js";import{P as R}from"./index-BTzg1GwG.js";var y="Checkbox",[$,te]=O(y),[J,_]=$(y);function Q(t){const{__scopeCheckbox:n,checked:c,children:l,defaultChecked:s,disabled:e,form:f,name:b,onCheckedChange:d,required:k,value:x="on",internal_do_not_use_render:u}=t,[p,v]=G({prop:c,defaultProp:s??!1,onChange:d,caller:y}),[C,m]=i.useState(null),[g,r]=i.useState(null),a=i.useRef(!1),E=C?!!f||!!C.closest("form"):!0,P={checked:p,disabled:e,setChecked:v,control:C,setControl:m,name:b,form:f,value:x,hasConsumerStoppedPropagationRef:a,required:k,defaultChecked:h(s)?!1:s,isFormControl:E,bubbleInput:g,setBubbleInput:r};return o.jsx(J,{scope:n,...P,children:V(u)?u(P):l})}var S="CheckboxTrigger",w=i.forwardRef(({__scopeCheckbox:t,onKeyDown:n,onClick:c,...l},s)=>{const{control:e,value:f,disabled:b,checked:d,required:k,setControl:x,setChecked:u,hasConsumerStoppedPropagationRef:p,isFormControl:v,bubbleInput:C}=_(S,t),m=N(s,x),g=i.useRef(d);return i.useEffect(()=>{const r=e==null?void 0:e.form;if(r){const a=()=>u(g.current);return r.addEventListener("reset",a),()=>r.removeEventListener("reset",a)}},[e,u]),o.jsx(R.button,{type:"button",role:"checkbox","aria-checked":h(d)?"mixed":d,"aria-required":k,"data-state":z(d),"data-disabled":b?"":void 0,disabled:b,value:f,...l,ref:m,onKeyDown:j(n,r=>{r.key==="Enter"&&r.preventDefault()}),onClick:j(c,r=>{u(a=>h(a)?!0:!a),C&&v&&(p.current=r.isPropagationStopped(),p.current||r.stopPropagation())})})});w.displayName=S;var B=i.forwardRef((t,n)=>{const{__scopeCheckbox:c,name:l,checked:s,defaultChecked:e,required:f,disabled:b,value:d,onCheckedChange:k,form:x,...u}=t;return o.jsx(Q,{__scopeCheckbox:c,checked:s,defaultChecked:e,disabled:b,required:f,onCheckedChange:k,name:l,form:x,value:d,internal_do_not_use_render:({isFormControl:p})=>o.jsxs(o.Fragment,{children:[o.jsx(w,{...u,ref:n,__scopeCheckbox:c}),p&&o.jsx(A,{__scopeCheckbox:c})]})})});B.displayName=y;var q="CheckboxIndicator",M=i.forwardRef((t,n)=>{const{__scopeCheckbox:c,forceMount:l,...s}=t,e=_(q,c);return o.jsx(H,{present:l||h(e.checked)||e.checked===!0,children:o.jsx(R.span,{"data-state":z(e.checked),"data-disabled":e.disabled?"":void 0,...s,ref:n,style:{pointerEvents:"none",...t.style}})})});M.displayName=q;var T="CheckboxBubbleInput",A=i.forwardRef(({__scopeCheckbox:t,...n},c)=>{const{control:l,hasConsumerStoppedPropagationRef:s,checked:e,defaultChecked:f,required:b,disabled:d,name:k,value:x,form:u,bubbleInput:p,setBubbleInput:v}=_(T,t),C=N(c,v),m=X(e),g=K(l);i.useEffect(()=>{const a=p;if(!a)return;const E=window.HTMLInputElement.prototype,I=Object.getOwnPropertyDescriptor(E,"checked").set,D=!s.current;if(m!==e&&I){const F=new Event("click",{bubbles:D});a.indeterminate=h(e),I.call(a,h(e)?!1:e),a.dispatchEvent(F)}},[p,m,e,s]);const r=i.useRef(h(e)?!1:e);return o.jsx(R.input,{type:"checkbox","aria-hidden":!0,defaultChecked:f??r.current,required:b,disabled:d,name:k,value:x,form:u,...n,tabIndex:-1,ref:C,style:{...n.style,...g,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});A.displayName=T;function V(t){return typeof t=="function"}function h(t){return t==="indeterminate"}function z(t){return h(t)?"indeterminate":t?"checked":"unchecked"}function re({className:t,...n}){return o.jsx(B,{"data-slot":"checkbox",className:L("peer border-input data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...n,children:o.jsx(M,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:o.jsx(U,{className:"size-3.5"})})})}export{re as C};
