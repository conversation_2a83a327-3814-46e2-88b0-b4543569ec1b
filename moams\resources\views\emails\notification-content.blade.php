{{-- General notification content template --}}

{{-- Badge (optional) --}}
@isset($badge)
    <div class="{{ $badge['type'] === 'success' ? 'success-badge' : 'warning-badge' }}">
        {{ $badge['text'] }}
    </div>
@endisset

{{-- Greeting --}}
<div class="greeting">{{ $greeting ?? 'Hello!' }}</div>

{{-- Main message --}}
<div class="message">
    {{ $message }}
</div>

{{-- Details cards (multiple cards supported) --}}
@isset($details)
    @foreach($details as $detail)
        <div class="details-card">
            <div class="details-title">{{ $detail['icon'] ?? '' }} {{ $detail['title'] }}</div>
            @foreach($detail['items'] as $item)
                <div class="detail-row">
                    <span class="detail-label">{{ $item['label'] }}:</span>
                    <span class="detail-value">{{ $item['value'] }}</span>
                </div>
            @endforeach
        </div>
    @endforeach
@endisset

{{-- Action button (optional) --}}
@isset($action)
    <div style="text-align: center; margin: 30px 0;">
        <a href="{{ $action['url'] }}" class="action-button">
            {{ $action['text'] }}
        </a>
    </div>
@endisset

{{-- Additional messages (optional) --}}
@isset($additionalMessages)
    @foreach($additionalMessages as $additionalMessage)
        @if(isset($additionalMessage['type']) && $additionalMessage['type'] === 'divider')
            <div class="divider"></div>
        @endif
        @if(isset($additionalMessage['content']))
            <div class="message">
                @if(isset($additionalMessage['title']))
                    <strong>{{ $additionalMessage['title'] }}</strong><br>
                @endif
                {{ $additionalMessage['content'] }}
            </div>
        @endif
    @endforeach
@endisset
