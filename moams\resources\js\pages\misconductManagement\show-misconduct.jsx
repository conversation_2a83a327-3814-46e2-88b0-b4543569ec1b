import { Head, Link, router } from '@inertiajs/react';
import { ChevronLeft, AlertTriangle, User, Car, FileText, CheckCircle, XCircle, Download, History } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useState } from 'react';
import AppLayout from '@/layouts/app-layout';
import { usePage } from '@inertiajs/react';

export default function ShowMisconduct({ misconduct }) {
    const { userRoles } = usePage().props;
    const [severityReviewOpen, setSeverityReviewOpen] = useState(false);
    const [reviewSeverity, setReviewSeverity] = useState(misconduct.severity);
    const [reviewNotes, setReviewNotes] = useState('');

    // User permissions
    const isClerk = userRoles && userRoles.includes('association clerk');
    const isManager = userRoles && userRoles.includes('association manager');
    const isAdmin = userRoles && userRoles.includes('system admin');
    const isMinibusOwner = userRoles && userRoles.includes('minibus owner');

    const canResolve = isAdmin || isClerk || isManager;

    const getSeverityBadge = (severity) => {
        const colors = {
            low: 'bg-green-100 text-green-800',
            medium: 'bg-orange-100 text-orange-800',
            high: 'bg-red-100 text-red-800'
        };
        return (
            <Badge className={colors[severity] || 'bg-gray-100 text-gray-800'}>
                {severity?.toUpperCase()}
            </Badge>
        );
    };

    const getResolutionBadge = (status) => {
        return (
            <Badge className={status === 'resolved' ? 'bg-green-100 text-green-800' : 'bg-orange-100 text-orange-800'}>
                {status === 'resolved' ? 'RESOLVED' : 'UNRESOLVED'}
            </Badge>
        );
    };

    const getTrustScoreBadge = (score) => {
        const color = score >= 80 ? 'bg-green-100 text-green-800' :
            score >= 60 ? 'bg-orange-100 text-orange-800' :
                'bg-red-100 text-red-800';
        return <Badge className={color}>{score}%</Badge>;
    };

    const handleResolutionToggle = async () => {
        const newStatus = misconduct.resolution_status === 'resolved' ? 'unresolved' : 'resolved';

        try {
            await router.patch(`/misconducts/${misconduct.id}/resolution`, {
                resolution_status: newStatus,
                resolution_notes: newStatus === 'resolved' ? 'Marked as resolved via details page' : null,
            });
        } catch (error) {
            console.error('Error updating resolution status:', error);
        }
    };

    const handleSeverityReview = async () => {
        try {
            await router.patch(`/misconducts/${misconduct.id}/severity`, {
                severity: reviewSeverity,
                severity_notes: reviewNotes,
            });
            setSeverityReviewOpen(false);
            setReviewNotes('');
        } catch (error) {
            console.error('Error updating severity:', error);
        }
    };

    const breadcrumbs = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Misconduct Management', href: '/misconducts' },
        { title: 'Misconduct Details', href: `/misconducts/${misconduct.id}` },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Misconduct: ${misconduct.name}`} />

            <div className="container mx-auto px-4 py-8 max-w-4xl">
                {/* Header */}
                <div className="flex flex-col sm:flex-row sm:items-center gap-4 mb-6">
                    <div className="flex-1 flex items-center gap-2">
                        <Link
                            href="/misconducts"
                            className="flex items-center px-3 py-1 rounded hover:bg-gray-100 border border-gray-200"
                        >
                            <ChevronLeft className="h-4 w-4 mr-1" /> Back
                        </Link>
                        <div>
                            <p className="text-gray-600">View and manage misconduct information</p>
                        </div>
                    </div>
                    <div className="flex gap-2">
                        {/* View Misconduct History - Available to all users */}
                        <Button
                            variant="outline"
                            onClick={() => router.visit(`/drivers/${misconduct.offender.id}/misconducts`)}
                        >
                            <History className="h-4 w-4 mr-2" />
                            View Misconduct History
                        </Button>


                        {canResolve && (
                            <Button
                                variant={misconduct.resolution_status === 'resolved' ? 'outline' : 'default'}
                                onClick={handleResolutionToggle}
                                className={misconduct.resolution_status === 'resolved'
                                    ? 'text-orange-700 border-orange-300 hover:bg-orange-50'
                                    : 'bg-green-600 text-white hover:bg-green-700'
                                }
                            >
                                {misconduct.resolution_status === 'resolved' ? (
                                    <>
                                        <XCircle className="h-4 w-4 mr-2" />
                                        Mark Unresolved
                                    </>
                                ) : (
                                    <>
                                        <CheckCircle className="h-4 w-4 mr-2" />
                                        Mark Resolved
                                    </>
                                )}
                            </Button>
                        )}

                        {(userRoles.includes('association clerk') || userRoles.includes('system admin')) && (
                            <Button
                                variant="outline"
                                onClick={() => setSeverityReviewOpen(!severityReviewOpen)}
                                className="text-blue-700 border-blue-300 hover:bg-blue-50"
                            >
                                <AlertTriangle className="h-4 w-4 mr-2" />
                                Review Severity
                            </Button>
                        )}
                    </div>
                </div>

                {/* Severity Review Form */}
                {severityReviewOpen && (
                    <Card className="mb-6 border-blue-200 bg-blue-50">
                        <CardHeader>
                            <CardTitle className="text-lg text-blue-800">Review Severity Level</CardTitle>
                            <p className="text-sm text-blue-600">
                                As an association clerk, you can review and adjust the severity level assigned by the minibus owner.
                            </p>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <label className="text-sm font-medium text-gray-700 mb-2 block">
                                    Current Severity: <span className="font-bold text-blue-600">{misconduct.severity?.toUpperCase()}</span>
                                    {misconduct.original_severity && misconduct.original_severity !== misconduct.severity && (
                                        <span className="text-sm text-gray-500 ml-2">
                                            (Originally: {misconduct.original_severity?.toUpperCase()})
                                        </span>
                                    )}
                                </label>
                                <Select value={reviewSeverity} onValueChange={setReviewSeverity}>
                                    <SelectTrigger className="w-full">
                                        <SelectValue placeholder="Select severity level" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="low">
                                            <div className="flex flex-col">
                                                <span className="font-medium">Low (5 points deducted)</span>
                                            </div>
                                        </SelectItem>
                                        <SelectItem value="medium">
                                            <div className="flex flex-col">
                                                <span className="font-medium">Medium (10 points deducted)</span>
                                            </div>
                                        </SelectItem>
                                        <SelectItem value="high">
                                            <div className="flex flex-col">
                                                <span className="font-medium">High (20 points deducted)</span>
                                            </div>
                                        </SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>

                            <div>
                                <label className="text-sm font-medium text-gray-700 mb-2 block">
                                    Review Notes (Optional)
                                </label>
                                <Textarea
                                    value={reviewNotes}
                                    onChange={(e) => setReviewNotes(e.target.value)}
                                    placeholder="Explain the reason for severity adjustment..."
                                    className="min-h-[80px]"
                                />
                            </div>

                            <div className="flex gap-3">
                                <Button onClick={handleSeverityReview} className="bg-blue-600 hover:bg-blue-700">
                                    Update Severity
                                </Button>
                                <Button
                                    variant="outline"
                                    onClick={() => {
                                        setSeverityReviewOpen(false);
                                        setReviewSeverity(misconduct.severity);
                                        setReviewNotes('');
                                    }}
                                >
                                    Cancel
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Top Metrics Bar */}
                <div className="bg-white border-b-2 border-gray-200 p-6 mb-8">
                    <div className="flex flex-wrap justify-between items-center gap-6">
                        <div className="flex items-center gap-3">
                            <AlertTriangle className="h-6 w-6 text-red-500" />
                            <div>
                                <p className="text-xs text-gray-500 uppercase tracking-wide">Severity</p>
                                {getSeverityBadge(misconduct.severity)}
                            </div>
                        </div>

                        <div className="flex items-center gap-3">
                            <div className="h-6 w-6 bg-orange-100 rounded-full flex items-center justify-center">
                                <span className="text-orange-600 font-bold text-xs">-{misconduct.points_deducted}</span>
                            </div>
                            <div>
                                <p className="text-xs text-gray-500 uppercase tracking-wide">Points Deducted</p>
                                <p className="text-lg font-bold text-orange-600">{misconduct.points_deducted}</p>
                            </div>
                        </div>

                        <div className="flex items-center gap-3">
                            {misconduct.resolution_status === 'resolved' ? (
                                <CheckCircle className="h-6 w-6 text-green-500" />
                            ) : (
                                <XCircle className="h-6 w-6 text-orange-500" />
                            )}
                            <div>
                                <p className="text-xs text-gray-500 uppercase tracking-wide">Status</p>
                                {getResolutionBadge(misconduct.resolution_status)}
                            </div>
                        </div>

                        <div className="flex items-center gap-3">
                            <User className="h-6 w-6 text-green-500" />
                            <div>
                                <p className="text-xs text-gray-500 uppercase tracking-wide">Trust Score</p>
                                <p className="text-sm font-medium">{getTrustScoreBadge(misconduct.offender?.trust_score || 0)}</p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Misconduct Details - Full Width Section */}
                <div className="bg-blue-50 p-6 rounded-lg mb-6">
                    <div className="flex items-center gap-2 mb-6">
                        <FileText className="h-6 w-6 text-blue-600" />
                        <h3 className="text-xl font-semibold text-blue-900">Misconduct Details</h3>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <p className="text-sm text-blue-600 uppercase tracking-wide font-medium mb-2">Misconduct Type</p>
                            <p className="text-lg font-semibold text-blue-900">{misconduct.name}</p>
                        </div>
                        <div>
                            <p className="text-sm text-blue-600 uppercase tracking-wide font-medium mb-2">Full Offense Date</p>
                            <p className="text-lg text-blue-800 font-medium">{new Date(misconduct.offense_date).toLocaleDateString('en-US', {
                                weekday: 'long',
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric'
                            })}</p>
                        </div>
                    </div>

                    <div className="mb-6">
                        <p className="text-sm text-blue-600 uppercase tracking-wide font-medium mb-3">Description</p>
                        <div className="bg-white p-4 rounded-lg border border-blue-200">
                            <p className="text-blue-900 leading-relaxed">
                                {misconduct.description || 'No description provided.'}
                            </p>
                        </div>
                    </div>

                    {/* Evidence */}
                    {misconduct.evidence_file && (
                        <div>
                            <p className="text-sm text-blue-600 uppercase tracking-wide font-medium mb-3">Evidence</p>
                            <div className="bg-white p-4 rounded-lg border border-blue-200">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-3">
                                        <FileText className="h-6 w-6 text-blue-600" />
                                        <div>
                                            <p className="font-medium text-blue-900">
                                                {misconduct.evidence_file.split('/').pop()}
                                            </p>
                                            <p className="text-sm text-blue-600">Evidence file</p>
                                        </div>
                                    </div>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => window.open(`/storage/${misconduct.evidence_file}`, '_blank')}
                                        className="text-blue-600 border-blue-300 hover:bg-blue-100"
                                    >
                                        <Download className="h-4 w-4 mr-2" />
                                        View Evidence
                                    </Button>
                                </div>
                            </div>
                        </div>
                    )}
                </div>

                {/* Dashboard Panels - Now 3 equal panels */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-1 mb-8">

                    {/* Driver & Vehicle Panel - Hidden for minibus owners */}
                    {!isMinibusOwner && (
                        <div className="bg-purple-50 p-6">
                            <div className="flex items-center gap-2 mb-4">
                                <User className="h-5 w-5 text-purple-600" />
                                <h3 className="font-semibold text-purple-900">Driver & Vehicle</h3>
                            </div>
                            <div className="space-y-4">
                                <div>
                                    <p className="text-xs text-purple-600 uppercase tracking-wide font-medium">Driver Name</p>
                                    <p className="text-sm font-semibold text-purple-900 mt-1">
                                        {misconduct.offender?.first_name} {misconduct.offender?.last_name}
                                    </p>
                                </div>
                                <div>
                                    <p className="text-xs text-purple-600 uppercase tracking-wide font-medium">Phone Number</p>
                                    <p className="text-sm text-purple-800 mt-1">{misconduct.offender?.phone_number}</p>
                                </div>
                                <div>
                                    <p className="text-xs text-purple-600 uppercase tracking-wide font-medium">Trust Score</p>
                                    <div className="mt-1">
                                        {getTrustScoreBadge(misconduct.offender?.trust_score)}
                                    </div>
                                </div>

                                {/* Minibus info in same panel */}
                                {misconduct.minibus && (
                                    <div className="pt-4 border-t border-purple-200">
                                        <div className="flex items-center gap-2 mb-3">
                                            <Car className="h-4 w-4 text-purple-600" />
                                            <p className="text-xs text-purple-600 uppercase tracking-wide font-medium">Vehicle Information</p>
                                        </div>
                                        <div className="space-y-3">
                                            <div>
                                                <p className="text-xs text-purple-500 uppercase tracking-wide">Number Plate</p>
                                                <p className="text-lg font-bold text-purple-900 mt-1">{misconduct.minibus?.number_plate}</p>
                                            </div>
                                            <div>
                                                <p className="text-xs text-purple-500 uppercase tracking-wide">Route</p>
                                                <p className="text-sm text-purple-800 mt-1">{misconduct.minibus?.route?.route_name || 'No route assigned'}</p>
                                            </div>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    )}

                    {/* Only show remaining panels for non-minibus owners */}
                    {!isMinibusOwner && (
                        <>
                            {/* Status & Resolution Panel */}
                            <div className="bg-green-50 p-6">
                                <div className="flex items-center gap-2 mb-4">
                                    {misconduct.resolution_status === 'resolved' ? (
                                        <CheckCircle className="h-5 w-5 text-green-600" />
                                    ) : (
                                        <XCircle className="h-5 w-5 text-orange-600" />
                                    )}
                                    <h3 className="font-semibold text-green-900">Status & Resolution</h3>
                                </div>
                                <div className="space-y-4">
                                    <div>
                                        <p className="text-xs text-green-600 uppercase tracking-wide font-medium">Current Status</p>
                                        <div className="mt-1">
                                            {getResolutionBadge(misconduct.resolution_status)}
                                        </div>
                                    </div>

                                    {misconduct.resolution_status === 'resolved' && (
                                        <>
                                            <div>
                                                <p className="text-xs text-green-600 uppercase tracking-wide font-medium">Resolved By</p>
                                                <p className="text-sm text-green-800 font-semibold mt-1">
                                                    {misconduct.resolved_by ?
                                                        `${misconduct.resolved_by.first_name} ${misconduct.resolved_by.last_name}` :
                                                        'System'
                                                    }
                                                </p>
                                            </div>
                                            <div>
                                                <p className="text-xs text-green-600 uppercase tracking-wide font-medium">Resolved Date</p>
                                                <p className="text-sm text-green-800 mt-1">{new Date(misconduct.resolved_at).toLocaleDateString()}</p>
                                            </div>

                                        </>
                                    )}

                                    {/* Severity Review */}
                                    {misconduct.severity_reviewed_by && (
                                        <div className="pt-4 border-t border-green-200">
                                            <div className="flex items-center gap-2 mb-3">
                                                <AlertTriangle className="h-4 w-4 text-green-600" />
                                                <p className="text-xs text-green-600 uppercase tracking-wide font-medium">Severity Review</p>
                                            </div>
                                            <div className="space-y-3">
                                                <div>
                                                    <p className="text-xs text-green-500 uppercase tracking-wide">Reviewed By</p>
                                                    <p className="text-sm text-green-800 font-semibold mt-1">
                                                        {misconduct.severity_reviewed_by?.first_name} {misconduct.severity_reviewed_by?.last_name}
                                                    </p>
                                                </div>
                                                <div>
                                                    <p className="text-xs text-green-500 uppercase tracking-wide">Review Date</p>
                                                    <p className="text-sm text-green-800 mt-1">{new Date(misconduct.severity_reviewed_at).toLocaleDateString()}</p>
                                                </div>
                                                {misconduct.original_severity && misconduct.original_severity !== misconduct.severity && (
                                                    <div>
                                                        <p className="text-xs text-green-500 uppercase tracking-wide">Severity Change</p>
                                                        <div className="flex items-center gap-2 mt-1">
                                                            <span className="px-2 py-1 bg-red-100 text-red-700 rounded text-xs font-medium">
                                                                {misconduct.original_severity?.toUpperCase()}
                                                            </span>
                                                            <span className="text-green-400">→</span>
                                                            <span className="px-2 py-1 bg-green-100 text-green-700 rounded text-xs font-medium">
                                                                {misconduct.severity?.toUpperCase()}
                                                            </span>
                                                        </div>
                                                    </div>
                                                )}
                                                {misconduct.severity_review_notes && (
                                                    <div>
                                                        <p className="text-xs text-green-500 uppercase tracking-wide">Review Notes</p>
                                                        <div className="bg-white p-3 rounded border border-green-200 mt-1">
                                                            <p className="text-green-900 text-sm leading-relaxed">
                                                                {misconduct.severity_review_notes}
                                                            </p>
                                                        </div>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    )}

                                </div>
                            </div>

                            {/* Report Information Panel */}
                            <div className="bg-orange-50 p-6">
                                <div className="flex items-center gap-2 mb-4">
                                    <FileText className="h-5 w-5 text-orange-600" />
                                    <h3 className="font-semibold text-orange-900">Report Information</h3>
                                </div>
                                <div className="space-y-4">
                                    <div>
                                        <p className="text-xs text-orange-600 uppercase tracking-wide font-medium">Reported By</p>
                                        <p className="text-sm font-semibold text-orange-900 mt-1">
                                            {misconduct.reported_by?.first_name} {misconduct.reported_by?.last_name}
                                        </p>
                                    </div>
                                    <div>
                                        <p className="text-xs text-orange-600 uppercase tracking-wide font-medium">Report Date</p>
                                        <p className="text-sm text-orange-800 mt-1">{new Date(misconduct.created_at).toLocaleDateString('en-US', {
                                            weekday: 'long',
                                            year: 'numeric',
                                            month: 'long',
                                            day: 'numeric'
                                        })}</p>
                                    </div>
                                    <div>
                                        <p className="text-xs text-orange-600 uppercase tracking-wide font-medium">Report Time</p>
                                        <p className="text-sm text-orange-800 mt-1">{new Date(misconduct.created_at).toLocaleTimeString('en-US', {
                                            hour: '2-digit',
                                            minute: '2-digit',
                                            hour12: true
                                        })}</p>
                                    </div>
                                </div>
                            </div>
                        </>
                    )}
                </div>
            </div>
        </AppLayout>
    );
}
