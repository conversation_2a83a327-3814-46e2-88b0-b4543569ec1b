<?php

namespace App\Http\Controllers;

use App\Models\RoleHasPermission;
use App\Http\Requests\StoreRoleHasPermissionRequest;
use App\Http\Requests\UpdateRoleHasPermissionRequest;

class RoleHasPermissionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreRoleHasPermissionRequest $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(RoleHasPermission $roleHasPermission)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(RoleHasPermission $roleHasPermission)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateRoleHasPermissionRequest $request, RoleHasPermission $roleHasPermission)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(RoleHasPermission $roleHasPermission)
    {
        //
    }
}
