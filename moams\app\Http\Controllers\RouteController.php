<?php

namespace App\Http\Controllers;

use App\Models\VehicleRoute;
use App\Models\Minibus;
use App\Http\Requests\StoreRouteRequest;
use App\Http\Requests\UpdateRouteRequest;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Barryvdh\DomPDF\Facade\Pdf;


class RouteController extends Controller
{
    // TODO: Add middleware to restrict to Clerk role

    public function index(Request $request)
    {
        $query = VehicleRoute::withCount('minibuses');

        // Handle search
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            });
        }

        // Handle status filter
        if ($request->has('status') && $request->status !== 'all') {
            if ($request->status === 'active') {
                $query->where('status', 'active');
            } elseif ($request->status === 'inactive') {
                $query->where('status', 'inactive');
            }
        }

        $routes = $query->orderBy($request->get('sort', 'name'), $request->get('direction', 'asc'))
            ->paginate(10);

        return Inertia::render('RouteManagement/index-route', [
            'routes' => $routes,
        ]);
    }

    public function store(StoreRouteRequest $request)
    {
        $route = VehicleRoute::create($request->validated());
        return redirect()->back()->with('success', 'Route created successfully.');
    }

    public function update(UpdateRouteRequest $request, VehicleRoute $route)
    {
        $route->update($request->validated());
        return redirect()->back()->with('success', 'Route updated successfully.');
    }

    public function destroy(VehicleRoute $route)
    {
        if ($route->minibuses()->count() > 0) {
            return redirect()->back()->with('warning', 'Cannot delete route: it is assigned to active minibuses.');
        }
        $route->delete();
        return redirect()->back()->with('success', 'Route deleted successfully.');
    }

    public function deactivate(VehicleRoute $route)
    {
        if ($route->minibuses()->count() > 0) {
            return redirect()->back()->with('warning', 'Warning: This route is assigned to active minibuses.');
        }
        $route->update(['status' => 'inactive']);
        return redirect()->back()->with('success', 'Route deactivated successfully.');
    }

    public function activate(VehicleRoute $route)
    {
        $route->update(['status' => 'active']);
        return redirect()->back()->with('success', 'Route activated successfully.');
    }

    public function export(Request $request)
    {
        $routes = VehicleRoute::withCount('minibuses')
            ->orderBy('minibuses_count', 'desc')
            ->get();

        $format = $request->query('format', 'pdf');

        if ($format === 'pdf') {
            // Calculate statistics
            $totalRoutes = $routes->count();
            $activeRoutes = $routes->where('status', 'active')->count();
            $inactiveRoutes = $routes->where('status', 'inactive')->count();
            $totalMinibuses = $routes->sum('minibuses_count');

            // Skip logo processing completely to avoid GD dependency
            $logoBase64 = null;

            // Prepare data for PDF
            $data = [
                'routes' => $routes,
                'reportDate' => now()->format('F j, Y'),
                'reportTime' => now()->format('g:i A'),
                'generatedBy' => auth()->user()->first_name . ' ' . auth()->user()->last_name,
                'totalRoutes' => $totalRoutes,
                'activeRoutes' => $activeRoutes,
                'inactiveRoutes' => $inactiveRoutes,
                'totalMinibuses' => $totalMinibuses,
                'logoBase64' => $logoBase64,
            ];

            // Generate PDF
            $pdf = Pdf::loadView('reports.routes-report', $data);
            $pdf->setPaper('A4', 'portrait');

            return $pdf->download('routes_report_' . date('Y-m-d') . '.pdf');
        }

        abort(400, 'Unsupported format. Only PDF export is supported.');
    }

    // Optionally, add export functionality here

    // API endpoint to fetch all vehicle routes as JSON
    public function apiIndex()
    {
        $routes = VehicleRoute::orderBy('name')->get(['id', 'name', 'status']);
        return response()->json($routes);
    }
}