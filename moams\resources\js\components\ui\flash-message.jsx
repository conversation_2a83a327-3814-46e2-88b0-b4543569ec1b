import React, { useEffect, useState } from "react";
import { cn } from "@/lib/utils";

export default function FlashMessage({
    message,
    type = "success", // "success", "error", "info", "warning"
    duration = 5000,
    className = "",
    onClose,
}) {
    if (!message || typeof message !== 'string' || message.trim() === '') return null;
    const [show, setShow] = useState(!!message);

    useEffect(() => {
        if (message) {
            setShow(true);
            if (duration > 0) {
                const timer = setTimeout(() => {
                    setShow(false);
                    if (onClose) onClose();
                }, duration);
                return () => clearTimeout(timer);
            }
        }
    }, [message, duration, onClose]);

    if (!show || !message) return null;

    const typeStyles = {
        success: "bg-green-200 text-green-900 border-green-400",
        error: "bg-red-200 text-red-900 border-red-400",
        info: "bg-blue-200 text-blue-900 border-blue-400",
        warning: "bg-yellow-200 text-yellow-900 border-yellow-400",
    };

    let bgColor = 'bg-green-100 text-green-800';
    if (type === 'error') bgColor = 'bg-red-100 text-red-800';
    else if (type === 'info') bgColor = 'bg-blue-100 text-blue-800';

    return (
        <div
            className={cn(
                // Full width, top center, with max-w-2xl for large screens
                "fixed top-0 left-0 w-full flex justify-center z-50 transition-all duration-300",
                show ? "opacity-100 translate-y-0" : "opacity-0 -translate-y-2 pointer-events-none",
                className
            )}
        >
            <div
                className={cn(
                    "mt-6 max-w-2xl w-full flex items-center text-center gap-3 px-5 py-3 rounded-lg border shadow-2xl font-semibold text-base text-center",
                    typeStyles[type] || typeStyles.success
                )}
                style={{ minWidth: 220 }}
            >
                {message}
            </div>
        </div>
    );
} 