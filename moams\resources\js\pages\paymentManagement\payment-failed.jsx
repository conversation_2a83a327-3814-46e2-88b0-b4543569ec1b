import React from 'react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { XCircle, CreditCard, RefreshCw, AlertTriangle } from 'lucide-react';
import { Link } from '@inertiajs/react';
import { route } from 'ziggy-js';

export default function PaymentFailed({ payment, associationClerk }) {
    // Define breadcrumbs for payment failed
    const breadcrumbs = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'My Membership', href: '/my-membership' },
        { title: 'Payment Failed', href: '#' },
    ];

    return (
        <AppLayout title="Payment Failed" breadcrumbs={breadcrumbs}>
            <div className="max-w-2xl mx-auto py-8 px-4">
                {/* Failed Header */}
                <div className="text-center mb-8">
                    <div className="flex justify-center mb-4">
                        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                            <XCircle className="h-10 w-10 text-red-600" />
                        </div>
                    </div>
                    <h1 className="text-3xl font-bold text-red-700 mb-2">
                        Payment Failed
                    </h1>
                    <p className="text-gray-600">
                        We were unable to process your payment
                    </p>
                </div>

                {/* Error Details Card */}
                <Card className="mb-6 border-red-200 bg-red-50">
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2 text-red-800">
                            <AlertTriangle className="h-5 w-5" />
                            Payment Details
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <div className="text-sm text-gray-600">Amount</div>
                                <div className="font-semibold text-red-800">
                                    MWK {parseFloat(payment.amount).toLocaleString()}
                                </div>
                            </div>

                            <div>
                                <div className="text-sm text-gray-600">Fee Type</div>
                                <div className="font-semibold text-red-800 capitalize">
                                    {payment.fee_type}
                                </div>
                            </div>

                            <div>
                                <div className="text-sm text-gray-600">Payment Method</div>
                                <div className="font-semibold text-red-800">
                                    {payment.payment_method === 'ctechpay_card' ? 'Card Payment' : payment.payment_method}
                                </div>
                            </div>

                            <div>
                                <div className="text-sm text-gray-600">Attempted At</div>
                                <div className="font-semibold text-red-800">
                                    {new Date(payment.created_at).toLocaleString()}
                                </div>
                            </div>
                        </div>

                        {/* Error Message */}
                        {payment.ctechpay_error_message && (
                            <div className="pt-4 border-t border-red-200">
                                <div className="text-sm text-gray-600 mb-2">Error Details</div>
                                <div className="bg-red-100 border border-red-200 rounded-lg p-3">
                                    <p className="text-red-800 text-sm">
                                        {payment.ctechpay_error_message}
                                    </p>
                                </div>
                            </div>
                        )}

                        {/* Order Reference if available */}
                        {payment.ctechpay_order_reference && (
                            <div className="pt-4 border-t border-red-200">
                                <div className="text-sm text-gray-600 mb-2">Reference</div>
                                <div className="font-mono text-sm text-red-800">
                                    {payment.ctechpay_order_reference}
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Common Reasons */}
                <Card className="mb-6">
                    <CardHeader>
                        <CardTitle>Common Reasons for Payment Failure</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-3">
                            <div className="flex items-start gap-3">
                                <div className="w-2 h-2 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
                                <div className="text-sm text-gray-700">
                                    <strong>Insufficient Funds:</strong> Your account or card doesn't have enough balance
                                </div>
                            </div>

                            <div className="flex items-start gap-3">
                                <div className="w-2 h-2 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
                                <div className="text-sm text-gray-700">
                                    <strong>Card Declined:</strong> Your bank or card issuer declined the transaction
                                </div>
                            </div>

                            <div className="flex items-start gap-3">
                                <div className="w-2 h-2 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
                                <div className="text-sm text-gray-700">
                                    <strong>Network Issues:</strong> Temporary connectivity problems
                                </div>
                            </div>

                            <div className="flex items-start gap-3">
                                <div className="w-2 h-2 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
                                <div className="text-sm text-gray-700">
                                    <strong>Incorrect Details:</strong> Wrong card number, expiry date, or CVV
                                </div>
                            </div>


                        </div>
                    </CardContent>
                </Card>

                {/* What to Do Next */}
                <Card className="mb-6">
                    <CardHeader>
                        <CardTitle>What to Do Next</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-3">
                            <div className="flex items-start gap-3">
                                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                    <span className="text-blue-600 text-sm font-semibold">1</span>
                                </div>
                                <div>
                                    <div className="font-medium">Check Your Account</div>
                                    <div className="text-sm text-gray-600">
                                        Ensure you have sufficient funds and your payment method is active
                                    </div>
                                </div>
                            </div>

                            <div className="flex items-start gap-3">
                                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                    <span className="text-blue-600 text-sm font-semibold">2</span>
                                </div>
                                <div>
                                    <div className="font-medium">Try Again</div>
                                    <div className="text-sm text-gray-600">
                                        You can retry the payment with the same or different payment method
                                    </div>
                                </div>
                            </div>

                            <div className="flex items-start gap-3">
                                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                    <span className="text-blue-600 text-sm font-semibold">3</span>
                                </div>
                                <div>
                                    <div className="font-medium">Contact Support</div>
                                    <div className="text-sm text-gray-600">
                                        If the problem persists, contact our support team for assistance
                                    </div>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-3 justify-center">
                    <Button asChild variant="outline">
                        <Link href={route('dashboard')}>
                            Return to Dashboard
                        </Link>
                    </Button>

                    <Button asChild className="bg-blue-600 hover:bg-blue-700">
                        <Link href={route('my.membership')}>
                            <RefreshCw className="h-4 w-4 mr-2" />
                            Try Again
                        </Link>
                    </Button>
                </div>

                {/* Support Information */}
                <div className="mt-8 text-center text-sm text-gray-500">
                    {associationClerk ? (
                        <p>
                            Need help? Contact {associationClerk.first_name} {associationClerk.last_name} at{' '}
                            <a href={`mailto:${associationClerk.email}`} className="text-blue-600 hover:underline">
                                {associationClerk.email}
                            </a>{' '}
                            or call {associationClerk.phone_number}
                        </p>
                    ) : (
                        <p>
                            Need help? Contact MOAM support for assistance with your payment.
                        </p>
                    )}
                </div>
            </div>
        </AppLayout>
    );
}
