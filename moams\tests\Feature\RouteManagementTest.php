<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\VehicleRoute;
use App\Models\Minibus;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Spatie\Permission\Models\Role;

class RouteManagementTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles
        Role::create(['name' => 'association clerk']);
        Role::create(['name' => 'association manager']);
        Role::create(['name' => 'minibus owner']);
    }

    /** @test */
    public function association_clerk_can_access_route_management()
    {
        $user = User::factory()->create();
        $user->assignRole('association clerk');

        $response = $this->actingAs($user)->get('/routes');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('RouteManagement/index-route'));
    }

    /** @test */
    public function association_manager_can_access_route_management()
    {
        $user = User::factory()->create();
        $user->assignRole('association manager');

        $response = $this->actingAs($user)->get('/routes');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('RouteManagement/index-route'));
    }

    /** @test */
    public function minibus_owner_cannot_access_route_management()
    {
        $user = User::factory()->create();
        $user->assignRole('minibus owner');

        $response = $this->actingAs($user)->get('/routes');

        $response->assertStatus(403);
    }

    /** @test */
    public function association_clerk_can_create_route()
    {
        $user = User::factory()->create();
        $user->assignRole('association clerk');

        $routeData = [
            'name' => 'Area 25 - City Centre',
            'status' => 'active'
        ];

        $response = $this->actingAs($user)->post('/routes', $routeData);

        $response->assertRedirect();
        $this->assertDatabaseHas('vehicle_routes', $routeData);
    }

    /** @test */
    public function route_name_must_be_unique()
    {
        $user = User::factory()->create();
        $user->assignRole('association clerk');

        VehicleRoute::create([
            'name' => 'Area 25 - City Centre',
            'status' => 'active'
        ]);

        $routeData = [
            'name' => 'Area 25 - City Centre',
            'status' => 'active'
        ];

        $response = $this->actingAs($user)->post('/routes', $routeData);

        $response->assertSessionHasErrors(['name']);
    }

    /** @test */
    public function association_clerk_can_update_route()
    {
        $user = User::factory()->create();
        $user->assignRole('association clerk');

        $route = VehicleRoute::create([
            'name' => 'Area 25 - City Centre',
            'status' => 'active'
        ]);

        $updateData = [
            'name' => 'Area 25 - City Centre Updated',
            'status' => 'inactive'
        ];

        $response = $this->actingAs($user)->post("/routes/{$route->id}/update", $updateData);

        $response->assertRedirect();
        $this->assertDatabaseHas('vehicle_routes', [
            'id' => $route->id,
            'name' => 'Area 25 - City Centre Updated',
            'status' => 'inactive'
        ]);
    }

    /** @test */
    public function association_manager_can_deactivate_route()
    {
        $user = User::factory()->create();
        $user->assignRole('association manager');

        $route = VehicleRoute::create([
            'name' => 'Area 25 - City Centre',
            'status' => 'active'
        ]);

        $response = $this->actingAs($user)->post("/routes/{$route->id}/deactivate");

        $response->assertRedirect();
        $this->assertDatabaseHas('vehicle_routes', [
            'id' => $route->id,
            'status' => 'inactive'
        ]);
    }

    /** @test */
    public function association_manager_can_activate_route()
    {
        $user = User::factory()->create();
        $user->assignRole('association manager');

        $route = VehicleRoute::create([
            'name' => 'Area 25 - City Centre',
            'status' => 'inactive'
        ]);

        $response = $this->actingAs($user)->post("/routes/{$route->id}/activate");

        $response->assertRedirect();
        $this->assertDatabaseHas('vehicle_routes', [
            'id' => $route->id,
            'status' => 'active'
        ]);
    }

    /** @test */
    public function cannot_delete_route_with_assigned_minibuses()
    {
        $user = User::factory()->create();
        $user->assignRole('association clerk');

        $route = VehicleRoute::create([
            'name' => 'Area 25 - City Centre',
            'status' => 'active'
        ]);

        // Create a minibus assigned to this route
        Minibus::factory()->create(['route_id' => $route->id]);

        $response = $this->actingAs($user)->post("/routes/{$route->id}/delete");

        $response->assertRedirect();
        $response->assertSessionHas('warning');
        $this->assertDatabaseHas('vehicle_routes', ['id' => $route->id]);
    }

    /** @test */
    public function can_delete_route_without_assigned_minibuses()
    {
        $user = User::factory()->create();
        $user->assignRole('association clerk');

        $route = VehicleRoute::create([
            'name' => 'Area 25 - City Centre',
            'status' => 'active'
        ]);

        $response = $this->actingAs($user)->post("/routes/{$route->id}/delete");

        $response->assertRedirect();
        $this->assertDatabaseMissing('vehicle_routes', ['id' => $route->id]);
    }

    /** @test */
    public function can_export_routes_as_csv()
    {
        $user = User::factory()->create();
        $user->assignRole('association clerk');

        VehicleRoute::create(['name' => 'Route 1', 'status' => 'active']);
        VehicleRoute::create(['name' => 'Route 2', 'status' => 'inactive']);

        $response = $this->actingAs($user)->get('/routes/export?format=csv');

        $response->assertStatus(200);
        $response->assertHeader('content-type', 'text/csv; charset=UTF-8');
    }

    /** @test */
    public function can_export_routes_as_pdf()
    {
        $user = User::factory()->create();
        $user->assignRole('association clerk');

        VehicleRoute::create(['name' => 'Route 1', 'status' => 'active']);
        VehicleRoute::create(['name' => 'Route 2', 'status' => 'inactive']);

        $response = $this->actingAs($user)->get('/routes/export?format=pdf');

        $response->assertStatus(200);
        $response->assertHeader('content-type', 'application/pdf');
    }

    /** @test */
    public function routes_are_displayed_with_usage_count()
    {
        $user = User::factory()->create();
        $user->assignRole('association clerk');

        $route = VehicleRoute::create([
            'name' => 'Area 25 - City Centre',
            'status' => 'active'
        ]);

        // Create minibuses assigned to this route
        Minibus::factory()->count(3)->create(['route_id' => $route->id]);

        $response = $this->actingAs($user)->get('/routes');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => 
            $page->has('routes.0.minibuses_count')
                 ->where('routes.0.minibuses_count', 3)
        );
    }
}
