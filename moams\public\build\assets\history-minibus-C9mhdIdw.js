import{r as D,j as e,B as n,C as L,a as T,b as A,c as E}from"./app-DL-qYY5V.js";import{n as H}from"./navigation-DAA2N51J.js";import{A as O,B as i}from"./app-layout-YqstQnqE.js";import{A as P}from"./arrow-left-DCW23wrL.js";import{B as U}from"./bus-CKTBKI0T.js";import{a as G}from"./users-DNGXY-sJ.js";import"./index-1tPPImJd.js";import"./index-BTzg1GwG.js";import"./app-logo-icon-BnXlkpcX.js";import"./dollar-sign-Cx0-nQIX.js";import"./clipboard-list-CxRumT8v.js";function W({minibus:a,history:r,userRole:C}){const[t,l]=D.useState(2),m=r.slice(0,t),k=m.length<r.length,d=[{title:"Dashboard",href:"/dashboard"},{title:C==="minibus owner"?"My Minibuses":"Minibus Management",href:"/minibuses"},{title:a.number_plate,href:`/minibuses/${a.id}`},{title:"Ownership History",href:"#"}],c=s=>s?new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}):"N/A",B=s=>{switch(s){case"internal":return e.jsx(i,{variant:"default",className:"bg-blue-600",children:"Internal"});case"external":return e.jsx(i,{variant:"destructive",children:"External"});default:return e.jsx(i,{variant:"outline",children:"Unknown"})}};return e.jsx(O,{breadcrumbs:d,children:e.jsxs("div",{className:"p-2 sm:p-4 md:p-8 ",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row gap-2 sm:gap-4 items-center sm:items-center sm:justify-between mb-4 sm:mb-6",children:[e.jsxs(n,{variant:"outline",onClick:()=>H(d),className:"w-full sm:w-auto",children:[e.jsx(P,{className:"h-4 w-4 mr-2"}),"Back"]}),e.jsx("h1",{className:"text-lg sm:text-xl md:text-2xl font-bold text-center sm:text-left",children:"Ownership History"})]}),e.jsxs("div",{className:"w-full max-w-3xl mx-auto",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("h3",{className:"text-base sm:text-lg md:text-xl font-semibold mb-5 text-center flex items-center justify-center",children:[e.jsx(U,{className:"inline-block text-gray-500 mr-2 h-5 w-5 align-text-bottom"}),a.number_plate," Ownership History"]})}),r.length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(G,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),e.jsx("div",{className:"text-lg font-medium text-gray-700 mb-1",children:"No ownership history found."}),e.jsx("div",{className:"text-gray-500 text-sm",children:"Ownership records will appear here as the minibus changes owners."})]}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"space-y-6",children:m.map((s,M)=>{var o,x,h,p,j,u,f,N,b,g,w,v,_,y;return e.jsxs(L,{className:"shadow-md",children:[e.jsx(T,{className:"pb-3",children:e.jsxs(A,{className:"flex items-center justify-between text-base",children:[e.jsxs("span",{className:"flex items-center gap-2",children:[B(s.transfer_type),e.jsxs("span",{children:[s.transfer_type==="internal"&&"Internal Transfer",s.transfer_type==="external"&&"External Transfer"]})]}),e.jsx("span",{className:"text-sm text-gray-500",children:c(s.created_at)})]})}),e.jsxs(E,{className:"pt-0 space-y-3",children:[e.jsxs("div",{className:"border border-red-200 rounded p-3 bg-red-50",children:[e.jsx("label",{className:"text-sm font-semibold text-red-700 block mb-1",children:"Previous Owner"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-2 text-sm",children:[e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"Name:"})," ",(o=s.previous_owner)==null?void 0:o.first_name," ",(x=s.previous_owner)==null?void 0:x.last_name]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"Email:"})," ",(h=s.previous_owner)==null?void 0:h.email]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"Phone:"})," ",(p=s.previous_owner)==null?void 0:p.phone_number]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"District:"})," ",(j=s.previous_owner)==null?void 0:j.district]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"Village:"})," ",(u=s.previous_owner)==null?void 0:u.village]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"Gender:"})," ",(f=s.previous_owner)==null?void 0:f.gender]})]})]}),e.jsxs("div",{className:"border border-green-200 rounded p-3 bg-green-50",children:[e.jsx("label",{className:"text-sm font-semibold text-green-700 block mb-1",children:"New Owner"}),s.transfer_type==="external"?e.jsx("span",{className:"text-gray-400",children:"Non-member"}):e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-2 text-sm",children:[e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"Name:"})," ",(N=s.new_owner)==null?void 0:N.first_name," ",(b=s.new_owner)==null?void 0:b.last_name]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"Email:"})," ",(g=s.new_owner)==null?void 0:g.email]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"Phone:"})," ",(w=s.new_owner)==null?void 0:w.phone_number]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"District:"})," ",(v=s.new_owner)==null?void 0:v.district]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"Village:"})," ",(_=s.new_owner)==null?void 0:_.village]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"Gender:"})," ",(y=s.new_owner)==null?void 0:y.gender]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Transferred At"}),e.jsx("p",{className:"text-sm",children:c(s.created_at)})]})]})]},M)})}),e.jsxs("div",{className:"flex justify-center gap-4 mt-6",children:[k&&e.jsx(n,{variant:"outline",onClick:()=>l(s=>s+2),children:"Load More"}),t>2&&e.jsx(n,{variant:"outline",onClick:()=>l(s=>Math.max(2,s-2)),children:"Load Less"})]})]})]})]})})}export{W as default};
