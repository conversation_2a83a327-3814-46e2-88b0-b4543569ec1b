<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

/**
 * @property int $id
 * @property string $fee_type
 * @property float $amount
 * @property string|null $description
 * @property \Carbon\Carbon $effective_from
 * @property \Carbon\Carbon|null $effective_until
 * @property bool $is_active
 * @property int $created_by
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property-read \App\Models\User $createdBy
 */
class FeeSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'fee_type',
        'amount',
        'description',
        'effective_from',
        'effective_until',
        'is_active',
        'created_by',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'effective_from' => 'date',
        'effective_until' => 'date',
        'is_active' => 'boolean',
    ];

    /**
     * Get the user who created this fee setting
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the current active fee for a specific fee type
     */
    public static function getCurrentFee(string $feeType): ?self
    {
        $today = Carbon::today();

        return self::where('fee_type', $feeType)
            ->where('is_active', true)
            ->where('effective_from', '<=', $today)
            ->where(function ($query) use ($today) {
                $query->whereNull('effective_until')
                    ->orWhere('effective_until', '>=', $today);
            })
            ->orderBy('effective_from', 'desc')
            ->first();
    }

    /**
     * Get the current fee amount for a specific fee type
     */
    public static function getCurrentFeeAmount(string $feeType): float
    {
        $fee = self::getCurrentFee($feeType);
        return $fee ? (float) $fee->amount : 0.00;
    }

    /**
     * Get fee history for a specific fee type
     */
    public static function getFeeHistory(string $feeType): \Illuminate\Database\Eloquent\Collection
    {
        return self::where('fee_type', $feeType)
            ->with('createdBy')
            ->orderBy('effective_from', 'desc')
            ->get();
    }

    /**
     * Create a new fee setting and deactivate previous ones
     */
    public static function createNewFee(array $data): self
    {
        // Deactivate previous active fees of the same type
        self::where('fee_type', $data['fee_type'])
            ->where('is_active', true)
            ->update([
                'effective_until' => Carbon::parse($data['effective_from'])->subDay(),
                'is_active' => false
            ]);

        // Create new fee setting
        return self::create($data);
    }

    /**
     * Scope to get active fees
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get fees by type
     */
    public function scopeByType($query, string $feeType)
    {
        return $query->where('fee_type', $feeType);
    }

    /**
     * Get formatted amount
     */
    public function getFormattedAmountAttribute(): string
    {
        return 'MK ' . number_format((float) $this->amount, 2);
    }

    /**
     * Get status text
     */
    public function getStatusTextAttribute(): string
    {
        if (!$this->is_active) {
            return 'Inactive';
        }

        $today = Carbon::today();

        if ($this->effective_from > $today) {
            return 'Future';
        }

        if ($this->effective_until && $this->effective_until < $today) {
            return 'Expired';
        }

        return 'Active';
    }
}
