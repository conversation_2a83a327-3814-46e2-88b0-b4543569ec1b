import{u as c,j as s,H as u,B as f}from"./app-DL-qYY5V.js";import{I as w}from"./input-error-C6jcuIY6.js";import{I as h}from"./input-Dm4SEXxy.js";import{L as x}from"./label-e3QxUH-L.js";import{A as j}from"./auth-layout-TDtItk3e.js";import{L as g}from"./loader-circle-D3J3XKS7.js";import"./index-BTzg1GwG.js";import"./app-logo-icon-BnXlkpcX.js";function P(){const{data:a,setData:e,post:t,processing:o,errors:i,reset:n}=c({password:""}),d=r=>{r.preventDefault(),t(route("password.confirm"),{onFinish:()=>n("password")})},p=r=>{const{id:m,value:l}=r.target;e(m,l)};return s.jsxs(j,{title:"Confirm your password",description:"This is a secure area of the application. Please confirm your password before continuing.",children:[s.jsx(u,{title:"Confirm password"}),s.jsx("form",{onSubmit:d,children:s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"grid gap-2",children:[s.jsx(x,{htmlFor:"password",children:"Password"}),s.jsx(h,{id:"password",type:"password",name:"password",placeholder:"Password",autoComplete:"current-password",value:a.password,autoFocus:!0,onChange:p}),s.jsx(w,{message:i.password})]}),s.jsx("div",{className:"flex items-center",children:s.jsxs(f,{className:"w-full bg-blue-400 hover:bg-blue-500",disabled:o,children:[o&&s.jsx(g,{className:"h-4 w-4 animate-spin"}),"Confirm password"]})})]})})]})}export{P as default};
