<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use Inertia\Response;

class AuthenticatedSessionController extends Controller
{
    /**
     * Show the login page.
     */
    public function create(Request $request): Response
    {
        return Inertia::render('auth/login', [
            'canResetPassword' => Route::has('password.request'),
            'status' => $request->session()->get('status'),
        ]);
    }

    /**
     * Handle an incoming authentication request.
     */
    public function store(LoginRequest $request)
    {
        $request->authenticate();

        $request->session()->regenerate();

        $user = auth()->user();
        $intendedUrl = session('url.intended');
        $requiresClerkRole = session('intended.requires_clerk_role', false);

        // Determine the appropriate redirect URL based on user role and intended destination
        $redirectUrl = $this->determineRedirectUrl($user, $intendedUrl, $requiresClerkRole);

        // Clear the intended session data
        session()->forget(['url.intended', 'intended.requires_clerk_role', 'intended.type', 'intended.id']);

        return Inertia::location($redirectUrl);
    }

    /**
     * Determine the appropriate redirect URL based on user role and intended destination
     */
    private function determineRedirectUrl($user, $intendedUrl, $requiresClerkRole)
    {
        // If there's an intended URL and user has the required role, go there
        if ($intendedUrl && $requiresClerkRole) {
            if ($user->hasAnyRole(['association clerk', 'association manager'])) {
                return $intendedUrl;
            } else {
                // User doesn't have required role, redirect to their appropriate dashboard
                session()->flash('info', 'You have been redirected to your dashboard. The requested page is only accessible to association clerks and managers.');
                return $this->getRoleDashboard($user);
            }
        }

        // If there's an intended URL but no special role requirement, go there
        if ($intendedUrl) {
            return $intendedUrl;
        }

        // No intended URL, redirect to role-appropriate dashboard
        return $this->getRoleDashboard($user);
    }

    /**
     * Get the appropriate dashboard URL based on user role
     */
    private function getRoleDashboard($user)
    {
        // All authenticated users go to the main dashboard
        return route('dashboard');
    }

    /**
     * Destroy an authenticated session.
     */
    public function destroy(Request $request): RedirectResponse
    {
        Auth::guard('web')->logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect('/');
    }
}
