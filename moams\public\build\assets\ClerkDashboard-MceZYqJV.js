import{j as e,C as a,a as l,b as i,c,h as b}from"./app-DL-qYY5V.js";import{U as g,a as p}from"./users-DNGXY-sJ.js";import{B as f}from"./bus-CKTBKI0T.js";import{B as u}from"./badge-check-BVA7OvLi.js";import{C as y}from"./clipboard-list-CxRumT8v.js";function R({user:t={},stats:s={},misconducts:o=0,clearanceRequests:N=0,transferRequests:v=0,currentFees:w={}}){var r,d,x,n,m,h,j;return e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 p-4",children:[e.jsxs(a,{className:"shadow-md",children:[e.jsx(l,{children:e.jsxs(i,{className:"flex items-center gap-2",children:[e.jsx(g,{className:"h-5 w-5 text-blue-600"})," Personal Details"]})}),e.jsxs(c,{children:[e.jsx("p",{className:"text-gray-500 mb-2",children:"Your basic profile information."}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("span",{className:"font-semibold",children:"First Name:"})," ",t.first_name||"N/A"]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-semibold",children:"Last Name:"})," ",t.last_name||"N/A"]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-semibold",children:"Gender:"})," ",t.gender||"N/A"]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-semibold",children:"District:"})," ",t.district||"N/A"]})]})]})]}),e.jsxs(a,{className:"shadow-md",children:[e.jsx(l,{children:e.jsxs(i,{className:"flex items-center gap-2",children:[e.jsx(f,{className:"h-5 w-5 text-blue-600"})," Minibus Statistics"]})}),e.jsxs(c,{children:[e.jsx("p",{className:"text-gray-500 mb-2",children:"Overview of all registered minibuses."}),e.jsxs("div",{className:"flex gap-8",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-xl font-bold",children:((r=s.minibuses)==null?void 0:r.all)??0}),e.jsx("div",{className:"text-gray-600",children:"All"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-xl font-bold text-green-600",children:((d=s.minibuses)==null?void 0:d.active)??0}),e.jsx("div",{className:"text-gray-600",children:"Active"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-xl font-bold text-red-600",children:((x=s.minibuses)==null?void 0:x.inactive)??0}),e.jsx("div",{className:"text-gray-600",children:"Inactive"})]})]})]})]}),e.jsxs(a,{className:"shadow-md",children:[e.jsx(l,{children:e.jsxs(i,{className:"flex items-center gap-2",children:[e.jsx(p,{className:"h-5 w-5 text-blue-600"})," Driver Statistics"]})}),e.jsxs(c,{children:[e.jsx("p",{className:"text-gray-500 mb-2",children:"Summary of all drivers in the system."}),e.jsxs("div",{className:"flex gap-8",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-xl font-bold",children:((n=s.drivers)==null?void 0:n.all)??0}),e.jsx("div",{className:"text-gray-600",children:"All"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-xl font-bold text-green-600",children:((m=s.drivers)==null?void 0:m.active)??0}),e.jsx("div",{className:"text-gray-600",children:"Active"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-xl font-bold text-red-600",children:((h=s.drivers)==null?void 0:h.inactive)??0}),e.jsx("div",{className:"text-gray-600",children:"Inactive"})]})]})]})]}),e.jsxs(a,{className:"shadow-md",children:[e.jsx(l,{children:e.jsxs(i,{className:"flex items-center gap-2",children:[e.jsx(u,{className:"h-5 w-5 text-blue-600"})," Members & Memberships"]})}),e.jsxs(c,{children:[e.jsx("p",{className:"text-gray-500 mb-2",children:"Membership status and unpaid memberships."}),e.jsxs("div",{className:"flex gap-8",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-xl font-bold",children:s.members??0}),e.jsx("div",{className:"text-gray-600",children:"All Members"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-xl font-bold text-yellow-600",children:((j=s.memberships)==null?void 0:j.unpaid)??0}),e.jsx("div",{className:"text-gray-600",children:"Unpaid memberships"})]})]})]})]}),e.jsxs(a,{className:"shadow-md",children:[e.jsx(l,{children:e.jsxs(i,{className:"flex items-center gap-2",children:[e.jsx(b,{className:"h-5 w-5 text-yellow-600"})," Misconducts"]})}),e.jsxs(c,{children:[e.jsx("p",{className:"text-gray-500 mb-2",children:"Reported misconducts in the association."}),e.jsx("div",{className:"text-center",children:e.jsx("span",{className:"text-xl font-bold text-yellow-600",children:o??0})})]})]}),e.jsxs(a,{className:"shadow-md",children:[e.jsx(l,{children:e.jsxs(i,{className:"flex items-center gap-2",children:[e.jsx(y,{className:"h-5 w-5 text-blue-600"})," Pending Requests"]})}),e.jsxs(c,{children:[e.jsx("p",{className:"text-gray-500 mb-2",children:"Requests awaiting action."}),e.jsxs("div",{className:"flex gap-8",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-xl font-bold text-blue-600",children:N??0}),e.jsx("div",{className:"text-gray-600",children:"Clearance"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-xl font-bold text-blue-600",children:v??0}),e.jsx("div",{className:"text-gray-600",children:"Transfer"})]})]})]})]})]})}export{R as default};
