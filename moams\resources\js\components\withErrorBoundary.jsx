import React from 'react';
import ErrorBoundary from './ErrorBoundary';

/**
 * Higher-order component that wraps a component with an error boundary
 */
const withErrorBoundary = (WrappedComponent, errorBoundaryProps = {}) => {
    const WithErrorBoundaryComponent = (props) => {
        return (
            <ErrorBoundary
                errorContext={{
                    component: WrappedComponent.name || 'Unknown',
                    props: Object.keys(props),
                }}
                {...errorBoundaryProps}
            >
                <WrappedComponent {...props} />
            </ErrorBoundary>
        );
    };

    WithErrorBoundaryComponent.displayName = `withErrorBoundary(${WrappedComponent.displayName || WrappedComponent.name || 'Component'})`;

    return WithErrorBoundaryComponent;
};

export default withErrorBoundary;
