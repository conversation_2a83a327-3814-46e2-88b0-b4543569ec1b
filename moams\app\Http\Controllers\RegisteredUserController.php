<?php

namespace App\Http\Controllers;

use App\Http\Requests\UpdateUserRequest;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rules;
use Inertia\Inertia;
use Inertia\Response;
use Spatie\Permission\Models\Role;

class RegisteredUserController extends Controller
{
    public function index(Request $request): Response
    {
        $query = User::with('roles');

        // Get count of system admins to determine if we should hide the last one
        $adminCount = User::whereHas('roles', function ($q) {
            $q->where('name', 'system admin');
        })->count();

        // If there's only one admin, exclude them from the list for security
        if ($adminCount === 1) {
            $query->whereDoesntHave('roles', function ($q) {
                $q->where('name', 'system admin');
            });
        }

        // Handle search
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                    ->orWhere('last_name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%")
                    ->orWhere('phone_number', 'like', "%{$search}%")
                    ->orWhere('district', 'like', "%{$search}%")
                    ->orWhere('village', 'like', "%{$search}%")
                    ->orWhereRaw("CONCAT(first_name, ' ', last_name) like ?", ["%{$search}%"]);
            });
        }

        // Handle role filter
        if ($request->has('role') && $request->role !== 'all') {
            $query->whereHas('roles', function ($q) use ($request) {
                $q->where('name', $request->role);
            });
        }

        // Handle status filter
        if ($request->has('status') && $request->status !== 'all') {
            if ($request->status === 'active') {
                $query->whereNull('archived_at');
            } elseif ($request->status === 'archived') {
                $query->whereNotNull('archived_at');
            }
        }

        $users = $query->orderBy('created_at', 'desc')->paginate(10);
        $user = auth()->user();

        // Get accurate statistics from the entire database
        $statistics = [
            'total_users' => User::count(),
            'minibus_owners' => User::whereHas('roles', function ($q) {
                $q->where('name', 'minibus owner');
            })->count(),
            'association_clerks' => User::whereHas('roles', function ($q) {
                $q->where('name', 'association clerk');
            })->count(),
            'association_managers' => User::whereHas('roles', function ($q) {
                $q->where('name', 'association manager');
            })->count(),
            'system_admins' => User::whereHas('roles', function ($q) {
                $q->where('name', 'system admin');
            })->count(),
            'active_users' => User::whereNull('archived_at')->count(),
            'archived_users' => User::whereNotNull('archived_at')->count(),
        ];

        return Inertia::render('userManagement/index-user', [
            'users' => $users,
            'statistics' => $statistics,
            'userRoles' => $user ? $user->roles->pluck('name')->toArray() : [],
        ]);
    }

    public function show(User $user): Response
    {
        $user->load('roles');
        $currentUser = auth()->user();
        $users = User::with('roles')->get();
        return Inertia::render('userManagement/show-user', [
            'user' => $user,
            'users' => $users,
            'userRoles' => $currentUser ? $currentUser->roles->pluck('name')->toArray() : [],
        ]);
    }

    public function create(): Response
    {
        $allRoles = Role::pluck('name')->toArray();
        $roles = array_values(array_intersect($allRoles, [
            'association manager',
            'association clerk',
            'minibus owner',
            'system admin',
        ]));
        $user = auth()->user();
        return Inertia::render('userManagement/create-user', [
            'roles' => $roles,
            'userRoles' => $user ? $user->roles->pluck('name')->toArray() : [],
        ]);
    }

    public function storeUser(Request $request): RedirectResponse
    {
        $currentUser = auth()->user();
        // If the current user is an association clerk, force role to 'minibus owner'
        if ($currentUser && $currentUser->hasRole('association clerk')) {
            $request->merge(['role' => 'minibus owner']);
        }

        $validationRules = [
            'first_name' => ['required', 'string', 'max:255', 'regex:/^[a-zA-Z\s]+$/'],
            'last_name' => ['required', 'string', 'max:255', 'regex:/^[a-zA-Z\s]+$/'],
            'gender' => 'required|string',

            'district' => 'required|string|max:255',
            'village' => 'string|max:255',
            'email' => 'required|string|lowercase|email|max:255|unique:' . User::class,
            'phone_number' => [
                'required',
                'unique:' . User::class,
                'string',
                function ($attribute, $value, $fail) {
                    if (!preg_match('/^(?:\\+265|0)[89]\\d{8}$/', $value)) {
                        $fail('The ' . preg_replace('/_/', ' ', $attribute) . ' is invalid.');
                    }
                }
            ],
            'password' => ['required', 'confirmed', \Illuminate\Validation\Rules\Password::defaults()],
            'role' => 'required|string|exists:roles,name',
        ];
        if ($request->role === 'minibus owner') {
            $validationRules['commitment_statement'] = 'required';
            $validationRules['joining_date'] = 'required|date|before_or_equal:today';
            $validationRules['national_id'] = 'required|file|mimes:jpg,jpeg,png,pdf|max:2048'; // Add file validation
        } else {
            $validationRules['joining_date'] = 'nullable';
        }
        $validated = $request->validate($validationRules);
        // Handle file upload
        $nationalIdPath = null;
        if ($request->hasFile('national_id')) {
            $file = $request->file('national_id');
            $hash = md5_file($file->getRealPath());
            $existing = User::where('national_id_path', 'like', "%$hash%")
                ->first();
            if ($existing) {
                $nationalIdPath = $existing->national_id_path;
            } else {
                $filename = $hash . '.' . $file->getClientOriginalExtension();
                $nationalIdPath = $file->storeAs('national_ids', $filename, 'public');
            }
        }
        $user = User::create([
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'gender' => $request->gender,
            'district' => $request->district,
            'village' => $request->village,
            'email' => $request->email,
            'phone_number' => $request->phone_number,
            'password' => Hash::make($request->password),
            'commitment_statement' => $request->role === 'minibus owner' ? $request->commitment_statement : null,
            'joining_date' => $request->role === 'minibus owner' ? $request->joining_date : null,
            'national_id_path' => $nationalIdPath, // Save file path if uploaded
        ]);
        $user->assignRole($request->role);

        // Automatically create the first Membership record for minibus owners (on create)
        if ($request->role === 'minibus owner' && $request->joining_date && !$user->memberships()->where('type', 'Registration')->exists()) {
            $joiningDate = $request->joining_date;
            $startDate = $joiningDate;
            $endDate = \Carbon\Carbon::parse($joiningDate)->addYear()->subDay();
            $user->memberships()->create([
                'start_date' => $startDate,
                'end_date' => $endDate,
                'type' => 'Registration',
                'status' => 'Unregistered',
            ]);
        }

        if ($currentUser && $currentUser->hasRole('association clerk')) {
            return redirect()->route('membership.management')->with('success', 'User created successfully!');
        }
        return redirect()->route('admin.users')->with('success', 'User created successfully!');
    }

    public function edit(User $user): Response
    {
        $user->load('roles');
        $allRoles = Role::pluck('name')->toArray();
        $roles = array_values(array_intersect($allRoles, [
            'association manager',
            'association clerk',
            'minibus owner',
            'system admin',
        ]));
        $currentUser = auth()->user();
        return Inertia::render('userManagement/edit-user', [
            'user' => $user,
            'roles' => $roles,
            'userRoles' => $currentUser ? $currentUser->roles->pluck('name')->toArray() : [],
        ]);
    }

    public function update(UpdateUserRequest $request, User $user): RedirectResponse
    {
        $currentUser = auth()->user();
        // If the current user is an association clerk, force role to 'minibus owner'
        if ($currentUser && $currentUser->hasRole('association clerk')) {
            $request->merge(['role' => 'minibus owner']);
        }

        // Prevent editing the last unarchived system admin
        if ($user->hasRole('system admin')) {
            $adminCount = \App\Models\User::role('system admin')->whereNull('archived_at')->count();
            if ($adminCount <= 1) {
                return redirect()->route('admin.users.show', $user)->with([
                    'error' => 'Cannot edit the last system admin.',
                    'flashId' => now()->timestamp,
                ]);
            }
        }

        $validatedData = $request->validated();

        // Only update fields that were provided in the request
        foreach ($validatedData as $field => $value) {
            if ($field === 'role')
                continue; // Don't set role as a user column
            if ($request->has($field)) {
                $user->$field = $value;
            }
        }

        $wasChanged = $user->isDirty();

        if ($wasChanged) {
            $user->save();

            // Handle role assignment
            if ($request->has('role')) {
                $user->syncRoles([$request->role]);
            }

            // Automatically create the first Membership record for minibus owners (on update)
            if ($request->role === 'minibus owner' && $user->joining_date && !$user->memberships()->where('type', 'Registration')->exists()) {
                $joiningDate = $user->joining_date;
                $startDate = $joiningDate;
                $endDate = \Carbon\Carbon::parse($joiningDate)->addYear()->subDay();
                $user->memberships()->create([
                    'start_date' => $startDate,
                    'end_date' => $endDate,
                    'type' => 'Registration',
                    'status' => 'Unregistered',
                ]);
            }

            if ($currentUser && $currentUser->hasRole('association clerk')) {
                return redirect()->route('membership.management')->with([
                    'success' => 'User updated successfully!',
                    'flashId' => now()->timestamp,
                ]);
            }
            return redirect()->route('admin.users.show', $user)->with([
                'success' => 'User updated successfully!',
                'flashId' => now()->timestamp,
            ]);
        } else {
            if ($currentUser && $currentUser->hasRole('association clerk')) {
                return redirect()->route('membership.management')->with([
                    'success' => 'No changes were made',
                    'flashId' => now()->timestamp,
                ]);
            }
            return redirect()->route('admin.users.show', $user)->with([
                'success' => 'No changes were made',
                'flashId' => now()->timestamp,
            ]);
        }
    }

    public function archive(User $user): RedirectResponse
    {
        if ($user->id === auth()->id()) {
            return redirect()->route('admin.users')->with('error', 'You cannot archive your own account.');
        }
        if ($user->hasRole('system admin')) {
            $adminCount = User::role('system admin')->whereNull('archived_at')->count();
            if ($adminCount <= 1) {
                return redirect()->route('admin.users')->with('error', 'Cannot archive the last system admin.');
            }
        }
        $user->archive();
        $userName = $user->first_name . ' ' . $user->last_name;
        return redirect()->route('admin.users')->with('success', "User '{$userName}' has been archived successfully.");
    }

    public function unarchive(User $user): RedirectResponse
    {
        $user->unarchive();
        $userName = $user->first_name . ' ' . $user->last_name;
        return redirect()->route('admin.users')->with('success', "User '{$userName}' has been unarchived successfully.");
    }
}