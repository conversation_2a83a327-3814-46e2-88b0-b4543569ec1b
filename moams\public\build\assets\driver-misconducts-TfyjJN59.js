import{d as x,T as r,j as e,L as d,B as o,C as h,a as f,b as p,h as m,c as j}from"./app-DL-qYY5V.js";import{A as u}from"./app-layout-YqstQnqE.js";import{n as g}from"./navigation-DAA2N51J.js";import{P as N}from"./pagination-CL_CHA67.js";import{C as b}from"./chevron-left-DFeVEtK7.js";import{P as w}from"./plus-CU6rIcI2.js";import"./index-1tPPImJd.js";import"./index-BTzg1GwG.js";import"./app-logo-icon-BnXlkpcX.js";import"./bus-CKTBKI0T.js";import"./dollar-sign-Cx0-nQIX.js";import"./users-DNGXY-sJ.js";import"./clipboard-list-CxRumT8v.js";function O({driver:t,misconducts:s}){const{userRoles:n}=x().props,c=n&&n.includes("minibus owner"),l=(s==null?void 0:s.data)||[],i=[{title:"Misconduct Management",href:r("misconducts.index")},{title:`${(t==null?void 0:t.first_name)||"Unknown"} ${(t==null?void 0:t.last_name)||"Driver"}`,href:r("drivers.misconducts",t==null?void 0:t.id)}];return e.jsx(u,{breadcrumbs:i,children:e.jsx("div",{className:"w-full max-w-full overflow-hidden",children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex flex-col gap-4 mb-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4",children:[e.jsxs("button",{className:"flex items-center px-3 py-1 rounded hover:bg-gray-100 border border-gray-200 w-fit",onClick:()=>g(i),children:[e.jsx(b,{className:"h-4 w-4 mr-1"})," Back"]}),e.jsx("div",{className:"flex-1 sm:flex-none",children:e.jsxs("span",{className:"text-muted-foreground text-sm sm:text-base font-medium break-words",children:["Misconduct history for ",t.first_name," ",t.last_name]})})]}),e.jsx("div",{className:"flex flex-wrap items-center gap-2 justify-start sm:justify-end",children:c&&e.jsx(d,{href:r("misconducts.index"),children:e.jsxs(o,{className:"w-fit flex items-center gap-2 bg-red-600 hover:bg-red-700 text-white px-4 py-2",children:[e.jsx(w,{className:"h-4 w-4"}),"Report New Misconduct"]})})})]}),e.jsx("div",{className:"mb-6",children:e.jsx("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-green-900",children:"Misconduct Summary"}),e.jsxs("p",{className:"text-green-700",children:["Total misconducts: ",s.total," | Showing: ",l.length," of ",s.total]})]})})})}),e.jsx("div",{className:"w-full border-2 border-blue-200 rounded-lg",style:{maxWidth:"calc(100vw - 2rem)",overflow:"hidden"},children:e.jsxs(h,{className:"w-full border-0 shadow-none",children:[e.jsx(f,{className:"px-0",children:e.jsxs(p,{className:"flex items-center gap-2",children:[e.jsx(m,{className:"h-5 w-5"}),"Driver Misconducts (",l.length,")"]})}),e.jsx(j,{className:"p-0",children:l.length===0?e.jsxs("div",{className:"text-center py-8 px-6",children:[e.jsx(m,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),e.jsx("h3",{className:"text-lg font-medium mb-2",children:"No misconducts found for this driver"}),e.jsx("p",{className:"text-muted-foreground mb-4",children:"This driver has no recorded misconducts."})]}):e.jsx("div",{className:"overflow-x-auto mb-8",children:e.jsxs("table",{className:"min-w-full w-full bg-white border border-gray-200 rounded-lg",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"bg-gray-100 text-gray-700",children:[e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Misconduct Type"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Date"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Description"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Actions"})]})}),e.jsx("tbody",{children:l.map(a=>e.jsxs("tr",{className:"border-b hover:bg-gray-50",children:[e.jsx("td",{className:"px-4 py-3 font-medium",children:a.name}),e.jsx("td",{className:"px-4 py-3",children:new Date(a.offense_date).toLocaleDateString()}),e.jsx("td",{className:"px-4 py-3 max-w-24",children:e.jsx("div",{className:"truncate",title:a.description||"N/A",children:a.description||"N/A"})}),e.jsx("td",{className:"px-4 py-3",children:e.jsx("div",{className:"flex items-center gap-2",children:e.jsx(d,{href:r("misconducts.show",a.id),children:e.jsx(o,{variant:"outline",size:"sm",className:"text-blue-700 border-blue-500 hover:bg-blue-50",children:"View Details"})})})})]},a.id))})]})})})]})}),s.total>0&&s.total>s.per_page&&e.jsx("div",{className:"mt-6 w-full",children:e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 px-4 py-4 border-t bg-gray-50/50 rounded-b-lg",children:[e.jsxs("div",{className:"text-sm text-gray-700 text-center sm:text-left",children:["Showing ",s.from," to ",s.to," of ",s.total," results"]}),s.last_page>1&&e.jsx(N,{data:s})]})})]})})})}export{O as default};
