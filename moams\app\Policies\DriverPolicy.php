<?php

namespace App\Policies;

use App\Models\Driver;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class DriverPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        // Association clerks can view all drivers
        if ($user->hasRole('association_clerk')) {
            return true;
        }

        // Minibus owners can view drivers associated with their minibuses
        if ($user->hasRole('minibus_owner')) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Driver $driver): bool
    {
        // Association clerks can view any driver
        if ($user->hasRole('association_clerk')) {
            return true;
        }

        // Minibus owners can view drivers associated with their minibuses
        if ($user->hasRole('minibus_owner')) {
            return $driver->minibuses()->whereHas('ownershipHistory', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })->exists();
        }

        return false;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        // Association clerks can create drivers
        if ($user->hasRole('association_clerk')) {
            return true;
        }

        // Minibus owners can create drivers
        if ($user->hasRole('minibus_owner')) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Driver $driver): bool
    {
        // Association clerks can update any driver
        if ($user->hasRole('association_clerk')) {
            return true;
        }

        // Minibus owners can update drivers associated with their minibuses
        if ($user->hasRole('minibus_owner')) {
            return $driver->minibuses()->whereHas('ownershipHistory', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })->exists();
        }

        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Driver $driver): bool
    {
        // Association clerks can delete any driver
        if ($user->hasRole('association_clerk')) {
            return true;
        }

        // Minibus owners can delete drivers associated with their minibuses
        if ($user->hasRole('minibus_owner')) {
            return $driver->minibuses()->whereHas('ownershipHistory', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })->exists();
        }

        return false;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Driver $driver): bool
    {
        // Only association clerks can restore drivers
        return $user->hasRole('association_clerk');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Driver $driver): bool
    {
        // Only association clerks can permanently delete drivers
        return $user->hasRole('association_clerk');
    }
}
