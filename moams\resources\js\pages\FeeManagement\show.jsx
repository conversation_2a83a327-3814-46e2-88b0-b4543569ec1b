import React from 'react';
import { Link } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, DollarSign, Calendar, User, FileText } from 'lucide-react';

export default function ShowFeeSetting({ feeSetting }) {
    const breadcrumbs = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Fee Management', href: '/fee-settings' },
        { title: `${feeSetting.fee_type} Fee Details`, href: `/fee-settings/${feeSetting.id}` },
    ];

    const getFeeTypeIcon = (type) => {
        switch (type) {
            case 'registration':
                return '📝';
            case 'affiliation':
                return '🤝';
            default:
                return '💰';
        }
    };

    const getStatusBadge = (isActive) => {
        return isActive ? (
            <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
                Active
            </Badge>
        ) : (
            <Badge variant="secondary">
                Inactive
            </Badge>
        );
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        });
    };

    const formatDateTime = (dateString) => {
        return new Date(dateString).toLocaleString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <div className="p-2 sm:p-4 md:p-8">
                <div className="flex flex-col sm:flex-row gap-2 sm:gap-4 items-center mb-4 sm:mb-6">
                    <div className="flex items-center gap-2">
                        <Link href="/fee-settings">
                            <Button variant="outline" className="w-full sm:w-auto">
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                Back to Fee Management
                            </Button>
                        </Link>
                        <span className="text-muted-foreground text-base font-medium">
                            View detailed information about this fee setting.
                        </span>
                    </div>
                </div>

                <div className="w-full max-w-4xl mx-auto space-y-6">
                    {/* Main Fee Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <span className="text-2xl">{getFeeTypeIcon(feeSetting.fee_type)}</span>
                                {feeSetting.fee_type.charAt(0).toUpperCase() + feeSetting.fee_type.slice(1)} Fee Setting
                                {getStatusBadge(feeSetting.is_active)}
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-4">
                                    <div className="flex items-center gap-2">
                                        <DollarSign className="h-5 w-5 text-green-600" />
                                        <div>
                                            <p className="text-sm font-medium text-gray-500">Amount</p>
                                            <p className="text-2xl font-bold">${feeSetting.amount.toFixed(2)}</p>
                                        </div>
                                    </div>

                                    <div className="flex items-center gap-2">
                                        <Calendar className="h-5 w-5 text-blue-600" />
                                        <div>
                                            <p className="text-sm font-medium text-gray-500">Effective From</p>
                                            <p className="text-lg font-semibold">{formatDate(feeSetting.effective_from)}</p>
                                        </div>
                                    </div>

                                    <div className="flex items-center gap-2">
                                        <User className="h-5 w-5 text-purple-600" />
                                        <div>
                                            <p className="text-sm font-medium text-gray-500">Created By</p>
                                            <p className="text-lg font-semibold">
                                                {feeSetting.created_by_user?.first_name} {feeSetting.created_by_user?.last_name}
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <div className="space-y-4">
                                    <div>
                                        <p className="text-sm font-medium text-gray-500 mb-2">Fee Type</p>
                                        <div className="flex items-center gap-2 p-3 border rounded-md bg-gray-50">
                                            <span className="text-lg">{getFeeTypeIcon(feeSetting.fee_type)}</span>
                                            <span className="font-medium capitalize">{feeSetting.fee_type}</span>
                                        </div>
                                    </div>

                                    <div>
                                        <p className="text-sm font-medium text-gray-500 mb-2">Created At</p>
                                        <p className="text-sm">{formatDateTime(feeSetting.created_at)}</p>
                                    </div>

                                    <div>
                                        <p className="text-sm font-medium text-gray-500 mb-2">Last Updated</p>
                                        <p className="text-sm">{formatDateTime(feeSetting.updated_at)}</p>
                                    </div>
                                </div>
                            </div>

                            {feeSetting.description && (
                                <div className="mt-6 pt-6 border-t">
                                    <div className="flex items-center gap-2 mb-3">
                                        <FileText className="h-5 w-5 text-gray-600" />
                                        <p className="text-sm font-medium text-gray-500">Description</p>
                                    </div>
                                    <div className="p-4 bg-gray-50 rounded-lg">
                                        <p className="text-gray-700">{feeSetting.description}</p>
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Action Buttons */}
                    <div className="flex flex-col sm:flex-row gap-3">
                        {feeSetting.is_active && (
                            <Link href={route('fee-settings.edit', feeSetting.id)}>
                                <Button className="flex-1 sm:flex-none">
                                    Edit Fee Setting
                                </Button>
                            </Link>
                        )}
                        <Link href={route('fee-settings.history', feeSetting.fee_type)}>
                            <Button variant="outline" className="flex-1 sm:flex-none">
                                View Fee History
                            </Button>
                        </Link>
                        <Link href="/fee-settings">
                            <Button variant="outline" className="flex-1 sm:flex-none">
                                Back to Fee Management
                            </Button>
                        </Link>
                    </div>

                    {/* Additional Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="text-lg">Additional Information</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div>
                                    <p className="font-medium text-gray-700">Fee Setting ID</p>
                                    <p className="text-gray-600">#{feeSetting.id}</p>
                                </div>
                                <div>
                                    <p className="font-medium text-gray-700">Status</p>
                                    <p className="text-gray-600">
                                        {feeSetting.is_active ? 'Currently Active' : 'Not Active'}
                                    </p>
                                </div>
                                <div>
                                    <p className="font-medium text-gray-700">Effective Date</p>
                                    <p className="text-gray-600">
                                        {new Date(feeSetting.effective_from) <= new Date() ? 'Already in effect' : 'Future effective date'}
                                    </p>
                                </div>
                                <div>
                                    <p className="font-medium text-gray-700">Created By User ID</p>
                                    <p className="text-gray-600">#{feeSetting.created_by}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
} 