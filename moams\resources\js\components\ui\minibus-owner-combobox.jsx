import * as React from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
    Command,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
    CommandList,
} from "@/components/ui/command";
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@/components/ui/popover";
import { ChevronsUpDown, Check, Loader2 } from "lucide-react";

// Debounce hook
function useDebounce(value, delay) {
    const [debounced, setDebounced] = React.useState(value);
    React.useEffect(() => {
        const handler = setTimeout(() => setDebounced(value), delay);
        return () => clearTimeout(handler);
    }, [value, delay]);
    return debounced;
}

export function MinibusOwnerCombobox({
    value,
    onChange,
    placeholder = "Select minibus owner...",
    fetchOwners, // async function: (search) => Promise<owners[]>
}) {
    const [open, setOpen] = React.useState(false);
    const [search, setSearch] = React.useState("");
    const [loading, setLoading] = React.useState(false);
    const [options, setOptions] = React.useState([]);
    const debouncedSearch = useDebounce(search, 300);

    // Fetch owners when popover is opened and search is empty (initial load)
    React.useEffect(() => {
        if (open && search === "") {
            setLoading(true);
            fetchOwners("").then(results => {
                const ownersWithStringId = results.map(o => ({ ...o, id: String(o.id) }));
                const uniqueOwners = [];
                const seenIds = new Set();
                for (const owner of ownersWithStringId) {
                    if (!seenIds.has(owner.id)) {
                        uniqueOwners.push(owner);
                        seenIds.add(owner.id);
                    }
                }
                setOptions(uniqueOwners);
                setLoading(false);
            });
        }
        // eslint-disable-next-line
    }, [open]);

    // Fetch owners when debounced search changes (user types)
    React.useEffect(() => {
        if (debouncedSearch !== "") {
            setLoading(true);
            fetchOwners(debouncedSearch).then(results => {
                const ownersWithStringId = results.map(o => ({ ...o, id: String(o.id) }));
                const uniqueOwners = [];
                const seenIds = new Set();
                for (const owner of ownersWithStringId) {
                    if (!seenIds.has(owner.id)) {
                        uniqueOwners.push(owner);
                        seenIds.add(owner.id);
                    }
                }
                setOptions(uniqueOwners);
                setLoading(false);
            });
        }
    }, [debouncedSearch, fetchOwners]);

    return (
        <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
                <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={open}
                    className="w-full justify-between h-9 px-3 py-1 text-base rounded-md border border-input bg-transparent font-normal"
                >
                    {value
                        ? value.name || placeholder
                        : <span className="text-muted-foreground">{placeholder}</span>}
                    {open && loading ? (
                        <Loader2 className="ml-2 h-4 w-4 animate-spin opacity-50" />
                    ) : (
                        <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                    )}
                </Button>
            </PopoverTrigger>
            <PopoverContent className="w-full p-0">
                <Command>
                    <CommandInput
                        placeholder="Search owner..."
                        value={search}
                        onValueChange={setSearch}
                    />
                    <CommandList>
                        <CommandEmpty>{loading ? "Searching..." : "Minibus owners not found."}</CommandEmpty>
                        <CommandGroup>
                            {options.map((owner) => (
                                <CommandItem
                                    key={owner.id}
                                    value={owner.name}
                                    onSelect={() => {
                                        onChange(owner);
                                        setOpen(false);
                                    }}
                                >
                                    <Check
                                        className={
                                            value && value.id === owner.id
                                                ? "mr-2 h-4 w-4 opacity-100"
                                                : "mr-2 h-4 w-4 opacity-0"
                                        }
                                    />
                                    {owner.name} {owner.email ? <span className="text-xs text-muted-foreground">({owner.email})</span> : null}
                                </CommandItem>
                            ))}
                        </CommandGroup>
                    </CommandList>
                </Command>
            </PopoverContent>
        </Popover>
    );
} 