import{u as O,r as t,j as e,H as z,L as b,B as u,C as B,a as I,b as P,k as R,c as V}from"./app-DL-qYY5V.js";import{I as c}from"./input-Dm4SEXxy.js";import{L as n}from"./label-e3QxUH-L.js";import{S as v,a as f,b as _,c as g,d as N}from"./select-Cp8NjZe8.js";import{A as H,b as K,c as $,d as U}from"./app-layout-YqstQnqE.js";import{M as Y}from"./minibus-owner-combobox-l9syvT7Z.js";import{A as C}from"./arrow-left-DCW23wrL.js";import"./index-BTzg1GwG.js";import"./index-1tPPImJd.js";import"./index-uC6ZAdKJ.js";import"./app-logo-icon-BnXlkpcX.js";import"./bus-CKTBKI0T.js";import"./dollar-sign-Cx0-nQIX.js";import"./users-DNGXY-sJ.js";import"./clipboard-list-CxRumT8v.js";import"./search-B4sum6Qx.js";import"./loader-circle-D3J3XKS7.js";function pe({userRole:h,minibusOwners:Z}){const{data:l,setData:r,post:w,processing:x,errors:i}=O({first_name:"",last_name:"",phone_number:"",license_number:"",district:"",village_town:"",owner_id:"",minibus_id:"",license:null}),[y,S]=t.useState(null),[q,D]=t.useState([]),[G,J]=t.useState(null),[p,d]=t.useState([]),[Q,j]=t.useState(null),[m,F]=t.useState(null);t.useEffect(()=>{fetch("/api/vehicle-routes").then(s=>s.json()).then(s=>D(s))},[]),t.useEffect(()=>{fetch("/api/minibuses").then(s=>s.json()).then(s=>d(s))},[]);const M=s=>{const a=s.target.files[0];if(m&&a&&a.name===m.name&&a.size===m.size&&a.lastModified===m.lastModified){alert("You have already selected this file. Please choose a different file."),s.target.value="";return}F(a),r("license",a)},k=s=>{s.preventDefault(),w(route("drivers.store"),{forceFormData:!0})},L=["Lilongwe","Blantyre","Mzuzu","Zomba","Kasungu","Ntcheu","Mangochi","Salima","Dowa","Ntchisi","Dedza","Nkhotakota","Mchinji","Nkhatabay","Rumphi","Chitipa","Karonga","Thyolo","Mulanje","Phalombe","Chikwawa","Nsanje","Balaka","Machinga"],E=[{title:"Dashboard",href:"/dashboard"},{title:h==="minibus owner"?"My Drivers":"Driver Management",href:"/drivers"},{title:"Add Driver",href:"/drivers/create"}],T=t.useCallback(async s=>(await(await fetch(`/api/minibus-owners?search=${encodeURIComponent(s)}`)).json()).filter(o=>o.id!==null&&o.id!==void 0&&o.id!==""),[]),A=t.useCallback(async s=>{try{const a=await fetch(`/api/minibuses/available-for-owner/${s}`);if(a.ok){const o=await a.json();d(o)}else d([])}catch(a){console.error("Error fetching minibuses:",a),d([])}},[]);return e.jsxs(H,{breadcrumbs:E,children:[e.jsx(z,{title:h==="minibus owner"?"Add My Driver":"Add New Driver"}),e.jsx("div",{className:"p-8",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(b,{href:route("drivers.index"),children:e.jsxs(u,{variant:"outline",size:"sm",children:[e.jsx("span",{className:"block sm:hidden",children:e.jsxs(K,{children:[e.jsx($,{asChild:!0,children:e.jsx("span",{children:e.jsx(C,{className:"h-4 w-4"})})}),e.jsx(U,{children:"Back to Drivers"})]})}),e.jsxs("span",{className:"hidden sm:flex items-center",children:[e.jsx(C,{className:"mr-2 h-4 w-4"}),"Back to Drivers"]})]})}),e.jsx("p",{className:"text-muted-foreground",children:"Enter the driver's information below"})]}),e.jsx("div",{className:"max-w-2xl",children:e.jsxs(B,{children:[e.jsxs(I,{children:[e.jsx(P,{children:"Driver Information"}),e.jsx(R,{children:"Fill in the driver's personal and contact details"})]}),e.jsx(V,{children:e.jsxs("form",{onSubmit:k,className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"first_name",children:"First Name *"}),e.jsx(c,{id:"first_name",value:l.first_name||"",onChange:s=>r("first_name",s.target.value),placeholder:"Enter first name",className:i.first_name?"border-red-500":""}),i.first_name&&e.jsx("p",{className:"text-sm text-red-500",children:i.first_name})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"last_name",children:"Last Name *"}),e.jsx(c,{id:"last_name",value:l.last_name||"",onChange:s=>r("last_name",s.target.value),placeholder:"Enter last name",className:i.last_name?"border-red-500":""}),i.last_name&&e.jsx("p",{className:"text-sm text-red-500",children:i.last_name})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"phone_number",children:"Phone Number *"}),e.jsx(c,{id:"phone_number",value:l.phone_number||"",onChange:s=>r("phone_number",s.target.value),placeholder:"Enter phone number",className:i.phone_number?"border-red-500":""}),i.phone_number&&e.jsx("p",{className:"text-sm text-red-500",children:i.phone_number})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"license_number",children:"License Number *"}),e.jsx(c,{id:"license_number",value:l.license_number||"",onChange:s=>r("license_number",s.target.value),placeholder:"Enter license number",className:i.license_number?"border-red-500":""}),i.license_number&&e.jsx("p",{className:"text-sm text-red-500",children:i.license_number})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"license",children:"Driver's License (PDF/Image) *"}),e.jsx(c,{id:"license",type:"file",accept:".pdf,.jpg,.jpeg,.png",onChange:M,className:i.license?"border-red-500":""}),i.license&&e.jsx("p",{className:"text-sm text-red-500",children:i.license})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"district",children:"District *"}),e.jsxs(v,{value:l.district||"",onValueChange:s=>r("district",s),children:[e.jsx(f,{className:i.district?"border-red-500":"",children:e.jsx(_,{placeholder:"Select district"})}),e.jsx(g,{children:L.map(s=>e.jsx(N,{value:s,children:s},s))})]}),i.district&&e.jsx("p",{className:"text-sm text-red-500",children:i.district})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"village_town",children:"Village/Town *"}),e.jsx(c,{id:"village_town",value:l.village_town||"",onChange:s=>r("village_town",s.target.value),placeholder:"Enter village or town",className:i.village_town?"border-red-500":""}),i.village_town&&e.jsx("p",{className:"text-sm text-red-500",children:i.village_town})]}),h==="association clerk"&&e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"owner_id",children:"Minibus Owner *"}),e.jsx(Y,{value:y||null,onChange:s=>{S(s),r("owner_id",(s==null?void 0:s.id)||""),j(null),r("minibus_id",""),s!=null&&s.id?A(s.id):d([])},fetchOwners:T,placeholder:"Select minibus owner..."}),i.owner_id&&e.jsx("p",{className:"text-sm text-red-500",children:i.owner_id})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"minibus_id",children:"Minibus *"}),e.jsxs(v,{value:l.minibus_id||"",onValueChange:s=>{r("minibus_id",s),j(p.find(a=>a.id.toString()===s))},children:[e.jsx(f,{className:i.minibus_id?"border-red-500":"",children:e.jsx(_,{placeholder:"Select minibus"})}),e.jsx(g,{children:p.map(s=>e.jsx(N,{value:s.id.toString(),children:s.number_plate},s.id))})]}),i.minibus_id&&e.jsx("p",{className:"text-sm text-red-500",children:i.minibus_id})]})]}),e.jsxs("div",{className:"flex justify-end space-x-4 pt-6",children:[e.jsx(b,{href:route("drivers.index"),children:e.jsx(u,{type:"button",variant:"outline",children:"Cancel"})}),e.jsx(u,{type:"submit",disabled:x,className:"bg-blue-600 hover:bg-blue-700",children:x?"Creating...":"Create Driver"})]})]})})]})})]})})]})}export{pe as default};
