import{u as v,j as e,L as f,B as n,C as b,a as N,b as y,c as w}from"./app-DL-qYY5V.js";import{A as _}from"./app-layout-YqstQnqE.js";import{I as p}from"./input-Dm4SEXxy.js";import{L as m}from"./label-e3QxUH-L.js";import{T as C}from"./textarea-SHrtPYpi.js";import{I as o}from"./input-error-C6jcuIY6.js";import{A as F}from"./arrow-left-DCW23wrL.js";import"./index-1tPPImJd.js";import"./index-BTzg1GwG.js";import"./app-logo-icon-BnXlkpcX.js";import"./bus-CKTBKI0T.js";import"./dollar-sign-Cx0-nQIX.js";import"./users-DNGXY-sJ.js";import"./clipboard-list-CxRumT8v.js";function H({feeSetting:s,currentFee:a}){const{data:i,setData:l,patch:h,processing:c,errors:r}=v({amount:s.amount||"",description:s.description||"",effective_from:s.effective_from?new Date(s.effective_from).toISOString().split("T")[0]:""}),u=[{title:"Dashboard",href:"/dashboard"},{title:"Fee Management",href:"/fee-settings"},{title:`Edit ${s.fee_type} Fee`,href:`/fee-settings/${s.id}/edit`}],x=t=>{t.preventDefault(),h(route("fee-settings.update",s.id))},j=()=>{if(!a)return!1;const t=parseFloat(a.amount),d=parseFloat(i.amount),g=s.effective_from?new Date(s.effective_from).toISOString().split("T")[0]:"";return t&&d&&t===d?!(a.id===s.id&&i.effective_from===g):!1};return e.jsx(_,{breadcrumbs:u,children:e.jsxs("div",{className:"p-2 sm:p-4 md:p-8",children:[e.jsx("div",{className:"flex flex-col sm:flex-row gap-2 sm:gap-4 items-center mb-4 sm:mb-6",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(f,{href:"/fee-settings",children:e.jsxs(n,{variant:"outline",className:"w-full sm:w-auto",children:[e.jsx(F,{className:"h-4 w-4 mr-2"}),"Back to Fee Management"]})}),e.jsxs("span",{className:"text-muted-foreground text-base font-medium",children:["Update the ",s.fee_type," fee setting."]})]})}),e.jsx("div",{className:"w-full max-w-2xl mx-auto",children:e.jsxs(b,{children:[e.jsx(N,{children:e.jsxs(y,{className:"flex items-center gap-2",children:["Edit ",s.fee_type.charAt(0).toUpperCase()+s.fee_type.slice(1)," Fee"]})}),e.jsx(w,{children:e.jsxs("form",{onSubmit:x,className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(m,{htmlFor:"fee_type",children:"Fee Type"}),e.jsx("div",{className:"flex items-center gap-2 p-3 border rounded-md bg-gray-50",children:e.jsx("span",{className:"font-medium capitalize",children:s.fee_type})}),e.jsx("p",{className:"text-sm text-gray-500",children:"Fee type cannot be changed once created."})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(m,{htmlFor:"current_amount",children:"Current Amount"}),e.jsx("div",{className:"flex items-center gap-2 p-3 border rounded-md bg-gray-50",children:e.jsxs("span",{className:"font-semibold",children:[e.jsx("span",{className:"text-green-600",children:"MK"})," ",s.amount]})}),e.jsx("p",{className:"text-sm text-gray-500",children:"Current active amount for this fee type."})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(m,{htmlFor:"amount",children:"New Amount (MK) *"}),e.jsx(p,{id:"amount",type:"number",step:"0.01",min:"0",value:i.amount,onChange:t=>l("amount",t.target.value),placeholder:"Enter new amount",className:r.amount?"border-red-500":""}),e.jsx(o,{message:r.amount,className:"mt-2"}),j()&&e.jsx("div",{className:"mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md",children:e.jsxs("p",{className:"text-sm text-yellow-800",children:["⚠️ This amount is the same as the current ",s.fee_type," fee (MK ",a!=null&&a.amount?parseFloat(a.amount).toLocaleString():"N/A","). The fee amount must be different from the current active fee unless you're only updating the description."]})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(m,{htmlFor:"effective_from",children:"Effective From *"}),e.jsx(p,{id:"effective_from",type:"date",value:i.effective_from,onChange:t=>l("effective_from",t.target.value),className:r.effective_from?"border-red-500":""}),e.jsx(o,{message:r.effective_from,className:"mt-2"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Changing this date will create a new fee setting"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(m,{htmlFor:"description",children:"Description (Optional)"}),e.jsx(C,{id:"description",value:i.description,onChange:t=>l("description",t.target.value),placeholder:"Enter a description for this fee change",rows:3,className:r.description?"border-red-500":""}),e.jsx(o,{message:r.description,className:"mt-2"})]}),e.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[e.jsx("h4",{className:"font-medium text-blue-900 mb-2",children:"Important Note"}),e.jsx("p",{className:"text-sm text-blue-700",children:"If you change the effective date, a new fee setting will be created. The current fee will remain active until the new effective date is reached."})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 pt-4",children:[e.jsx(n,{type:"submit",disabled:c,className:"flex-1 bg-blue-500 hover:bg-blue-600",children:c?"Updating...":"Update Fee Setting"}),e.jsx(f,{href:"/fee-settings",children:e.jsx(n,{variant:"outline",className:"w-full sm:w-auto",children:"Cancel"})})]})]})})]})})]})})}export{H as default};
