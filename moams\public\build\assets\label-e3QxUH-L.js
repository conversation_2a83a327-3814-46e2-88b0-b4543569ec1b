import{r as n,j as o,g as s}from"./app-DL-qYY5V.js";import{P as i}from"./index-BTzg1GwG.js";var d="Label",l=n.forwardRef((e,a)=>o.jsx(i.label,{...e,ref:a,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||((r=e.onMouseDown)==null||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName=d;var u=l;function b({className:e,...a}){return o.jsx(u,{"data-slot":"label",className:s("text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...a})}export{b as L};
