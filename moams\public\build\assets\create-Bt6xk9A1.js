import{u as w,j as e,H as F,L as x,B as l,C as h,a as p,b as u,c as j}from"./app-DL-qYY5V.js";import{A as _}from"./app-layout-YqstQnqE.js";import{I as g}from"./input-Dm4SEXxy.js";import{L as n}from"./label-e3QxUH-L.js";import{S as C,a as A,b as T,c as I,d as v}from"./select-Cp8NjZe8.js";import{T as D}from"./textarea-SHrtPYpi.js";import{I as i}from"./input-error-C6jcuIY6.js";import{A as L}from"./arrow-left-DCW23wrL.js";import{D as M}from"./dollar-sign-Cx0-nQIX.js";import"./index-1tPPImJd.js";import"./index-BTzg1GwG.js";import"./app-logo-icon-BnXlkpcX.js";import"./bus-CKTBKI0T.js";import"./users-DNGXY-sJ.js";import"./clipboard-list-CxRumT8v.js";import"./index-uC6ZAdKJ.js";function X({currentFees:o}){var d;const{data:t,setData:r,post:b,processing:c,errors:a}=w({fee_type:"",amount:"",description:"",effective_from:""}),N=s=>{s.preventDefault(),b(route("fee-settings.store"))},m=()=>{if(!t.fee_type||!o)return null;const s=o[t.fee_type];return s?parseFloat(s.amount):null},y=()=>{const s=m(),f=parseFloat(t.amount);return s&&f&&s===f},S=[{title:"Dashboard",href:"/dashboard"},{title:"Fee Management",href:"/fee-settings"},{title:"Set New Fee",href:"/fee-settings/create"}];return e.jsxs(_,{breadcrumbs:S,children:[e.jsx(F,{title:"Set New Fee"}),e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"max-w-2xl mx-auto",children:[e.jsxs("div",{className:"flex items-center space-x-4 mb-6",children:[e.jsx(x,{href:route("fee-settings.index"),children:e.jsxs(l,{variant:"outline",size:"sm",children:[e.jsx(L,{className:"mr-2 h-4 w-4"}),"Back to Fee Management"]})}),e.jsx("span",{className:"text-muted-foreground text-base font-medium",children:"Set new fee amount for the association"})]}),e.jsxs(h,{children:[e.jsx(p,{children:e.jsxs(u,{className:"flex items-center gap-2",children:[e.jsx(M,{className:"h-5 w-5"}),"Set New Fee"]})}),e.jsx(j,{children:e.jsxs("form",{onSubmit:N,className:"space-y-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"fee_type",children:"Fee Type *"}),e.jsxs(C,{value:t.fee_type,onValueChange:s=>r("fee_type",s),children:[e.jsx(A,{className:a.fee_type?"border-red-500":"",children:e.jsx(T,{placeholder:"Select fee type"})}),e.jsxs(I,{children:[e.jsx(v,{value:"registration",children:"Registration Fee"}),e.jsx(v,{value:"affiliation",children:"Affiliation Fee"})]})]}),e.jsx(i,{message:a.fee_type,className:"mt-2"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"amount",children:"Amount (MK) *"}),e.jsx(g,{id:"amount",type:"number",step:"0.01",min:"0",value:t.amount,onChange:s=>r("amount",s.target.value),placeholder:"Enter amount in Malawi Kwacha",className:a.amount?"border-red-500":""}),e.jsx(i,{message:a.amount,className:"mt-2"}),y()&&e.jsx("div",{className:"mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md",children:e.jsxs("p",{className:"text-sm text-yellow-800",children:["⚠️ This amount is the same as the current ",t.fee_type," fee (MK ",(d=m())==null?void 0:d.toLocaleString(),"). The fee amount must be different from the current active fee."]})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"effective_from",children:"Effective From *"}),e.jsx(g,{id:"effective_from",type:"date",value:t.effective_from,onChange:s=>r("effective_from",s.target.value),min:new Date().toISOString().split("T")[0],className:a.effective_from?"border-red-500":""}),e.jsx(i,{message:a.effective_from,className:"mt-2"}),e.jsx("p",{className:"text-sm text-gray-600",children:"This fee will become active from the selected date"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(n,{htmlFor:"description",children:"Description (Optional)"}),e.jsx(D,{id:"description",value:t.description,onChange:s=>r("description",s.target.value),placeholder:"Enter a description for this fee change (e.g., 'Annual fee increase due to economic changes')",rows:3,className:a.description?"border-red-500":""}),e.jsx(i,{message:a.description,className:"mt-2"})]}),e.jsxs("div",{className:"flex justify-end space-x-4",children:[e.jsx(x,{href:route("fee-settings.index"),children:e.jsx(l,{type:"button",variant:"outline",children:"Cancel"})}),e.jsx(l,{type:"submit",disabled:c,className:"bg-blue-600 hover:bg-blue-700",children:c?"Setting Fee...":"Set Fee"})]})]})})]}),e.jsxs(h,{className:"mt-6",children:[e.jsx(p,{children:e.jsx(u,{children:"Important Information"})}),e.jsx(j,{children:e.jsxs("div",{className:"space-y-3 text-sm text-gray-600",children:[e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("span",{className:"text-blue-600 font-semibold",children:"•"}),e.jsx("span",{children:"Setting a new fee will automatically deactivate the previous fee from the effective date."})]}),e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("span",{className:"text-blue-600 font-semibold",children:"•"}),e.jsx("span",{children:"Fees cannot be set for past dates to maintain data integrity."})]}),e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("span",{className:"text-blue-600 font-semibold",children:"•"}),e.jsx("span",{children:"All fee changes are logged with your user account for audit purposes."})]}),e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("span",{className:"text-blue-600 font-semibold",children:"•"}),e.jsx("span",{children:"Members will be charged the active fee amount when payments are recorded."})]})]})})]})]})})]})}export{X as default};
