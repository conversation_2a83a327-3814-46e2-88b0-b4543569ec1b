<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreMinibusRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'number_plate' => 'required|string|max:20|unique:minibuses,number_plate',
            'make' => 'required|string|max:255',
            'model' => 'required|string|max:255',
            'year_of_make' => 'nullable|integer|min:1980|max:' . (date('Y') + 1),
            'main_colour' => 'nullable|string|max:255',
            'proof_of_ownership' => 'required|file|mimes:pdf,jpg,jpeg,png',
            'route_id' => 'required|exists:vehicle_routes,id',
        ];
        if (auth()->user() && auth()->user()->hasRole('association clerk')) {
            $rules['owner_id'] = [
                'required',
                'exists:users,id',
                function ($attribute, $value, $fail) {
                    if (
                        !\App\Models\User::where('id', $value)->whereHas('roles', function ($q) {
                            $q->where('name', 'minibus owner');
                        })->exists()
                    ) {
                        $fail('Selected minibus owner is invalid.');
                    }
                }
            ];
        }
        return $rules;
    }
}
