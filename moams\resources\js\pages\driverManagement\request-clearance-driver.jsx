import { useState } from 'react';
import { Head, useForm, Link, router } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, AlertTriangle } from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import ConfirmDialog from '@/components/ui/confirm-dialog';

export default function RequestClearanceDriver({ driver, userRole, pendingRequest }) {
    const { data, setData, post, processing, errors } = useForm({
        reason: '',
    });

    const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
    const [loading, setLoading] = useState(false);

    const handleSubmit = (e) => {
        e.preventDefault();
        post(route('drivers.store-clearance-request', driver.id));
    };

    const handleDelete = () => {
        setConfirmDialogOpen(true);
    };
    const handleCancel = () => setConfirmDialogOpen(false);
    const handleConfirm = async () => {
        setLoading(true);
        await router.delete(route('drivers.clearance-requests.destroy', pendingRequest.id), {
            onSuccess: () => router.visit(route('drivers.show', driver.id)),
        });
        setLoading(false);
        setConfirmDialogOpen(false);
    };

    const breadcrumbs = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: userRole === 'minibus owner' ? 'My Drivers' : 'Driver Management', href: '/drivers' },
        { title: 'Request Clearance', href: '#' },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Request Clearance - ${driver.first_name} ${driver.last_name}`} />
            <div className="container mx-auto px-4 py-8">
                <div className="space-y-8">
                    <div className="flex items-center space-x-4 mb-8">
                        <Link href={route('drivers.show', driver.id)}>
                            <Button variant="outline" size="sm">
                                <span className="block sm:hidden">
                                    <Tooltip>
                                        <TooltipTrigger asChild>
                                            <span><ArrowLeft className="h-4 w-4" /></span>
                                        </TooltipTrigger>
                                        <TooltipContent>Back to Driver</TooltipContent>
                                    </Tooltip>
                                </span>
                                <span className="hidden sm:flex items-center">
                                    <ArrowLeft className="mr-2 h-4 w-4" />
                                    Back to Driver
                                </span>
                            </Button>
                        </Link>
                        <p className="text-muted-foreground">
                            Submit a clearance request for {driver.first_name} {driver.last_name}
                        </p>
                    </div>

                    <div className="grid grid-cols-1 gap-8">
                        {/* Driver Information */}
                        <div className="lg:col-span-2">
                            <Card className="mb-8">
                                <CardHeader>
                                    <CardTitle>Driver Information</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div>
                                            <label className="text-sm font-medium text-muted-foreground">Name</label>
                                            <p className="text-sm">{driver.first_name} {driver.last_name}</p>
                                        </div>
                                        <div>
                                            <label className="text-sm font-medium text-muted-foreground">Phone Number</label>
                                            <p className="text-sm">{driver.phone_number}</p>
                                        </div>
                                        <div>
                                            <label className="text-sm font-medium text-muted-foreground">District</label>
                                            <p className="text-sm">{driver.district}</p>
                                        </div>
                                        <div>
                                            <label className="text-sm font-medium text-muted-foreground">Village/Town</label>
                                            <p className="text-sm">{driver.village_town}</p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Clearance Request Form */}
                        <div>
                            <Card className="mb-8">
                                <CardHeader>
                                    <CardTitle>Clearance Request</CardTitle>
                                    <CardDescription>
                                        Provide a reason for requesting driver clearance
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <form onSubmit={handleSubmit} className="space-y-4">
                                        <div className="space-y-2">
                                            <Label htmlFor="reason">Reason for Clearance *</Label>
                                            <Textarea
                                                id="reason"
                                                value={data.reason}
                                                onChange={(e) => setData('reason', e.target.value)}
                                                placeholder="Please provide a detailed reason for requesting driver clearance..."
                                                rows={6}
                                                className={errors.reason ? 'border-red-500' : ''}
                                            />
                                            {errors.reason && (
                                                <p className="text-sm text-red-500">{errors.reason}</p>
                                            )}
                                        </div>

                                        <div className="flex justify-end space-x-4">
                                            <Link href={route('drivers.show', driver.id)}>
                                                <Button type="button" variant="outline">
                                                    Cancel
                                                </Button>
                                            </Link>
                                            <Button type="submit" disabled={processing}>
                                                {processing ? 'Submitting...' : 'Submit Request'}
                                            </Button>
                                        </div>
                                    </form>
                                </CardContent>
                            </Card>

                            {/* Quick Actions Card for Deleting Pending Request */}
                            {pendingRequest && (
                                <Card className="mb-8">
                                    <CardHeader>
                                        <CardTitle>Quick Actions</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <Button type="button" variant="destructive" onClick={handleDelete} className="w-full">
                                            Delete Pending Clearance Request
                                        </Button>
                                    </CardContent>
                                </Card>
                            )}

                            {/* Warning Card */}
                            <Card className="mt-4 border-yellow-200 bg-yellow-50">
                                <CardContent className="pt-6">
                                    <div className="flex items-start space-x-3">
                                        <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
                                        <div className="space-y-2">
                                            <h4 className="text-sm font-medium text-yellow-800">
                                                Important Notice
                                            </h4>
                                            <p className="text-sm text-yellow-700">
                                                Once clearance is approved, the driver will be archived and removed from your active drivers list.
                                                You will need to contact an association clerk to unarchive the driver if you want to rehire them.
                                            </p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </div>
            <ConfirmDialog
                open={confirmDialogOpen}
                title="Delete Pending Clearance Request?"
                description="This action cannot be undone and will permanently remove the clearance request."
                confirmText="Delete"
                confirmVariant="destructive"
                loading={loading}
                onCancel={handleCancel}
                onConfirm={handleConfirm}
            />
        </AppLayout>
    );
} 