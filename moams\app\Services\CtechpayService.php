<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;

class CtechpayService
{
    private $apiUrl;
    private $apiToken;
    private $registration;
    private $environment;

    public function __construct()
    {
        $this->apiUrl = Config::get('services.ctechpay.api_url');
        $this->apiToken = Config::get('services.ctechpay.token');
        $this->registration = Config::get('services.ctechpay.registration');
        $this->environment = Config::get('services.ctechpay.environment');
    }

    /**
     * Create a card payment order
     */
    public function createCardPayment(array $paymentData): array
    {
        try {
            Log::info('Creating ctechpay card payment order', $paymentData);

            $cardEndpoint = Config::get('services.ctechpay.card_payment_endpoint');
            $response = Http::timeout(30)
                ->asForm()
                ->post($this->apiUrl . $cardEndpoint, [
                    'token' => $this->apiToken,
                    'amount' => $paymentData['amount'],
                    'merchantAttributes' => true,
                    'redirectUrl' => $paymentData['redirect_url'],
                    'cancelUrl' => $paymentData['cancel_url'],
                    'cancelText' => 'Cancel Payment',
                ]);

            if ($response->successful()) {
                $responseData = $response->json();
                Log::info('Ctechpay card payment order created successfully', $responseData);

                return [
                    'success' => true,
                    'order_reference' => $responseData['order_reference'],
                    'payment_page_url' => $responseData['payment_page_URL'],
                    'response_data' => $responseData,
                ];
            } else {
                Log::error('Ctechpay card payment order creation failed', [
                    'status' => $response->status(),
                    'body' => $response->body(),
                ]);

                return $this->handleApiError($response);
            }
        } catch (\Exception $e) {
            Log::error('Ctechpay card payment order creation exception', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return $this->handleException($e);
        }
    }



    /**
     * Check payment status
     */
    public function checkPaymentStatus(string $orderReference): array
    {
        try {
            Log::info('Checking ctechpay payment status', ['order_reference' => $orderReference]);

            $statusEndpoint = Config::get('services.ctechpay.status_check_endpoint');
            $response = Http::timeout(30)
                ->asForm()
                ->post($this->apiUrl . $statusEndpoint, [
                    'token' => $this->apiToken,
                    'orderRef' => $orderReference,
                    'registration' => $this->registration, // Add registration parameter
                ]);

            if ($response->successful()) {
                $responseData = $response->json();
                Log::info('Ctechpay payment status retrieved successfully', ['response' => $responseData]);

                return [
                    'success' => true,
                    'status' => $responseData['status'] ?? 'UNKNOWN',
                    'response_data' => $responseData,
                ];
            } else {
                $responseBody = $response->body();
                Log::error('Ctechpay payment status check failed', [
                    'status' => $response->status(),
                    'body' => $responseBody,
                    'order_reference' => $orderReference,
                ]);

                // Handle specific authentication failure
                if ($responseBody === 'User authentication failed') {
                    return [
                        'success' => false,
                        'error' => 'API authentication failed - please contact support',
                    ];
                }

                return $this->handleApiError($response);
            }
        } catch (\Exception $e) {
            Log::error('Ctechpay payment status check exception', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'order_reference' => $orderReference,
            ]);

            return $this->handleException($e);
        }
    }

    /**
     * Validate webhook signature (if ctechpay provides signature validation)
     */
    public function validateWebhookSignature(array $payload, string $signature = null): bool
    {
        // Implement webhook signature validation if ctechpay provides it
        // For now, we'll return true as ctechpay documentation doesn't specify signature validation
        return true;
    }

    /**
     * Get the real Ctechpay API base URL
     */
    public function getApiUrl(): string
    {
        return $this->apiUrl;
    }

    /**
     * Get the current environment
     */
    public function getEnvironment(): string
    {
        return $this->environment;
    }

    /**
     * Handle API response errors with user-friendly messages
     */
    private function handleApiError($response): array
    {
        $status = $response->status();

        // Return user-friendly messages based on HTTP status codes
        switch ($status) {
            case 400:
                return [
                    'success' => false,
                    'error' => 'Invalid payment information. Please check your details and try again.',
                ];
            case 401:
                return [
                    'success' => false,
                    'error' => 'Payment service authentication failed. Please contact support.',
                ];
            case 403:
                return [
                    'success' => false,
                    'error' => 'Payment service access denied. Please contact support.',
                ];
            case 404:
                return [
                    'success' => false,
                    'error' => 'Payment service endpoint not found. Please try again later.',
                ];
            case 429:
                return [
                    'success' => false,
                    'error' => 'Too many payment requests. Please wait a moment and try again.',
                ];
            case 500:
            case 502:
            case 503:
            case 504:
                return [
                    'success' => false,
                    'error' => 'Payment service is temporarily unavailable. Please try again in a few minutes.',
                ];
            default:
                return [
                    'success' => false,
                    'error' => 'Payment processing failed. Please try again or contact support.',
                ];
        }
    }

    /**
     * Handle exceptions with user-friendly messages
     */
    private function handleException(\Exception $e): array
    {
        $message = $e->getMessage();

        // Handle cURL errors specifically
        if (str_contains($message, 'cURL error')) {
            return $this->handleCurlError($message);
        }

        // Handle timeout errors
        if (str_contains($message, 'timeout') || str_contains($message, 'timed out')) {
            return [
                'success' => false,
                'error' => 'Payment request timed out. Please check your connection and try again.',
            ];
        }

        // Handle connection errors
        if (str_contains($message, 'Connection') || str_contains($message, 'connect')) {
            return [
                'success' => false,
                'error' => 'Unable to connect to payment service. Please check your internet connection and try again.',
            ];
        }

        // Generic exception handling
        return [
            'success' => false,
            'error' => 'Payment service is currently unavailable. Please try again later.',
        ];
    }

    /**
     * Handle specific cURL errors with user-friendly messages
     */
    private function handleCurlError(string $message): array
    {
        // Extract cURL error code if present
        if (preg_match('/cURL error (\d+):/', $message, $matches)) {
            $errorCode = (int) $matches[1];

            switch ($errorCode) {
                case 6: // CURLE_COULDNT_RESOLVE_HOST
                    return [
                        'success' => false,
                        'error' => 'Unable to connect to payment service. Please check your internet connection and try again.',
                    ];
                case 7: // CURLE_COULDNT_CONNECT
                    return [
                        'success' => false,
                        'error' => 'Payment service is unreachable. Please try again in a few minutes.',
                    ];
                case 28: // CURLE_OPERATION_TIMEDOUT
                    return [
                        'success' => false,
                        'error' => 'Payment request timed out. Please try again.',
                    ];
                case 35: // CURLE_SSL_CONNECT_ERROR
                    return [
                        'success' => false,
                        'error' => 'Secure connection to payment service failed. Please try again.',
                    ];
                default:
                    return [
                        'success' => false,
                        'error' => 'Payment service connection failed. Please try again later.',
                    ];
            }
        }

        // Fallback for unrecognized cURL errors
        return [
            'success' => false,
            'error' => 'Payment service connection failed. Please try again later.',
        ];
    }
}
