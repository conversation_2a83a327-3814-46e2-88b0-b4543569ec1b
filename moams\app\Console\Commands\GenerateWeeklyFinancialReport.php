<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Http\Controllers\PaymentManagementController;
use Illuminate\Http\Request;

class GenerateWeeklyFinancialReport extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'reports:weekly-financial 
                            {--year= : Year for the report (default: current year)}
                            {--month= : Month for the report (default: current month)}
                            {--output= : Output format (console|pdf) default: console}';

    /**
     * The console command description.
     */
    protected $description = 'Generate weekly financial report for a specific month and year';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $year = $this->option('year') ?: now()->year;
        $month = $this->option('month') ?: now()->month;
        $output = $this->option('output') ?: 'console';

        $this->info("Generating weekly financial report for {$year}-{$month}...");

        try {
            $controller = new PaymentManagementController();
            
            // Create a mock request
            $request = new Request([
                'period' => 'weekly',
                'year' => $year,
                'month' => $month,
            ]);

            if ($output === 'pdf') {
                $this->info('Generating PDF report...');
                $response = $controller->generateFinancialReportPdf($request);
                
                // Save PDF to storage
                $filename = "weekly_financial_report_{$year}_{$month}.pdf";
                $path = storage_path("app/reports/{$filename}");
                
                // Ensure directory exists
                if (!file_exists(dirname($path))) {
                    mkdir(dirname($path), 0755, true);
                }
                
                file_put_contents($path, $response->getContent());
                $this->info("PDF report saved to: {$path}");
                
            } else {
                // Console output
                $data = $this->getWeeklyReportData($year, $month);
                $this->displayWeeklyReport($data, $year, $month);
            }

        } catch (\Exception $e) {
            $this->error("Failed to generate report: " . $e->getMessage());
            return 1;
        }

        return 0;
    }

    /**
     * Get weekly report data
     */
    private function getWeeklyReportData($year, $month)
    {
        $controller = new PaymentManagementController();
        
        // Use reflection to access private method
        $reflection = new \ReflectionClass($controller);
        $method = $reflection->getMethod('getWeeklyReports');
        $method->setAccessible(true);
        
        return $method->invoke($controller, $year, $month);
    }

    /**
     * Display weekly report in console
     */
    private function displayWeeklyReport($data, $year, $month)
    {
        $monthName = \Carbon\Carbon::create($year, $month, 1)->format('F Y');
        
        $this->info("=== WEEKLY FINANCIAL REPORT - {$monthName} ===");
        $this->newLine();

        if ($data->isEmpty()) {
            $this->warn('No payment data found for the specified period.');
            return;
        }

        // Calculate totals
        $totalPayments = $data->sum('total_payments');
        $totalAmount = $data->sum('total_amount');
        $totalRegistration = $data->sum('registration_amount');
        $totalAffiliation = $data->sum('affiliation_amount');

        // Display summary
        $this->info('SUMMARY:');
        $this->line("Total Payments: {$totalPayments}");
        $this->line("Total Revenue: MWK " . number_format($totalAmount, 2));
        $this->line("Registration Fees: MWK " . number_format($totalRegistration, 2));
        $this->line("Affiliation Fees: MWK " . number_format($totalAffiliation, 2));
        $this->newLine();

        // Display weekly breakdown
        $this->info('WEEKLY BREAKDOWN:');
        
        $headers = ['Week Period', 'Payments', 'Total Amount', 'Registration', 'Affiliation'];
        $rows = [];

        foreach ($data as $week) {
            $weekStart = \Carbon\Carbon::parse($week->week_start)->format('M j');
            $weekEnd = \Carbon\Carbon::parse($week->week_end)->format('M j');
            $weekPeriod = "{$weekStart} - {$weekEnd}";
            
            $rows[] = [
                $weekPeriod,
                number_format($week->total_payments),
                'MWK ' . number_format($week->total_amount, 2),
                'MWK ' . number_format($week->registration_amount, 2),
                'MWK ' . number_format($week->affiliation_amount, 2),
            ];
        }

        $this->table($headers, $rows);
        
        // Display average per week
        $weekCount = $data->count();
        if ($weekCount > 0) {
            $this->newLine();
            $this->info('AVERAGES PER WEEK:');
            $this->line("Average Payments: " . number_format($totalPayments / $weekCount, 1));
            $this->line("Average Revenue: MWK " . number_format($totalAmount / $weekCount, 2));
        }
    }
}
