<?php

namespace App\Http\Controllers;

use App\Models\Minibus;
use App\Http\Requests\StoreMinibusRequest;
use App\Http\Requests\UpdateMinibusRequest;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use App\Models\MinibusTransferRequest;
use App\Models\MinibusOwnershipHistory;
use Illuminate\Support\Facades\Storage;

class MinibusController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Get the authenticated user's role name
     */
    private function getUserRole()
    {
        $user = auth()->user();
        return $user ? $user->roles->first()?->name : null;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        if (!$user) {
            return redirect()->route('login');
        }

        $query = Minibus::with([
            'minibusOwner',
            'route',
            'ownershipHistory' => function ($query) {
                $query->where('transfer_type', '!=', 'external');
            }
        ]);

        // Handle search
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('number_plate', 'like', "%{$search}%")
                    ->orWhere('make', 'like', "%{$search}%")
                    ->orWhere('model', 'like', "%{$search}%")
                    ->orWhere('year', 'like', "%{$search}%")
                    ->orWhereHas('minibusOwner', function ($ownerQuery) use ($search) {
                        $ownerQuery->where('first_name', 'like', "%{$search}%")
                            ->orWhere('last_name', 'like', "%{$search}%")
                            ->orWhereRaw("CONCAT(first_name, ' ', last_name) like ?", ["%{$search}%"]);
                    })
                    ->orWhereHas('route', function ($routeQuery) use ($search) {
                        $routeQuery->where('route_name', 'like', "%{$search}%");
                    });
            });
        }

        // Handle status filter
        if ($request->has('status') && $request->status !== 'all') {
            if ($request->status === 'active') {
                $query->where('archived', false);
            } elseif ($request->status === 'archived') {
                $query->where('archived', true);
            }
        }

        if ($user->hasRole('association clerk')) {
            // Association clerk can see all minibuses
            $minibuses = $query->orderBy('created_at', 'desc')->paginate(10);
            // Fetch all transfer requests with related minibus and owner
            $transferRequests = MinibusTransferRequest::with(['minibus', 'owner'])->get();
        } elseif ($user->hasRole('minibus owner')) {
            // Minibus owners can only see their own minibuses
            $query->where('owner_id', $user->id);
            if (!$request->has('status') || $request->status !== 'archived') {
                $query->where('archived', false);
            }
            $minibuses = $query->orderBy('created_at', 'desc')->paginate(10);
            $transferRequests = collect(); // empty collection
        } else {
            // Other users can only see non-archived minibuses
            if (!$request->has('status') || $request->status !== 'archived') {
                $query->where('archived', false);
            }
            $minibuses = $query->orderBy('created_at', 'desc')->paginate(10);
            $transferRequests = collect(); // empty collection
        }

        // Transform the paginated data
        $minibuses->getCollection()->transform(function ($minibus) {
            $minibus->internal_transfers_count = $minibus->ownershipHistory->count();
            // Ensure minibusOwner is included as array (or null)
            $minibusArr = $minibus->toArray();
            $minibusArr['minibusOwner'] = $minibus->minibusOwner
                ? [
                    'id' => $minibus->minibusOwner->id,
                    'first_name' => $minibus->minibusOwner->first_name,
                    'last_name' => $minibus->minibusOwner->last_name,
                    'email' => $minibus->minibusOwner->email,
                ]
                : null;
            $minibusArr['internal_transfers_count'] = $minibus->internal_transfers_count;
            return $minibusArr;
        });

        return Inertia::render('MinibusManagement/index-minibus', [
            'minibuses' => $minibuses,
            'transferRequests' => $transferRequests,
            'userRole' => $this->getUserRole(),
            'auth' => [
                'user' => $user,
            ],
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        if (!auth()->user()) {
            return redirect()->route('login');
        }

        return Inertia::render('MinibusManagement/create-minibus', [
            'userRole' => $this->getUserRole(),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreMinibusRequest $request)
    {
        $user = auth()->user();
        if (!$user) {
            return redirect()->route('login');
        }

        $ownerId = $user->hasRole('association clerk') ? $request->owner_id : $user->id;
        $data = $request->only([
            'number_plate',
            'make',
            'model',
            'year_of_make',
            'main_colour',
            'route_id',
        ]);
        $data['owner_id'] = $ownerId;
        // Handle file upload
        if ($request->hasFile('proof_of_ownership')) {
            $file = $request->file('proof_of_ownership');
            $hash = md5_file($file->getRealPath());
            $existing = Minibus::where('proof_of_ownership', 'like', "%$hash%")
                ->first();
            if ($existing) {
                $data['proof_of_ownership'] = $existing->proof_of_ownership;
            } else {
                $filename = $hash . '.' . $file->getClientOriginalExtension();
                $data['proof_of_ownership'] = $file->storeAs('ownership_docs', $filename, 'public');
            }
        }
        $minibus = Minibus::create($data);
        return redirect()->route('minibuses.index')->with('success', 'Minibus details saved successfully');
    }

    /**
     * Display the specified resource.
     */
    public function show(Minibus $minibus)
    {
        if (!auth()->user()) {
            return redirect()->route('login');
        }

        $minibus->load('minibusOwner', 'route');
        $minibus->refresh();

        $history = MinibusOwnershipHistory::where('minibus_id', $minibus->id)
            ->where('status', 'completed')
            ->with(['previous_owner', 'new_owner'])
            ->orderBy('created_at', 'desc')
            ->get();

        // Ensure minibusOwner is included as array (or null)
        $minibusArr = $minibus->toArray();
        $minibusArr['minibusOwner'] = $minibus->minibusOwner
            ? [
                'id' => $minibus->minibusOwner->id,
                'first_name' => $minibus->minibusOwner->first_name,
                'last_name' => $minibus->minibusOwner->last_name,
                'email' => $minibus->minibusOwner->email,
            ]
            : null;

        return Inertia::render('MinibusManagement/show-minibus', [
            'minibus' => $minibusArr,
            'userRole' => $this->getUserRole(),
            'history' => $history,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Minibus $minibus)
    {
        if (!auth()->user()) {
            return redirect()->route('login');
        }

        // Ensure we have the latest data including the archived status
        $minibus->load('minibusOwner');
        $minibus->refresh();

        return Inertia::render('MinibusManagement/edit-minibus', [
            'minibus' => array_merge($minibus->toArray(), [
                'archived' => (bool) $minibus->archived,
            ]),
            'userRole' => $this->getUserRole(),
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateMinibusRequest $request, Minibus $minibus)
    {
        $user = auth()->user();
        if (!$user) {
            return redirect()->route('login');
        }

        $this->authorize('update', $minibus);

        $data = $request->only([
            'number_plate',
            'make',
            'model',
            'year_of_make',
            'main_colour',
            'route_id',
        ]);
        // Handle owner update for association clerks
        if ($user->hasRole('association clerk')) {
            if ($request->has('owner_id')) {
                $data['owner_id'] = $request->owner_id;
                // If setting a new owner, ensure the minibus is not archived
                if ($request->owner_id && $minibus->archived) {
                    $data['archived'] = false;
                }
            }
        }
        // Handle file upload if present
        if ($request->hasFile('proof_of_ownership')) {
            $file = $request->file('proof_of_ownership');
            $hash = md5_file($file->getRealPath());
            $existing = Minibus::where('proof_of_ownership', 'like', "%$hash%")
                ->first();
            if ($existing) {
                $data['proof_of_ownership'] = $existing->proof_of_ownership;
            } else {
                $filename = $hash . '.' . $file->getClientOriginalExtension();
                $data['proof_of_ownership'] = $file->storeAs('ownership_docs', $filename, 'public');
            }
        }
        $minibus->update($data);
        return redirect()->route('minibuses.show', $minibus)
            ->with('success', 'Minibus updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Minibus $minibus)
    {
        if (!auth()->user()) {
            return redirect()->route('login');
        }

        $this->authorize('delete', $minibus);
        $minibus->delete();
        return redirect()->route('minibuses.index')->with('success', 'Minibus deleted successfully.');
    }

    public function archive(Minibus $minibus)
    {
        if (!auth()->user()) {
            return redirect()->route('login');
        }

        $this->authorize('archive', $minibus);
        $minibus->update(['archived' => true]);
        return redirect()->back()->with('success', 'Minibus archived successfully.');
    }

    public function unarchive(Minibus $minibus)
    {
        if (!auth()->user()) {
            return redirect()->route('login');
        }

        $this->authorize('unarchive', $minibus);
        $minibus->update(['archived' => false]);
        return redirect()->back()->with('success', 'Minibus unarchived successfully.');
    }

    // API endpoint to fetch all minibuses as JSON
    public function apiIndex()
    {
        $user = auth()->user();
        $query = Minibus::query();
        if ($user && $user->hasRole('minibus owner')) {
            $query->where('owner_id', $user->id);
        }
        $minibuses = $query->orderBy('number_plate')->get(['id', 'number_plate', 'owner_id']);
        return response()->json($minibuses);
    }
}
