import{j as e,C as a,a as l,b as r,c as d,B as i,L as c,T as n,R as x}from"./app-DL-qYY5V.js";import{A as h,h as o}from"./app-layout-YqstQnqE.js";import{C as j}from"./circle-x-DliR3-rL.js";import{A as u}from"./arrow-left-DCW23wrL.js";import"./index-1tPPImJd.js";import"./index-BTzg1GwG.js";import"./app-logo-icon-BnXlkpcX.js";import"./bus-CKTBKI0T.js";import"./dollar-sign-Cx0-nQIX.js";import"./users-DNGXY-sJ.js";import"./clipboard-list-CxRumT8v.js";function A({payment:s,associationClerk:t}){const m=[{title:"Dashboard",href:"/dashboard"},{title:"My Membership",href:"/my-membership"},{title:"Payment Cancelled",href:"#"}];return e.jsx(h,{title:"Payment Cancelled",breadcrumbs:m,children:e.jsxs("div",{className:"max-w-2xl mx-auto py-8 px-4",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("div",{className:"flex justify-center mb-4",children:e.jsx("div",{className:"w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center",children:e.jsx(j,{className:"h-10 w-10 text-yellow-600"})})}),e.jsx("h1",{className:"text-3xl font-bold text-yellow-700 mb-2",children:"Payment Cancelled"}),e.jsx("p",{className:"text-gray-600",children:"You cancelled the payment process"})]}),e.jsxs(a,{className:"mb-6 border-yellow-200 bg-yellow-50",children:[e.jsx(l,{children:e.jsxs(r,{className:"flex items-center gap-2 text-yellow-800",children:[e.jsx(o,{className:"h-5 w-5"}),"Payment Details"]})}),e.jsxs(d,{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Amount"}),e.jsxs("div",{className:"font-semibold text-yellow-800",children:["MWK ",parseFloat(s.amount).toLocaleString()]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Fee Type"}),e.jsx("div",{className:"font-semibold text-yellow-800 capitalize",children:s.fee_type})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Payment Method"}),e.jsx("div",{className:"font-semibold text-yellow-800",children:s.payment_method==="ctechpay_card"?"Card Payment":s.payment_method})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Cancelled At"}),e.jsx("div",{className:"font-semibold text-yellow-800",children:new Date(s.updated_at||s.created_at).toLocaleString()})]})]}),s.ctechpay_order_reference&&e.jsxs("div",{className:"pt-4 border-t border-yellow-200",children:[e.jsx("div",{className:"text-sm text-gray-600 mb-2",children:"Order Reference"}),e.jsx("div",{className:"font-mono text-sm text-yellow-800",children:s.ctechpay_order_reference})]})]})]}),e.jsxs(a,{className:"mb-6",children:[e.jsx(l,{children:e.jsx(r,{children:"What Happened?"})}),e.jsx(d,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{className:"text-gray-700",children:"You chose to cancel the payment process before it was completed. No charges have been made to your account or payment method."}),e.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5",children:e.jsx("span",{className:"text-blue-600 text-sm",children:"ℹ"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-blue-800",children:"No Payment Processed"}),e.jsx("div",{className:"text-sm text-blue-700 mt-1",children:"Your membership status remains unchanged, and you can try the payment again at any time."})]})]})})]})})]}),e.jsxs(a,{className:"mb-6",children:[e.jsx(l,{children:e.jsx(r,{children:"What's Next?"})}),e.jsx(d,{children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5",children:e.jsx("span",{className:"text-blue-600 text-sm font-semibold",children:"1"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:"Try Payment Again"}),e.jsx("div",{className:"text-sm text-gray-600",children:"You can retry the payment process with the same or different payment method"})]})]}),e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5",children:e.jsx("span",{className:"text-blue-600 text-sm font-semibold",children:"2"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:"Alternative Payment Methods"}),e.jsx("div",{className:"text-sm text-gray-600",children:"Consider using a different payment method if you encountered issues"})]})]}),e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5",children:e.jsx("span",{className:"text-blue-600 text-sm font-semibold",children:"3"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:"Contact Support"}),e.jsx("div",{className:"text-sm text-gray-600",children:"If you need assistance or have questions about the payment process"})]})]})]})})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[e.jsx(i,{asChild:!0,variant:"outline",children:e.jsxs(c,{href:n("dashboard"),children:[e.jsx(u,{className:"h-4 w-4 mr-2"}),"Return to Dashboard"]})}),e.jsx(i,{asChild:!0,className:"bg-blue-600 hover:bg-blue-700",children:e.jsxs(c,{href:n("my.membership"),children:[e.jsx(x,{className:"h-4 w-4 mr-2"}),"Try Payment Again"]})})]}),e.jsx("div",{className:"mt-8 text-center text-sm text-gray-500",children:t?e.jsxs("p",{children:["Need help with payments? Contact ",t.first_name," ",t.last_name," at"," ",e.jsx("a",{href:`mailto:${t.email}`,className:"text-blue-600 hover:underline",children:t.email})," ","or call ",t.phone_number]}):e.jsx("p",{children:"Need help with payments? Contact MOAM support for assistance."})})]})})}export{A as default};
