import{r as o,j as e,B as b}from"./app-DL-qYY5V.js";import{P as g,a as w,b as y,C as E,c as I,d as N,e as L,f as P,g as R}from"./minibus-owner-combobox-l9syvT7Z.js";import{L as D}from"./loader-circle-D3J3XKS7.js";import{a as O}from"./app-layout-YqstQnqE.js";import{C as T}from"./index-1tPPImJd.js";function q(n,d){const[m,r]=o.useState(n);return o.useEffect(()=>{const a=setTimeout(()=>r(n),d);return()=>clearTimeout(a)},[n,d]),m}function U({value:n,onChange:d,placeholder:m="Select route...",fetchRoutes:r}){const[a,f]=o.useState(!1),[p,S]=o.useState(""),[x,u]=o.useState(!1),[C,j]=o.useState([]),l=q(p,300);return o.useEffect(()=>{a&&p===""&&(u(!0),r("").then(t=>{const h=t.map(s=>({...s,id:String(s.id)})),i=[],c=new Set;for(const s of h)c.has(s.id)||(i.push(s),c.add(s.id));j(i),u(!1)}))},[a]),o.useEffect(()=>{l!==""&&(u(!0),r(l).then(t=>{const h=t.map(s=>({...s,id:String(s.id)})),i=[],c=new Set;for(const s of h)c.has(s.id)||(i.push(s),c.add(s.id));j(i),u(!1)}))},[l,r]),e.jsxs(g,{open:a,onOpenChange:f,children:[e.jsx(w,{asChild:!0,children:e.jsxs(b,{variant:"outline",role:"combobox","aria-expanded":a,className:"w-full justify-between h-9 px-3 py-1 text-base rounded-md border border-input bg-transparent font-normal",children:[n?n.name||m:e.jsx("span",{className:"text-muted-foreground",children:m}),a&&x?e.jsx(D,{className:"ml-2 h-4 w-4 animate-spin opacity-50"}):e.jsx(O,{className:"ml-2 h-4 w-4 opacity-50"})]})}),e.jsx(y,{className:"w-full p-0",children:e.jsxs(E,{children:[e.jsx(I,{placeholder:"Search route...",value:p,onValueChange:S}),e.jsxs(N,{children:[e.jsx(L,{children:x?"Searching...":"Routes not found."}),e.jsx(P,{children:C.map(t=>e.jsxs(R,{value:t.name,onSelect:()=>{d(t),f(!1)},children:[e.jsx(T,{className:n&&n.id===t.id?"mr-2 h-4 w-4 opacity-100":"mr-2 h-4 w-4 opacity-0"}),t.name]},t.id))})]})]})})]})}export{U as R};
