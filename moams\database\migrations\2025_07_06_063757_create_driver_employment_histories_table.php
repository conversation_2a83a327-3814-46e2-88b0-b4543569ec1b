<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('driver_employment_histories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('driver_id')->constrained('drivers')->onDelete('cascade');
            $table->foreignId('previous_owner_id')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('new_owner_id')->nullable()->constrained('users')->onDelete('set null');
            $table->enum('employment_change_type', ['hired', 'cleared', 'rehired'])->default('hired');
            $table->enum('status', ['active', 'completed', 'pending'])->default('completed');
            $table->text('reason')->nullable();
            $table->date('employment_start_date')->nullable();
            $table->date('employment_end_date')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('driver_employment_histories');
    }
};
