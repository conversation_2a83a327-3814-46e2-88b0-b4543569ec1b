import{r as i,d as J,j as e,H as j,e as c,L as F,B as x,C as g,a as L,b as M,c as f}from"./app-DL-qYY5V.js";import{I as K}from"./input-Dm4SEXxy.js";import{L as v}from"./label-e3QxUH-L.js";import{S as k,a as E,b as P,c as R,e as $,d as n}from"./select-Cp8NjZe8.js";import{A as N,T as Q,B as u,b as X,c as Y,d as Z}from"./app-layout-YqstQnqE.js";import{C as ee}from"./confirm-dialog-B1e93Onq.js";import{P as se}from"./pagination-CL_CHA67.js";import{P as ae}from"./plus-CU6rIcI2.js";import{S as re}from"./search-B4sum6Qx.js";import{F as te}from"./filter-DPFCjsfq.js";import{E as y}from"./eye-sp8vtjJC.js";import"./index-BTzg1GwG.js";import"./index-1tPPImJd.js";import"./index-uC6ZAdKJ.js";import"./app-logo-icon-BnXlkpcX.js";import"./bus-CKTBKI0T.js";import"./dollar-sign-Cx0-nQIX.js";import"./users-DNGXY-sJ.js";import"./clipboard-list-CxRumT8v.js";import"./dialog-CXtul0Wy.js";import"./chevron-left-DFeVEtK7.js";function Le(){var A,_,U,T;const[o,b]=i.useState(""),[d,w]=i.useState("all"),[m,S]=i.useState("active"),[a,p]=i.useState({open:!1,action:null,user:null}),[D,C]=i.useState(!1);try{const{users:r,statistics:le}=J().props,h=(r==null?void 0:r.data)||r||[],t=r!=null&&r.data?r:null;if(!r)return e.jsxs(N,{children:[e.jsx(j,{title:"User Management"}),e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx("div",{className:"text-center",children:"Loading..."})})]});const ne=(s,l)=>{p({open:!0,action:s,user:l})},B=()=>p({open:!1,action:null,user:null}),V=async()=>{if(!a.user)return;C(!0);const{id:s,first_name:l,last_name:ce,archived_at:oe}=a.user;(a.action==="archive"||a.action==="unarchive")&&await c.put(`/admin/users/${s}/${a.action}`),C(!1),p({open:!1,action:null,user:null})},I=[{title:"Dashboard",href:"/dashboard"},{title:"User Management",href:"/admin/users"}],H=s=>{b(s),c.get("/admin/users",{search:s,role:d,status:m},{preserveState:!0,preserveScroll:!0})},z=s=>{w(s),c.get("/admin/users",{search:o,role:s,status:m},{preserveState:!0,preserveScroll:!0})},G=s=>{S(s),c.get("/admin/users",{search:o,role:d,status:s},{preserveState:!0,preserveScroll:!0})},O=s=>{switch(s){case"system admin":return"bg-red-100 text-red-800 border-red-200";case"minibus owner":return"bg-green-100 text-green-800 border-green-200";case"association clerk":return"bg-purple-100 text-purple-800 border-purple-200";case"association manager":return"bg-orange-100 text-orange-800 border-orange-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}},W=s=>new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),q=h.filter(s=>s.roles&&s.roles.some(l=>l.name==="system admin")&&!s.archived_at),ie=s=>s.roles&&s.roles.some(l=>l.name==="system admin")&&q.length===1;return e.jsxs(N,{breadcrumbs:I,children:[e.jsx(j,{title:"User Management"}),e.jsxs("div",{className:"w-full max-w-full overflow-hidden",children:[e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex justify-between items-center mb-8",children:[e.jsx("div",{children:e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Manage all system users, their roles, and permissions"})}),e.jsx(F,{href:"/admin/create-user",children:e.jsxs(x,{className:"bg-blue-600 hover:bg-blue-700",children:[e.jsx(ae,{className:"h-4 w-4 mr-2"}),"Create User"]})})]}),e.jsxs(g,{className:"mb-6",children:[e.jsx(L,{children:e.jsx(M,{className:"text-lg",children:"Filters & Search"})}),e.jsx(f,{children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsxs("div",{children:[e.jsx(v,{htmlFor:"search",children:"Search Users"}),e.jsxs("div",{className:"relative",children:[e.jsx(re,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),e.jsx(K,{id:"search",placeholder:"Search by name, email, or phone...",value:o,onChange:s=>H(s.target.value),className:"pl-10"})]})]}),e.jsxs("div",{children:[e.jsx(v,{htmlFor:"role-filter",children:"Filter by Role"}),e.jsxs(k,{value:d,onValueChange:z,children:[e.jsx(E,{children:e.jsx(P,{placeholder:"All roles"})}),e.jsx(R,{children:e.jsxs($,{children:[e.jsx(n,{value:"all",children:"All Roles"}),e.jsx(n,{value:"system admin",children:"System Admin"}),e.jsx(n,{value:"minibus owner",children:"Minibus Owner"}),e.jsx(n,{value:"association clerk",children:"Association Clerk"}),e.jsx(n,{value:"association manager",children:"Association Manager"})]})})]})]}),e.jsxs("div",{children:[e.jsx(v,{htmlFor:"status-filter",children:"Account Status"}),e.jsxs(k,{value:m,onValueChange:G,children:[e.jsx(E,{children:e.jsx(P,{placeholder:"Active"})}),e.jsx(R,{children:e.jsxs($,{children:[e.jsx(n,{value:"active",children:"Active"}),e.jsx(n,{value:"inactive",children:"Inactive"}),e.jsx(n,{value:"all",children:"All"})]})})]})]}),e.jsx("div",{className:"flex items-end",children:e.jsxs(x,{variant:"outline",onClick:()=>{b(""),w("all"),S("active"),c.get("/admin/users")},className:"w-full",children:[e.jsx(te,{className:"h-4 w-4 mr-2"}),"Clear Filters"]})})]})})]}),e.jsx("div",{className:"w-full",style:{maxWidth:"calc(100vw - 2rem)",overflow:"hidden"},children:e.jsxs(g,{className:"w-full border-0 shadow-none",children:[e.jsx(L,{className:"px-0",children:e.jsxs(M,{className:"flex items-center gap-2",children:[e.jsx(y,{className:"h-5 w-5"}),"Users (",(t==null?void 0:t.total)||h.length,")"]})}),e.jsx(f,{className:"p-0",children:h.length>0?e.jsx("div",{className:"overflow-x-auto mb-8",children:e.jsx(Q,{children:e.jsxs("table",{className:"min-w-full w-full bg-white border border-gray-200 rounded-lg",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"bg-gray-100 text-gray-700",children:[e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Name"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Email"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Phone"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Gender"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Roles"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Status"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Created"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Actions"})]})}),e.jsx("tbody",{children:h.map(s=>e.jsxs("tr",{className:"border-b hover:bg-gray-50",children:[e.jsxs("td",{className:"px-4 py-3",children:[s.first_name||"Unknown"," ",s.last_name||"User"]}),e.jsx("td",{className:"px-4 py-3",children:s.email}),e.jsx("td",{className:"px-4 py-3",children:s.phone_number||"N/A"}),e.jsx("td",{className:"px-4 py-3 capitalize",children:s.gender||"N/A"}),e.jsx("td",{className:"px-4 py-3",children:e.jsx("div",{className:"flex flex-wrap gap-1",children:s.roles&&Array.isArray(s.roles)?s.roles.map(l=>e.jsx(u,{variant:"outline",className:`text-xs ${O(l.name)}`,children:l.name},l.id)):e.jsx(u,{variant:"outline",className:"text-xs",children:"No roles assigned"})})}),e.jsx("td",{className:"px-4 py-3",children:s.archived_at?e.jsx(u,{variant:"destructive",children:"Archived"}):e.jsx(u,{className:"bg-green-100 text-green-800",children:"Active"})}),e.jsx("td",{className:"px-4 py-3",children:s.created_at?W(s.created_at):"N/A"}),e.jsx("td",{className:"px-4 py-3",children:e.jsx("div",{className:"flex items-center gap-2",children:e.jsxs(X,{children:[e.jsx(Y,{asChild:!0,children:e.jsx(F,{href:`/admin/users/${s.id}`,children:e.jsx(x,{variant:"outline",size:"sm",children:e.jsx(y,{className:"h-4 w-4"})})})}),e.jsx(Z,{children:e.jsx("p",{children:"View user details"})})]})})})]},s.id))})]})})}):e.jsxs("div",{className:"text-center py-8 px-6",children:[e.jsx(y,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),e.jsx("h3",{className:"text-lg font-medium mb-2",children:"No users found"}),e.jsx("p",{className:"text-muted-foreground mb-4",children:o||d!=="all"||m!=="all"?"Try adjusting your search or filters":"No users have been registered yet"})]})})]})}),t&&t.total>0&&t.total>t.per_page&&e.jsx("div",{className:"mt-6 w-full",children:e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 px-4 py-4 border-t bg-gray-50/50 rounded-b-lg",children:[e.jsxs("div",{className:"text-sm text-gray-700 text-center sm:text-left",children:["Showing ",t.from," to ",t.to," of ",t.total," results"]}),t.last_page>1&&e.jsx(se,{data:t})]})})]}),e.jsx(ee,{open:a.open,title:a.action==="archive"?`Archive ${(A=a.user)==null?void 0:A.first_name} ${(_=a.user)==null?void 0:_.last_name}?`:a.action==="unarchive"?`Unarchive ${(U=a.user)==null?void 0:U.first_name} ${(T=a.user)==null?void 0:T.last_name}?`:"Are you sure?",description:a.action==="archive"?"This user will be archived and will not be able to access the system.":a.action==="unarchive"?"This user will be restored and regain access to the system.":"",confirmText:a.action==="archive"?"Archive":a.action==="unarchive"?"Unarchive":"Confirm",confirmVariant:a.action==="archive"?"destructive":"default",loading:D,onCancel:B,onConfirm:V})]})]})}catch(r){return console.error("Error in UserManagement component:",r),e.jsxs(N,{children:[e.jsx(j,{title:"User Management"}),e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx(g,{children:e.jsxs(f,{className:"p-8 text-center",children:[e.jsx("h2",{className:"text-xl font-semibold text-red-600 mb-2",children:"Error Loading Users"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"There was an error loading the user management page."}),e.jsxs("p",{className:"text-sm text-gray-500",children:["Error: ",r.message]}),e.jsx(x,{onClick:()=>window.location.reload(),className:"mt-4",children:"Reload Page"})]})})})]})}}export{Le as default};
