<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateRouteRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'unique:vehicle_routes,name,' . $this->route('route'),
                'regex:/^[A-Za-z\s]+ - [A-Za-z\s]+$/'
            ],
            'status' => 'required|in:active,inactive',
        ];
    }

    public function messages(): array
    {
        return [
            'name.regex' => 'Route name must follow the pattern "Location - Location" (e.g., "Ntcheu - Chingeni"). Use exactly one space before and after the dash.',
        ];
    }
}
