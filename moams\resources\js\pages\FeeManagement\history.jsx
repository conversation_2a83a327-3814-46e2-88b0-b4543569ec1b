import React from 'react';
import { Link } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, DollarSign, Calendar, User } from 'lucide-react';

export default function FeeHistory({ feeType, history }) {
    const breadcrumbs = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Fee Management', href: '/fee-settings' },
        { title: `${feeType.charAt(0).toUpperCase() + feeType.slice(1)} Fee History`, href: `/fee-settings/history/${feeType}` },
    ];

    const getStatusBadge = (isActive) => {
        return isActive ? (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Active
            </span>
        ) : (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                Inactive
            </span>
        );
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <div className="p-2 sm:p-4 md:p-8">
                <div className="flex flex-col sm:flex-row gap-2 sm:gap-4 items-center mb-4 sm:mb-6">
                    <div className="flex items-center gap-2">
                        <Link href="/fee-settings">
                            <Button variant="outline" className="w-full sm:w-auto">
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                Back to Fee Management
                            </Button>
                        </Link>
                        <span className="text-muted-foreground text-base font-medium">
                            View complete history of {feeType} fee changes.
                        </span>
                    </div>
                </div>

                <div className="w-full mx-auto">
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                {feeType.charAt(0).toUpperCase() + feeType.slice(1)} Fee History
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            {history.length > 0 ? (
                                <div className="space-y-4">
                                    {history.map((fee, index) => (
                                        <Card key={fee.id} className="border-l-4 border-l-blue-500">
                                            <CardContent className="p-4">
                                                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                                                    <div className="flex-1">
                                                        <div className="flex items-center gap-2 mb-2">
                                                            <DollarSign className="h-4 w-4 text-green-600" />
                                                            <span className="text-lg font-semibold">
                                                                ${fee.amount.toFixed(2)}
                                                            </span>
                                                            {getStatusBadge(fee.is_active)}
                                                        </div>

                                                        {fee.description && (
                                                            <p className="text-sm text-gray-600 mb-2">
                                                                {fee.description}
                                                            </p>
                                                        )}

                                                        <div className="flex flex-col sm:flex-row sm:items-center gap-4 text-sm text-gray-500">
                                                            <div className="flex items-center gap-1">
                                                                <Calendar className="h-3 w-3" />
                                                                <span>Effective: {new Date(fee.effective_from).toLocaleDateString()}</span>
                                                            </div>
                                                            <div className="flex items-center gap-1">
                                                                <User className="h-3 w-3" />
                                                                <span>Set by: {fee.created_by_user?.first_name} {fee.created_by_user?.last_name}</span>
                                                            </div>
                                                            <div>
                                                                <span>Created: {new Date(fee.created_at).toLocaleDateString()}</span>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    {index === 0 && fee.is_active && (
                                                        <div className="flex-shrink-0">
                                                            <Link href={route('fee-settings.edit', fee.id)}>
                                                                <Button variant="outline" size="sm">
                                                                    Edit
                                                                </Button>
                                                            </Link>
                                                        </div>
                                                    )}
                                                </div>
                                            </CardContent>
                                        </Card>
                                    ))}
                                </div>
                            ) : (
                                <div className="text-center py-8">
                                    <DollarSign className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                                    <h3 className="text-lg font-medium text-gray-900 mb-2">No Fee History</h3>
                                    <p className="text-gray-500 mb-4">
                                        No {feeType} fee changes have been recorded yet.
                                    </p>
                                    <Link href={route('fee-settings.create')}>
                                        <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                                            Set First Fee
                                        </Button>
                                    </Link>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
} 