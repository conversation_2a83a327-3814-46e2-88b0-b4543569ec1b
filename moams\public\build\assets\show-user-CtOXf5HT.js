import{i as S,d as k,r as b,j as e,H as P,B as h,L as B,C as t,a as i,b as n,c,e as R}from"./app-DL-qYY5V.js";import{n as I}from"./navigation-DAA2N51J.js";import{L as m}from"./label-e3QxUH-L.js";import{A as M,T as E,b as y,c as _,d as w,i as V,j as H,k as q,B as l}from"./app-layout-YqstQnqE.js";import{C as z}from"./confirm-dialog-B1e93Onq.js";import{A as F}from"./arrow-left-DCW23wrL.js";import{S as J}from"./square-pen-BT4HeE62.js";import{A as G}from"./archive-uWgoYkgZ.js";import{M as K,P as O}from"./phone-d7ZPSr1q.js";import{U as Q}from"./users-DNGXY-sJ.js";import{C as W}from"./calendar-Bzuvt9Ns.js";import{S as X}from"./shield-B3ISjw25.js";import"./index-BTzg1GwG.js";import"./index-1tPPImJd.js";import"./app-logo-icon-BnXlkpcX.js";import"./bus-CKTBKI0T.js";import"./dollar-sign-Cx0-nQIX.js";import"./clipboard-list-CxRumT8v.js";import"./dialog-CXtul0Wy.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Y=[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]],Z=S("RotateCcw",Y);function ve(){var g,f,v,N;const{user:s,users:C=[]}=k().props,[r,o]=b.useState({open:!1,action:null,user:null}),[A,u]=b.useState(!1),L=C.filter(a=>a.roles.some(x=>x.name==="system admin")&&!a.archived_at),j=s.roles.some(a=>a.name==="system admin")&&L.length===1,$=(a,x)=>{o({open:!0,action:a,user:x})},T=()=>o({open:!1,action:null,user:null}),U=async()=>{if(!r.user)return;u(!0);const{id:a}=r.user;(r.action==="archive"||r.action==="unarchive")&&await R.put(`/admin/users/${a}/${r.action}`),u(!1),o({open:!1,action:null,user:null})},p=[{title:"Dashboard",href:"/dashboard"},{title:"User Management",href:"/admin/users"},{title:`${s.first_name} ${s.last_name}`,href:`/admin/users/${s.id}`}],D=a=>{switch(a){case"system admin":return"bg-red-100 text-red-800 border-red-200";case"minibus owner":return"bg-green-100 text-green-800 border-green-200";case"association clerk":return"bg-purple-100 text-purple-800 border-purple-200";case"association manager":return"bg-orange-100 text-orange-800 border-orange-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}},d=a=>new Date(a).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});return e.jsxs(M,{breadcrumbs:p,children:[e.jsx(P,{title:`User Details - ${s.first_name} ${s.last_name}`}),e.jsxs("div",{className:"p-8",children:[e.jsxs("div",{className:"flex justify-between items-center mb-8",children:[e.jsx("div",{className:"flex items-center space-x-4",children:e.jsxs(h,{variant:"outline",onClick:()=>I(p),className:"w-full sm:w-auto",children:[e.jsx(F,{className:"h-4 w-4 mr-2"}),"Back"]})}),e.jsx(E,{children:e.jsxs("div",{className:"flex space-x-2",children:[!(j&&!s.archived_at)&&e.jsxs(y,{children:[e.jsx(_,{asChild:!0,children:e.jsx(B,{href:`/admin/users/${s.id}/edit`,children:e.jsx(h,{className:"bg-green-600 hover:bg-green-700",children:e.jsx(J,{className:"h-4 w-4 sm:mr-2"})})})}),e.jsx(w,{children:e.jsx("p",{children:"Edit user information"})})]}),!(j&&!s.archived_at)&&e.jsxs(y,{children:[e.jsx(_,{asChild:!0,children:e.jsx(h,{variant:s.archived_at?"success":"outline",className:"ml-2",onClick:()=>$(s.archived_at?"unarchive":"archive",s),children:s.archived_at?e.jsx(Z,{className:"h-4 w-4 sm:mr-2"}):e.jsx(G,{className:"h-4 w-4 sm:mr-2"})})}),e.jsx(w,{children:e.jsx("p",{children:s.archived_at?"Unarchive user":"Archive user"})})]})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[e.jsxs("div",{className:"lg:col-span-1",children:[e.jsxs(t,{children:[e.jsxs(i,{className:"text-center",children:[e.jsx("div",{className:"flex justify-center mb-4",children:e.jsx(V,{className:"h-24 w-24",children:e.jsx(H,{children:e.jsx(q,{text:`${s.first_name} ${s.last_name}`,className:"text-2xl"})})})}),e.jsxs(n,{className:"text-xl",children:[s.first_name," ",s.last_name]})]}),e.jsxs(c,{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(K,{className:"h-4 w-4 text-gray-500"}),e.jsx("span",{className:"text-sm",children:s.email})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(O,{className:"h-4 w-4 text-gray-500"}),e.jsx("span",{className:"text-sm",children:s.phone_number})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(Q,{className:"h-4 w-4 text-gray-500"}),e.jsx("span",{className:"text-sm capitalize",children:s.gender})]}),s.roles.some(a=>a.name==="minibus owner")&&e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(W,{className:"h-4 w-4 text-gray-500"}),e.jsxs("span",{className:"text-sm",children:["Joined on ",s.joining_date?d(s.joining_date):"N/A"]})]}),s.archived_at&&e.jsx(l,{variant:"destructive",className:"ml-2",children:"Archived"})]})]}),e.jsxs(t,{className:"mt-6",children:[e.jsx(i,{children:e.jsx(n,{children:"Location Information"})}),e.jsx(c,{className:"space-y-4",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"District"}),e.jsx("p",{className:"text-sm",children:s.district})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Village/Town"}),e.jsx("p",{className:"text-sm",children:s.village})]})]})})]})]}),e.jsxs("div",{className:"lg:col-span-2 space-y-6",children:[e.jsxs(t,{children:[e.jsx(i,{children:e.jsxs(n,{className:"flex items-center",children:[e.jsx(X,{className:"h-5 w-5 mr-2"}),"Roles & Permissions"]})}),e.jsx(c,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium mb-2",children:"Assigned Roles:"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:s.roles.map(a=>e.jsx(l,{variant:"outline",className:`${D(a.name)}`,children:a.name},a.id))})]}),s.roles.length===0&&e.jsx("p",{className:"text-gray-500 text-sm",children:"No roles assigned"})]})})]}),e.jsxs(t,{children:[e.jsx(i,{children:e.jsx(n,{children:"Account Information"})}),e.jsx(c,{children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx(m,{className:"text-sm font-medium text-gray-600",children:"Email Verified"}),e.jsx("p",{className:"text-sm mt-1",children:e.jsx(l,{variant:"outline",className:"bg-green-100 text-green-800 border-green-200",children:"Verified"})})]}),e.jsxs("div",{children:[e.jsx(m,{className:"text-sm font-medium text-gray-600",children:"Last Updated"}),e.jsx("p",{className:"text-sm mt-1",children:d(s.updated_at)})]}),e.jsxs("div",{children:[e.jsx(m,{className:"text-sm font-medium text-gray-600",children:"Account Status"}),e.jsx("p",{className:"text-sm mt-1",children:s.archived_at?e.jsx(l,{variant:"destructive",className:"bg-red-100 text-red-800 border-red-200",children:"Inactive"}):e.jsx(l,{variant:"outline",className:"bg-green-100 text-green-800 border-green-200",children:"Active"})})]}),e.jsxs("div",{children:[e.jsx(m,{className:"text-sm font-medium text-gray-600",children:"Created on"}),e.jsx("p",{className:"text-sm mt-1",children:d(s.created_at)}),e.jsxs("p",{className:"text-xs text-gray-500 mt-1",children:["Last updated ",d(s.updated_at)]})]})]})})]}),s.roles.some(a=>a.name==="minibus owner")&&s.commitment&&e.jsxs(t,{children:[e.jsx(i,{children:e.jsx(n,{children:"Commitment Statement"})}),e.jsx(c,{children:e.jsx("p",{className:"text-sm whitespace-pre-line",children:s.commitment})})]})]})]})]}),e.jsx(z,{open:r.open,title:r.action==="archive"?`Archive ${(g=r.user)==null?void 0:g.first_name} ${(f=r.user)==null?void 0:f.last_name}?`:r.action==="unarchive"?`Unarchive ${(v=r.user)==null?void 0:v.first_name} ${(N=r.user)==null?void 0:N.last_name}?`:"Are you sure?",description:r.action==="archive"?"This user will be archived and will not be able to access the system.":r.action==="unarchive"?"This user will be restored and regain access to the system.":"",confirmText:r.action==="archive"?"Archive":r.action==="unarchive"?"Unarchive":"Confirm",confirmVariant:r.action==="delete"?"destructive":"default",loading:A,onCancel:T,onConfirm:U})]})}export{ve as default};
