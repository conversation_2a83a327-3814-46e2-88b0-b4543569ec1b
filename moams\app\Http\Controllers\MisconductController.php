<?php

namespace App\Http\Controllers;

use App\Models\Misconduct;
use App\Models\User;
use App\Models\Driver;
use App\Http\Requests\StoreMisconductRequest;
use App\Http\Requests\UpdateMisconductRequest;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Auth;

class MisconductController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $user = Auth::user();
        $userRoles = $user->roles()->pluck('name')->toArray();

        $query = Misconduct::with(['offender', 'minibus', 'reportedBy', 'resolvedBy']);

        // Filter misconducts based on user role
        if ($user->hasRole('minibus owner')) {
            // Minibus owners only see misconducts related to their drivers
            $userDriverIds = $user->drivers()->pluck('id')->toArray();
            $query->where('offender_type', 'App\Models\Driver')
                ->whereIn('offender_id', $userDriverIds);
        }
        // Association staff (clerks, managers, admins) see all misconducts
        // No additional filtering needed for them

        $misconducts = $query->orderBy('offense_date', 'desc')->paginate(10);

        // Get drivers for minibus owners (for the create modal)
        $drivers = [];
        if ($user->hasRole('minibus owner')) {
            $drivers = $user->drivers()->active()->with('minibus')->get(['id', 'first_name', 'last_name', 'phone_number', 'minibus_id']);
        }

        return Inertia::render('misconductManagement/index-misconduct', [
            'misconducts' => $misconducts,
            'userRoles' => $userRoles,
            'drivers' => $drivers,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $user = Auth::user();
        $userRoles = $user->roles()->pluck('name')->toArray();

        // Only minibus owners can create misconducts
        if (!$user->hasRole('minibus owner')) {
            abort(403, 'Only minibus owners can create misconducts.');
        }

        // Get only the minibus owner's drivers for selection
        $drivers = $user->drivers()->active()->with('minibus')->get(['id', 'first_name', 'last_name', 'phone_number', 'minibus_id']);

        return Inertia::render('misconductManagement/create-misconduct', [
            'drivers' => $drivers,
            'userRoles' => $userRoles,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreMisconductRequest $request)
    {
        $user = Auth::user();

        // Only drivers can be offenders now
        $offenderType = 'App\Models\Driver';

        // Verify the driver exists
        $driver = Driver::findOrFail($request->input('offender_id'));

        // Check for duplicate reports (same driver, same date, same type)
        $existingMisconduct = Misconduct::where('offender_id', $driver->id)
            ->where('offender_type', $offenderType)
            ->where('offense_date', $request->input('offense_date'))
            ->where('name', $request->input('name'))
            ->first();

        if ($existingMisconduct) {
            return redirect()->back()
                ->withErrors(['duplicate' => 'A similar misconduct report for this driver on this date already exists.'])
                ->withInput();
        }

        // Handle evidence file upload
        $evidenceFile = null;
        if ($request->hasFile('evidence_file')) {
            $evidenceFile = $request->file('evidence_file')->store('misconduct-evidence', 'public');
        }

        // Calculate points to deduct based on severity
        $pointsDeducted = Misconduct::getPointsForSeverity($request->input('severity'));

        $misconduct = new Misconduct([
            'name' => $request->input('name'),
            'description' => $request->input('description'),
            'offense_date' => $request->input('offense_date'),
            'severity' => $request->input('severity'),
            'minibus_id' => $driver->minibus_id, // Use driver's assigned minibus
            'reported_by' => $user->id,
            'evidence_file' => $evidenceFile,
            'points_deducted' => $pointsDeducted,
            'offender_type' => $offenderType,
            'offender_id' => $request->input('offender_id'),
        ]);

        $misconduct->save();

        // Update driver's trust score
        $driver->deductTrustScore($pointsDeducted);

        return redirect()->route('misconducts.index')
            ->with('success', "Driver misconduct reported successfully. {$pointsDeducted} points deducted from driver's trust score.");
    }

    /**
     * Display the specified resource.
     */
    public function show(Misconduct $misconduct)
    {
        $user = Auth::user();
        $userRoles = $user->roles()->pluck('name')->toArray();

        $misconduct->load(['offender', 'minibus', 'reportedBy', 'resolvedBy', 'severityReviewedBy']);

        return Inertia::render('misconductManagement/show-misconduct', [
            'misconduct' => $misconduct,
            'userRoles' => $userRoles,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Misconduct $misconduct)
    {
        $user = Auth::user();
        $userRoles = $user->roles()->pluck('name')->toArray();

        // Only minibus owners can edit misconducts
        if (!$user->hasRole('minibus owner')) {
            abort(403, 'Only minibus owners can edit misconducts.');
        }

        // Minibus owners can only edit their own reports
        if ($misconduct->reported_by !== $user->id) {
            abort(403, 'You can only edit your own misconduct reports.');
        }

        $misconduct->load(['offender', 'minibus', 'reportedBy']);

        // Get only the minibus owner's drivers for selection
        $drivers = $user->drivers()->active()->with('minibus')->get(['id', 'first_name', 'last_name', 'phone_number', 'minibus_id']);

        return Inertia::render('misconductManagement/edit-misconduct', [
            'misconduct' => $misconduct,
            'drivers' => $drivers,
            'userRoles' => $userRoles,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateMisconductRequest $request, Misconduct $misconduct)
    {
        $user = Auth::user();

        // Only minibus owners can update misconducts
        if (!$user->hasRole('minibus owner')) {
            abort(403, 'Only minibus owners can update misconducts.');
        }

        // Minibus owners can only update their own reports
        if ($misconduct->reported_by !== $user->id) {
            abort(403, 'You can only update your own misconduct reports.');
        }

        // Only drivers can be offenders now
        $offenderType = 'App\Models\Driver';

        // Verify the driver exists
        $driver = Driver::findOrFail($request->input('offender_id'));

        // Handle evidence file upload
        $evidenceFile = $misconduct->evidence_file; // Keep existing file by default
        if ($request->hasFile('evidence_file')) {
            // Delete old file if exists
            if ($misconduct->evidence_file) {
                \Storage::disk('public')->delete($misconduct->evidence_file);
            }
            $evidenceFile = $request->file('evidence_file')->store('misconduct-evidence', 'public');
        }

        // Calculate new points if severity changed
        $oldPoints = $misconduct->points_deducted;
        $newPoints = Misconduct::getPointsForSeverity($request->input('severity'));
        $pointsDifference = $newPoints - $oldPoints;

        $misconduct->update([
            'name' => $request->input('name'),
            'description' => $request->input('description'),
            'offense_date' => $request->input('offense_date'),
            'severity' => $request->input('severity'),
            'minibus_id' => $driver->minibus_id, // Use driver's assigned minibus
            'evidence_file' => $evidenceFile,
            'points_deducted' => $newPoints,
            'offender_type' => $offenderType,
            'offender_id' => $request->input('offender_id'),
        ]);

        // Adjust driver's trust score if points changed
        if ($pointsDifference != 0) {
            $driver->deductTrustScore($pointsDifference);
        }

        return redirect()->route('misconducts.index')
            ->with('success', 'Driver misconduct updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Misconduct $misconduct)
    {
        $this->authorize('delete', $misconduct);
        $misconduct->delete();

        return redirect()->route('misconducts.index')
            ->with('success', 'Misconduct deleted successfully.');
    }

    /**
     * Update resolution status of a misconduct
     */
    public function updateResolution(Request $request, Misconduct $misconduct)
    {
        $user = Auth::user();

        // Only admins, clerks, and managers can resolve misconducts
        if (!$user->hasAnyRole(['system admin', 'association clerk', 'association manager'])) {
            abort(403, 'Only administrators and clerks can resolve misconducts.');
        }

        $request->validate([
            'resolution_status' => 'required|in:resolved,unresolved',
            'resolution_notes' => 'nullable|string|max:1000',
        ]);

        if ($request->input('resolution_status') === 'resolved') {
            $misconduct->markAsResolved($user->id, $request->input('resolution_notes'));
            $message = 'Misconduct marked as resolved successfully.';
        } else {
            $misconduct->markAsUnresolved();
            $message = 'Misconduct marked as unresolved.';
        }

        return redirect()->back()->with('success', $message);
    }

    /**
     * Update severity level (Association Clerk only)
     */
    public function updateSeverity(Request $request, Misconduct $misconduct)
    {
        // Only association clerks and admins can update severity
        if (!auth()->user()->hasRole(['association clerk', 'system admin'])) {
            abort(403, 'Unauthorized to update severity levels.');
        }

        $request->validate([
            'severity' => 'required|in:low,medium,high',
            'severity_notes' => 'nullable|string|max:1000',
        ]);

        $oldSeverity = $misconduct->severity;
        $newSeverity = $request->input('severity');

        // Calculate point adjustment
        $oldPoints = $misconduct->points_deducted;
        $newPoints = Misconduct::getPointsForSeverity($newSeverity);
        $pointsDifference = $newPoints - $oldPoints;

        // Update misconduct
        $misconduct->update([
            'severity' => $newSeverity,
            'points_deducted' => $newPoints,
            'severity_reviewed_by' => auth()->id(),
            'severity_reviewed_at' => now(),
            'severity_review_notes' => $request->input('severity_notes'),
            'original_severity' => $misconduct->original_severity ?? $oldSeverity,
        ]);

        // Adjust driver's trust score if points changed
        if ($pointsDifference != 0 && $misconduct->offender_type === 'App\Models\Driver') {
            $driver = $misconduct->offender;
            if ($driver) {
                $driver->deductTrustScore($pointsDifference);
            }
        }

        $message = $oldSeverity !== $newSeverity
            ? "Severity updated from {$oldSeverity} to {$newSeverity}. Trust score adjusted by {$pointsDifference} points."
            : 'Severity review completed with no changes.';

        return redirect()->back()->with('success', $message);
    }

    /**
     * Get misconducts for a specific user (minibus owner)
     */
    public function userMisconducts(User $user)
    {
        $currentUser = Auth::user();
        $userRoles = $currentUser->roles()->pluck('name')->toArray();

        $misconducts = $user->misconducts()
            ->orderBy('offense_date', 'desc')
            ->paginate(10);

        return Inertia::render('misconductManagement/user-misconducts', [
            'user' => $user,
            'misconducts' => $misconducts,
            'userRoles' => $userRoles,
        ]);
    }

    /**
     * Get misconducts for a specific driver
     */
    public function driverMisconducts(Driver $driver)
    {
        $currentUser = Auth::user();
        $userRoles = $currentUser->roles()->pluck('name')->toArray();

        $misconducts = $driver->misconducts()
            ->orderBy('offense_date', 'desc')
            ->paginate(10);

        return Inertia::render('misconductManagement/driver-misconducts', [
            'driver' => $driver,
            'misconducts' => $misconducts,
            'userRoles' => $userRoles,
        ]);
    }
}
