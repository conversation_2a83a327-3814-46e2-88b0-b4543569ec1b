<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateDriverRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $user = auth()->user();
        $userRole = $user->roles->first()->name ?? null;

        $rules = [
            'first_name' => ['required', 'string', 'max:255', 'regex:/^[a-zA-Z\s]+$/'],
            'last_name' => ['required', 'string', 'max:255', 'regex:/^[a-zA-Z\s]+$/'],
            'phone_number' => [
                'required',
                'string',
                'max:255',
                Rule::unique('drivers')->ignore($this->driver->id),
            ],
            'district' => 'required|string|max:255',
            'village_town' => 'required|string|max:255',
        ];

        // Only association clerks can assign drivers to different owners
        if ($userRole === 'association clerk') {
            $rules['owner_id'] = 'required|exists:users,id';
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'first_name.required' => 'First name is required.',
            'last_name.required' => 'Last name is required.',
            'phone_number.required' => 'Phone number is required.',
            'phone_number.unique' => 'This phone number is already registered.',
            'district.required' => 'District is required.',
            'village_town.required' => 'Village/Town is required.',
            'owner_id.required' => 'Please select a minibus owner.',
            'owner_id.exists' => 'Selected minibus owner is invalid.',
        ];
    }
}
