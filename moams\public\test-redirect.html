<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Paychangu Redirect</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .result {
            background: #e8f5e8;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid #4caf50;
        }
        .error {
            background: #ffeaea;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid #f44336;
        }
        button {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
    </style>
</head>
<body>
    <h1>Paychangu Redirect Test</h1>
    
    <div class="test-section">
        <h2>Test Redirect with Sample Parameters</h2>
        <p>This will test the redirect functionality with sample transaction parameters.</p>
        
        <button onclick="testRedirect()">Test Redirect with Sample TX_REF</button>
        <button onclick="testRedirectNoParams()">Test Redirect without Parameters</button>
        
        <div id="test-results"></div>
    </div>
    
    <div class="test-section">
        <h2>Manual Test Links</h2>
        <p>Click these links to manually test the redirect:</p>
        
        <ul>
            <li><a href="/redirect-paychangu.html?tx_ref=MOAMS_1234567890_TEST&status=successful" target="_blank">Test with Success Status</a></li>
            <li><a href="/redirect-paychangu.html?tx_ref=MOAMS_1234567890_TEST&status=failed" target="_blank">Test with Failed Status</a></li>
            <li><a href="/redirect-paychangu.html?tx_ref=MOAMS_1234567890_TEST" target="_blank">Test with TX_REF only</a></li>
            <li><a href="/redirect-paychangu.html" target="_blank">Test without parameters</a></li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>Expected Behavior</h2>
        <ul>
            <li>With tx_ref: Should redirect immediately to <code>http://127.0.0.1:8000/payments/paychangu/return?tx_ref=...</code></li>
            <li>Without tx_ref: Should redirect to dashboard after 3 seconds</li>
            <li>All redirects should include the port number <code>:8000</code></li>
        </ul>
    </div>

    <script>
        function testRedirect() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<div class="result">Testing redirect with sample parameters...</div>';
            
            // Simulate the redirect URL construction
            const txRef = 'MOAMS_' + Date.now() + '_TEST';
            const status = 'successful';
            
            const redirectUrl = 'http://127.0.0.1:8000/payments/paychangu/return?tx_ref=' + encodeURIComponent(txRef) + '&status=' + encodeURIComponent(status);
            
            resultsDiv.innerHTML += '<div class="result">Generated URL: <code>' + redirectUrl + '</code></div>';
            resultsDiv.innerHTML += '<div class="result">Opening redirect page in new tab...</div>';
            
            // Open the redirect page with test parameters
            window.open('/redirect-paychangu.html?tx_ref=' + encodeURIComponent(txRef) + '&status=' + encodeURIComponent(status), '_blank');
        }
        
        function testRedirectNoParams() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<div class="result">Testing redirect without parameters...</div>';
            resultsDiv.innerHTML += '<div class="result">Should redirect to dashboard after 3 seconds</div>';
            resultsDiv.innerHTML += '<div class="result">Opening redirect page in new tab...</div>';
            
            // Open the redirect page without parameters
            window.open('/redirect-paychangu.html', '_blank');
        }
    </script>
</body>
</html>
