import React from 'react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, CreditCard, User, Calendar, DollarSign } from 'lucide-react';
import { Link } from '@inertiajs/react';
import { route } from 'ziggy-js';

export default function PaymentSuccess({ payment, associationClerk }) {
    // Define breadcrumbs for payment success
    const breadcrumbs = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'My Membership', href: '/my-membership' },
        { title: 'Payment Successful', href: '#' },
    ];

    if (!payment) {
        return (
            <AppLayout title="Payment Not Found" breadcrumbs={breadcrumbs}>
                <div className="max-w-2xl mx-auto py-8 px-4 text-center">
                    <h1 className="text-2xl font-bold text-red-600 mb-4">Payment Not Found</h1>
                    <p className="text-gray-600 mb-4">The payment information could not be loaded.</p>
                    <Link href={route('dashboard')} className="text-blue-600 hover:underline">
                        Return to Dashboard
                    </Link>
                </div>
            </AppLayout>
        );
    }

    return (
        <AppLayout title="Payment Successful" breadcrumbs={breadcrumbs}>
            <div className="max-w-2xl mx-auto py-8 px-4">
                {/* Success Header */}
                <div className="text-center mb-8">
                    <div className="flex justify-center mb-4">
                        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                            <CheckCircle className="h-10 w-10 text-green-600" />
                        </div>
                    </div>
                    <h1 className="text-3xl font-bold text-green-700 mb-2">
                        Payment Successful!
                    </h1>
                    <p className="text-gray-600">
                        Your payment has been processed successfully
                    </p>
                </div>

                {/* Payment Details Card */}
                <Card className="mb-6 border-green-200 bg-green-50">
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2 text-green-800">
                            <CreditCard className="h-5 w-5" />
                            Payment Details
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="flex items-center gap-3">
                                <DollarSign className="h-5 w-5 text-green-600" />
                                <div>
                                    <div className="text-sm text-gray-600">Amount</div>
                                    <div className="font-semibold text-green-800">
                                        MWK {parseFloat(payment.amount).toLocaleString()}
                                    </div>
                                </div>
                            </div>

                            <div className="flex items-center gap-3">
                                <User className="h-5 w-5 text-green-600" />
                                <div>
                                    <div className="text-sm text-gray-600">Fee Type</div>
                                    <div className="font-semibold text-green-800 capitalize">
                                        {payment.fee_type}
                                    </div>
                                </div>
                            </div>

                            <div className="flex items-center gap-3">
                                <CreditCard className="h-5 w-5 text-green-600" />
                                <div>
                                    <div className="text-sm text-gray-600">Payment Method</div>
                                    <div className="font-semibold text-green-800">
                                        {payment.payment_method === 'ctechpay_card' ? 'Card Payment' : payment.payment_method}
                                    </div>
                                </div>
                            </div>

                            <div className="flex items-center gap-3">
                                <Calendar className="h-5 w-5 text-green-600" />
                                <div>
                                    <div className="text-sm text-gray-600">Date & Time</div>
                                    <div className="font-semibold text-green-800">
                                        {new Date(payment.paid_at || payment.created_at).toLocaleString()}
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Ctechpay specific details */}
                        {payment.ctechpay_order_reference && (
                            <div className="pt-4 border-t border-green-200">
                                <div className="text-sm text-gray-600 mb-2">Transaction Details</div>
                                <div className="space-y-2 text-sm">
                                    <div className="flex justify-between">
                                        <span>Order Reference:</span>
                                        <span className="font-mono">{payment.ctechpay_order_reference}</span>
                                    </div>
                                    {payment.ctechpay_transaction_id && (
                                        <div className="flex justify-between">
                                            <span>Transaction ID:</span>
                                            <span className="font-mono">{payment.ctechpay_transaction_id}</span>
                                        </div>
                                    )}
                                    {payment.ctechpay_card_holder_name && (
                                        <div className="flex justify-between">
                                            <span>Card Holder:</span>
                                            <span>{payment.ctechpay_card_holder_name}</span>
                                        </div>
                                    )}
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Next Steps */}
                <Card className="mb-6">
                    <CardHeader>
                        <CardTitle>What's Next?</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-3">
                            <div className="flex items-start gap-3">
                                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                    <span className="text-blue-600 text-sm font-semibold">1</span>
                                </div>
                                <div>
                                    <div className="font-medium">Confirmation Email</div>
                                    <div className="text-sm text-gray-600">
                                        You will receive a confirmation email shortly with your payment receipt.
                                    </div>
                                </div>
                            </div>

                            <div className="flex items-start gap-3">
                                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                    <span className="text-blue-600 text-sm font-semibold">2</span>
                                </div>
                                <div>
                                    <div className="font-medium">Membership Status Updated</div>
                                    <div className="text-sm text-gray-600">
                                        Your membership status has been automatically updated to reflect this payment.
                                    </div>
                                </div>
                            </div>

                            <div className="flex items-start gap-3">
                                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                    <span className="text-blue-600 text-sm font-semibold">3</span>
                                </div>
                                <div>
                                    <div className="font-medium">Access Your Dashboard</div>
                                    <div className="text-sm text-gray-600">
                                        You can now access all membership features and services.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-3 justify-center">
                    <Button asChild variant="outline">
                        <Link href={route('dashboard')}>
                            Return to Dashboard
                        </Link>
                    </Button>

                    <Button asChild className="bg-green-600 hover:bg-green-700">
                        <Link href={route('my.membership')}>
                            View My Membership
                        </Link>
                    </Button>
                </div>

                {/* Support Information */}
                <div className="mt-8 text-center text-sm text-gray-500">
                    {associationClerk ? (
                        <p>
                            Need help? Contact {associationClerk.first_name} {associationClerk.last_name} at{' '}
                            <a href={`mailto:${associationClerk.email}`} className="text-blue-600 hover:underline">
                                {associationClerk.email}
                            </a>{' '}
                            or call {associationClerk.phone_number}
                        </p>
                    ) : (
                        <p>
                            Need help? Contact MOAM support for assistance.
                        </p>
                    )}
                </div>
            </div>
        </AppLayout>
    );
}
