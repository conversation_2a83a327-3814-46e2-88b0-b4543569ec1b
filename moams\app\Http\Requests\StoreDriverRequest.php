<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Models\Driver;

class StoreDriverRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $user = auth()->user();
        $userRole = $user->roles->first()->name ?? null;

        $rules = [
            'first_name' => ['required', 'string', 'max:255', 'regex:/^[a-zA-Z\s]+$/'],
            'last_name' => ['required', 'string', 'max:255', 'regex:/^[a-zA-Z\s]+$/'],
            'phone_number' => 'required|string|unique:drivers,phone_number|max:255',
            'license_number' => [
                'required',
                'string',
                'max:255',
                function ($attribute, $value, $fail) {
                    if (Driver::licenseNumberExists($value)) {
                        $fail('This license number is already registered.');
                    }
                }
            ],
            'district' => 'required|string|max:255',
            'village_town' => 'required|string|max:255',
            'minibus_id' => [
                'nullable',
                'exists:minibuses,id',
                function ($attribute, $value, $fail) {
                    if ($value) {
                        // Check if minibus is already assigned to another active driver
                        $existingDriver = Driver::where('minibus_id', $value)
                            ->where('archived', false)
                            ->first();

                        if ($existingDriver) {
                            $fail("This minibus is already assigned to driver: {$existingDriver->full_name}");
                        }

                        // Check if minibus belongs to the selected owner
                        $user = auth()->user();
                        $userRole = $user->roles->first()->name ?? null;

                        $ownerId = $userRole === 'minibus owner'
                            ? $user->id
                            : request()->input('owner_id');

                        if ($ownerId) {
                            $minibus = \App\Models\Minibus::find($value);
                            if ($minibus && $minibus->owner_id != $ownerId) {
                                $fail('The selected minibus does not belong to the selected owner.');
                            }
                        }
                    }
                }
            ],
            'license' => 'required|file|mimes:pdf,jpg,jpeg,png',
        ];

        // Only association clerks can assign drivers to different owners
        if ($userRole === 'association clerk') {
            $rules['owner_id'] = [
                'required',
                'exists:users,id',
                function ($attribute, $value, $fail) {
                    if (
                        !\App\Models\User::where('id', $value)->whereHas('roles', function ($q) {
                            $q->where('name', 'minibus owner');
                        })->exists()
                    ) {
                        $fail('Selected minibus owner is invalid.');
                    }
                }
            ];
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'first_name.required' => 'First name is required.',
            'last_name.required' => 'Last name is required.',
            'phone_number.required' => 'Phone number is required.',
            'phone_number.unique' => 'This phone number is already registered.',
            'license_number.required' => 'License number is required.',
            'district.required' => 'District is required.',
            'village_town.required' => 'Village/Town is required.',
            'owner_id.required' => 'Please select a minibus owner.',
            'owner_id.exists' => 'Selected minibus owner is invalid.',
            'minibus_id.exists' => 'Selected minibus is invalid.',
            'license.required' => 'License file is required.',
            'license.file' => 'License must be a file.',
            'license.mimes' => 'License must be a PDF or image file.',
        ];
    }
}
