<?php

use App\Http\Controllers\DriverController;
use App\Http\Controllers\DriverClearanceController;
use Illuminate\Support\Facades\Route;

// Driver Management Routes
Route::middleware(['auth', 'verified'])->group(function () {
    // IMPORTANT: Specific routes MUST come before resource routes to avoid conflicts

    // Clerk/Manager only routes - MUST be before resource route
    Route::middleware('role:association clerk|association manager')->group(function () {
        Route::get('/drivers/clearance-requests', [DriverClearanceController::class, 'indexClearanceRequests'])
            ->name('drivers.clearance-requests.index');
        Route::get('/drivers/clearance-requests/{clearanceRequest}', [DriverClearanceController::class, 'showClearanceRequest'])
            ->name('drivers.clearance-requests.show');
        Route::patch('/drivers/clearance-requests/{clearanceRequest}/process', [DriverClearanceController::class, 'processClearance'])
            ->name('drivers.clearance-requests.process');
        Route::delete('/drivers/clearance-requests/{clearanceRequest}', [DriverClearanceController::class, 'destroyClearanceRequest'])
            ->name('drivers.clearance-requests.destroy');
    });

    // Basic Driver CRUD operations - MUST come after specific routes
    Route::resource('drivers', DriverController::class);

    // Driver unarchive (for association clerks)
    Route::patch('/drivers/{driver}/unarchive', [DriverController::class, 'unarchive'])
        ->name('drivers.unarchive');

    // Driver direct clearance (for association clerks)
    Route::patch('/drivers/{driver}/direct-clear', [DriverClearanceController::class, 'directClear'])
        ->name('drivers.direct-clear');

    // Driver Clearance Routes (flattened, no prefix)
    Route::get('/drivers/{driver}/request-clearance', [DriverClearanceController::class, 'requestClearance'])
        ->name('drivers.request-clearance')
        ->middleware('role:minibus owner');
    Route::post('/drivers/{driver}/request-clearance', [DriverClearanceController::class, 'storeClearanceRequest'])
        ->name('drivers.store-clearance-request')
        ->middleware('role:minibus owner');

    Route::get('/drivers/{driver}/history', [DriverClearanceController::class, 'history'])
        ->name('drivers.history');
});