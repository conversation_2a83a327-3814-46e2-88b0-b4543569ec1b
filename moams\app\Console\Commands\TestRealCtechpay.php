<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use App\Models\User;
use App\Models\Membership;

class TestRealCtechpay extends Command
{
    protected $signature = 'test:real-ctechpay';
    protected $description = 'Test real Ctechpay API connection';

    public function handle()
    {
        $this->info('Testing Real Ctechpay API...');

        try {
            // Get configuration
            $apiToken = config('services.ctechpay.token');
            $apiUrl = config('services.ctechpay.api_url');
            $registration = config('services.ctechpay.registration');
            $environment = config('services.ctechpay.environment');
            $mockMode = env('CTECHPAY_MOCK_MODE', false);

            $this->info('Configuration:');
            $this->info("API Token: " . ($apiToken ? substr($apiToken, 0, 8) . '...' : 'Not set'));
            $this->info("API URL: {$apiUrl}");
            $this->info("Registration: {$registration}");
            $this->info("Environment: {$environment}");
            $this->info("Mock Mode: " . ($mockMode ? 'ENABLED' : 'DISABLED'));

            if ($mockMode) {
                $this->warn('Mock mode is still enabled! Real API will not be used.');
                return;
            }

            // Test API connectivity
            $this->info('Testing API connectivity...');
            
            $redirectUrl = route('payments.ctechpay.return', [], true);
            $cancelUrl = route('payments.ctechpay.cancel', [], true);

            $this->info("Redirect URL: {$redirectUrl}");
            $this->info("Cancel URL: {$cancelUrl}");

            // Test order creation
            $this->info('Creating test order...');
            
            $response = Http::timeout(30)->asForm()->post($apiUrl . '/?endpoint=order', [
                'token' => $apiToken,
                'amount' => 100, // Small test amount
                'merchantAttributes' => true,
                'redirectUrl' => $redirectUrl,
                'cancelUrl' => $cancelUrl,
                'cancelText' => 'Cancel Payment',
            ]);

            $this->info('Response Status: ' . $response->status());
            $this->info('Response Headers: ' . json_encode($response->headers()));

            if ($response->successful()) {
                $data = $response->json();
                $this->info('✅ SUCCESS: API connection working!');
                $this->info('Response Data: ' . json_encode($data, JSON_PRETTY_PRINT));

                if (isset($data['payment_page_URL']) && $data['payment_page_URL']) {
                    $this->info('✅ Payment URL received: ' . $data['payment_page_URL']);
                } else {
                    $this->warn('⚠️  No payment_page_URL in response');
                }

                if (isset($data['order_reference']) && $data['order_reference']) {
                    $this->info('✅ Order reference received: ' . $data['order_reference']);
                } else {
                    $this->warn('⚠️  No order_reference in response');
                }

            } else {
                $this->error('❌ FAILED: API request failed');
                $this->error('Status: ' . $response->status());
                $this->error('Body: ' . $response->body());
                
                // Try to parse error response
                try {
                    $errorData = $response->json();
                    $this->error('Error Data: ' . json_encode($errorData, JSON_PRETTY_PRINT));
                } catch (\Exception $e) {
                    $this->error('Could not parse error response as JSON');
                }
            }

        } catch (\Exception $e) {
            $this->error('❌ EXCEPTION: ' . $e->getMessage());
            $this->error('Stack trace: ' . $e->getTraceAsString());
        }
    }
}
