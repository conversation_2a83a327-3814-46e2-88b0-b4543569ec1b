import { useState, useCallback, useEffect } from 'react';
import { Head, useForm, Link } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowLeft } from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { MinibusOwnerCombobox } from '@/components/ui/minibus-owner-combobox';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';

export default function EditDriver({ driver, userRole, minibusOwners, currentOwner }) {
    const owners = minibusOwners || [];
    const [selectedOwner, setSelectedOwner] = useState(currentOwner ? { ...currentOwner, email: currentOwner.email || driver.minibus_owner?.email || '' } : null);
    const { data, setData, put, processing, errors, reset } = useForm({
        first_name: driver.first_name,
        last_name: driver.last_name,
        phone_number: driver.phone_number,
        district: driver.district,
        village_town: driver.village_town,
        owner_id: (currentOwner?.id || driver.owner_id?.toString() || ''),
    });

    const [routes, setRoutes] = useState([]);
    const [selectedRoute, setSelectedRoute] = useState(null);

    // Fetch routes on mount
    useEffect(() => {
        fetch('/api/vehicle-routes')
            .then(res => res.json())
            .then(data => setRoutes(data));
    }, []);

    const handleFileChange = (e) => {
        setData('license', e.target.files[0]);
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        put(route('drivers.update', driver.id), {
            forceFormData: true
        });
    };

    const handleOwnerChange = (owner) => {
        setSelectedOwner(owner);
        setData('owner_id', owner?.id || '');
    };

    const districts = [
        'Lilongwe', 'Blantyre', 'Mzuzu', 'Zomba', 'Kasungu', 'Ntcheu',
        'Mangochi', 'Salima', 'Dowa', 'Ntchisi', 'Dedza', 'Nkhotakota',
        'Mchinji', 'Nkhatabay', 'Rumphi', 'Chitipa', 'Karonga', 'Thyolo',
        'Mulanje', 'Phalombe', 'Chikwawa', 'Nsanje', 'Balaka', 'Machinga'
    ];

    const breadcrumbs = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: userRole === 'minibus owner' ? 'My Drivers' : 'Driver Management', href: '/drivers' },
        { title: 'Edit Driver', href: `/drivers/${driver.id}/edit` },
    ];

    const fetchOwners = useCallback(async (search) => {
        try {
            const response = await fetch(`/api/minibus-owners?search=${encodeURIComponent(search)}`);
            if (response.ok) {
                const data = await response.json();
                return data.map(owner => ({
                    id: owner.id,
                    name: `${owner.first_name} ${owner.last_name}`,
                    email: owner.email,
                }));
            }
        } catch (error) {
            console.error('Error fetching minibus owners:', error);
        }
        return [];
    }, []);

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Edit Driver" />
            <div className="container mx-auto px-4 py-8">
                <div className="space-y-8">
                    <div className="flex items-center space-x-4 mb-6">
                        <Link href={route('drivers.show', driver.id)}>
                            <Button variant="outline" size="sm">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Back to Driver
                            </Button>
                        </Link>
                        <span className="text-muted-foreground text-base font-medium">
                            Update the driver's information. Note: License number cannot be changed for security reasons.
                        </span>
                    </div>

                    <div className="max-w-2xl">
                        <Card>
                            <CardHeader>
                                <CardTitle>Driver Information</CardTitle>
                                <CardDescription>
                                    Update the driver's personal and contact details
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <form onSubmit={handleSubmit} className="space-y-6">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div className="space-y-2">
                                            <Label htmlFor="first_name">First Name *</Label>
                                            <Input
                                                id="first_name"
                                                value={data.first_name || ""}
                                                onChange={(e) => setData('first_name', e.target.value)}
                                                placeholder="Enter first name"
                                                className={errors.first_name ? 'border-red-500' : ''}
                                            />
                                            {errors.first_name && (
                                                <p className="text-sm text-red-500">{errors.first_name}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="last_name">Last Name *</Label>
                                            <Input
                                                id="last_name"
                                                value={data.last_name || ""}
                                                onChange={(e) => setData('last_name', e.target.value)}
                                                placeholder="Enter last name"
                                                className={errors.last_name ? 'border-red-500' : ''}
                                            />
                                            {errors.last_name && (
                                                <p className="text-sm text-red-500">{errors.last_name}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="phone_number">Phone Number *</Label>
                                            <Input
                                                id="phone_number"
                                                value={data.phone_number || ""}
                                                onChange={(e) => setData('phone_number', e.target.value)}
                                                placeholder="Enter phone number"
                                                className={errors.phone_number ? 'border-red-500' : ''}
                                            />
                                            {errors.phone_number && (
                                                <p className="text-sm text-red-500">{errors.phone_number}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="district">District *</Label>
                                            <Select
                                                value={data.district || ""}
                                                onValueChange={(value) => setData('district', value)}
                                            >
                                                <SelectTrigger className={errors.district ? 'border-red-500' : ''}>
                                                    <SelectValue placeholder="Select district" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {districts.map((district) => (
                                                        <SelectItem key={district} value={district}>
                                                            {district}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            {errors.district && (
                                                <p className="text-sm text-red-500">{errors.district}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="village_town">Village/Town *</Label>
                                            <Input
                                                id="village_town"
                                                value={data.village_town || ""}
                                                onChange={(e) => setData('village_town', e.target.value)}
                                                placeholder="Enter village or town"
                                                className={errors.village_town ? 'border-red-500' : ''}
                                            />
                                            {errors.village_town && (
                                                <p className="text-sm text-red-500">{errors.village_town}</p>
                                            )}
                                        </div>

                                        {userRole === 'association clerk' && (
                                            <div className="space-y-2">
                                                <Label htmlFor="owner_id">Minibus Owner *</Label>
                                                <MinibusOwnerCombobox
                                                    value={selectedOwner || null}
                                                    onChange={handleOwnerChange}
                                                    fetchOwners={fetchOwners}
                                                    placeholder="Select minibus owner..."
                                                />
                                                {errors.owner_id && (
                                                    <p className="text-sm text-red-500">{errors.owner_id}</p>
                                                )}
                                            </div>
                                        )}
                                    </div>

                                    <div className="flex justify-end space-x-4">
                                        <Link href={route('drivers.show', driver.id)}>
                                            <Button type="button" variant="outline">
                                                Cancel
                                            </Button>
                                        </Link>
                                        <Button type="submit" disabled={processing} className="bg-blue-600 hover:bg-blue-700">
                                            {processing ? 'Updating...' : 'Update Driver'}
                                        </Button>
                                    </div>
                                </form>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
