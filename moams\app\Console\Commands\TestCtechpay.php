<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Membership;
use Illuminate\Support\Facades\Http;

class TestCtechpay extends Command
{
    protected $signature = 'test:ctechpay';
    protected $description = 'Test Ctechpay payment initiation';

    public function handle()
    {
        $this->info('Testing Ctechpay payment initiation...');

        try {
            // Get test data
            $user = User::first();
            $membership = Membership::first();
            
            if (!$user || !$membership) {
                $this->error('No user or membership found for testing');
                return;
            }

            $this->info("Testing with user: {$user->first_name} {$user->last_name}");
            $this->info("Membership ID: {$membership->id}");

            // Test payment data
            $paymentData = [
                'user_id' => $user->id,
                'membership_id' => $membership->id,
                'fee_type' => 'registration',
                'amount' => 5000,
            ];

            $this->info('Payment data: ' . json_encode($paymentData));

            // Check mock mode
            $mockMode = env('CTECHPAY_MOCK_MODE', false);
            $this->info('Mock mode: ' . ($mockMode ? 'ENABLED' : 'DISABLED'));

            // Make the API call
            $this->info('Making payment initiation request...');
            
            $response = Http::withHeaders([
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
                'X-CSRF-TOKEN' => csrf_token(),
            ])->post(route('payments.ctechpay.initiate'), $paymentData);

            $this->info('Response status: ' . $response->status());
            $this->info('Response body: ' . $response->body());

            if ($response->successful()) {
                $data = $response->json();
                $this->info('Response data: ' . json_encode($data, JSON_PRETTY_PRINT));
                
                if (isset($data['payment_page_url']) && $data['payment_page_url']) {
                    $this->info('✅ SUCCESS: Payment URL received: ' . $data['payment_page_url']);
                } else {
                    $this->error('❌ FAILED: No payment URL in response');
                }
            } else {
                $this->error('❌ FAILED: HTTP error ' . $response->status());
                $this->error('Error body: ' . $response->body());
            }

        } catch (\Exception $e) {
            $this->error('❌ EXCEPTION: ' . $e->getMessage());
            $this->error('Stack trace: ' . $e->getTraceAsString());
        }
    }
}
