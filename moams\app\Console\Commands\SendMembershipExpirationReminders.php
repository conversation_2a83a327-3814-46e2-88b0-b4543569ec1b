<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Membership;
use App\Models\User;
use App\Notifications\MembershipExpirationReminderNotification;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class SendMembershipExpirationReminders extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'memberships:send-expiration-reminders 
                            {--dry-run : Show what would be sent without actually sending}
                            {--type= : Specific reminder type to send (early,standard,urgent,final)}
                            {--days= : Specific days until expiration to target}';

    /**
     * The console command description.
     */
    protected $description = 'Send membership expiration reminder notifications to users';

    /**
     * Reminder configuration
     */
    private $reminderConfig = [
        'early' => 30,      // 30 days before expiration
        'standard' => 14,   // 14 days before expiration
        'urgent' => 7,      // 7 days before expiration
        'final' => 1,       // 1 day before expiration
    ];

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $isDryRun = $this->option('dry-run');
        $specificType = $this->option('type');
        $specificDays = $this->option('days');

        $this->info('Starting membership expiration reminder process...');
        
        if ($isDryRun) {
            $this->warn('DRY RUN MODE - No notifications will be sent');
        }

        $totalSent = 0;
        $results = [];

        // If specific type or days are provided, use those
        if ($specificType && isset($this->reminderConfig[$specificType])) {
            $days = $this->reminderConfig[$specificType];
            $sent = $this->sendRemindersForDays($days, $specificType, $isDryRun);
            $totalSent += $sent;
            $results[$specificType] = $sent;
        } elseif ($specificDays) {
            $sent = $this->sendRemindersForDays((int)$specificDays, 'custom', $isDryRun);
            $totalSent += $sent;
            $results['custom'] = $sent;
        } else {
            // Send all configured reminders
            foreach ($this->reminderConfig as $type => $days) {
                $sent = $this->sendRemindersForDays($days, $type, $isDryRun);
                $totalSent += $sent;
                $results[$type] = $sent;
            }
        }

        // Display results
        $this->displayResults($results, $totalSent, $isDryRun);

        Log::info('Membership expiration reminders process completed', [
            'total_sent' => $totalSent,
            'results' => $results,
            'dry_run' => $isDryRun
        ]);

        return 0;
    }

    /**
     * Send reminders for memberships expiring in specific number of days
     */
    private function sendRemindersForDays(int $days, string $type, bool $isDryRun): int
    {
        $targetDate = Carbon::today()->addDays($days);
        
        $this->info("Processing {$type} reminders for memberships expiring on {$targetDate->format('Y-m-d')} ({$days} days)...");

        // Get memberships expiring on the target date
        $memberships = Membership::with(['user'])
            ->whereDate('end_date', $targetDate)
            ->whereIn('status', ['Affiliation fee paid', 'Registered']) // Only active memberships
            ->get();

        $sent = 0;

        foreach ($memberships as $membership) {
            try {
                // Check if user exists and has valid email
                if (!$membership->user || !$membership->user->email) {
                    $this->warn("Skipping membership {$membership->id} - no valid user or email");
                    continue;
                }

                // Check if reminder was already sent recently for this membership and type
                if ($this->wasReminderRecentlySent($membership, $type, $days)) {
                    $this->info("Skipping membership {$membership->id} - {$type} reminder already sent recently");
                    continue;
                }

                if (!$isDryRun) {
                    // Send notification
                    $membership->user->notify(
                        new MembershipExpirationReminderNotification($membership, $days, $type)
                    );

                    // Log the reminder
                    $this->logReminderSent($membership, $type, $days);
                }

                $sent++;
                
                $this->line("  ✓ {$type} reminder for {$membership->user->first_name} {$membership->user->last_name} ({$membership->user->email})");

            } catch (\Exception $e) {
                $this->error("Failed to send {$type} reminder for membership {$membership->id}: " . $e->getMessage());
                
                Log::error('Failed to send membership expiration reminder', [
                    'membership_id' => $membership->id,
                    'user_id' => $membership->user_id,
                    'reminder_type' => $type,
                    'days' => $days,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $sent;
    }

    /**
     * Check if a reminder was recently sent for this membership
     */
    private function wasReminderRecentlySent(Membership $membership, string $type, int $days): bool
    {
        // Check database notifications to see if this type of reminder was sent in the last 24 hours
        $recentNotification = $membership->user->notifications()
            ->where('type', MembershipExpirationReminderNotification::class)
            ->where('created_at', '>=', Carbon::now()->subDay())
            ->whereJsonContains('data->reminder_type', $type)
            ->whereJsonContains('data->membership_id', $membership->id)
            ->first();

        return $recentNotification !== null;
    }

    /**
     * Log that a reminder was sent
     */
    private function logReminderSent(Membership $membership, string $type, int $days): void
    {
        Log::info('Membership expiration reminder sent', [
            'membership_id' => $membership->id,
            'user_id' => $membership->user_id,
            'user_email' => $membership->user->email,
            'membership_type' => $membership->type,
            'reminder_type' => $type,
            'days_until_expiration' => $days,
            'expiration_date' => $membership->end_date->toDateString()
        ]);
    }

    /**
     * Display results summary
     */
    private function displayResults(array $results, int $totalSent, bool $isDryRun): void
    {
        $this->newLine();
        $this->info('=== RESULTS SUMMARY ===');
        
        if ($isDryRun) {
            $this->warn('DRY RUN - No notifications were actually sent');
        }

        foreach ($results as $type => $count) {
            $this->line("  {$type}: {$count} reminders " . ($isDryRun ? 'would be sent' : 'sent'));
        }

        $this->newLine();
        $this->info("Total: {$totalSent} reminders " . ($isDryRun ? 'would be sent' : 'sent'));

        if (!$isDryRun && $totalSent > 0) {
            $this->info('All reminders have been sent successfully!');
        }
    }

    /**
     * Get upcoming expirations for preview
     */
    public function previewUpcomingExpirations(): void
    {
        $this->info('=== UPCOMING MEMBERSHIP EXPIRATIONS ===');

        foreach ($this->reminderConfig as $type => $days) {
            $targetDate = Carbon::today()->addDays($days);
            
            $count = Membership::whereDate('end_date', $targetDate)
                ->whereIn('status', ['Affiliation fee paid', 'Registered'])
                ->count();

            $this->line("  {$type} ({$days} days): {$count} memberships expiring on {$targetDate->format('Y-m-d')}");
        }
    }
}
