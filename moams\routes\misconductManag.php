<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\MisconductController;

Route::middleware(['auth', 'verified'])->group(function () {
    // Misconduct Management Routes - Only minibus owners can create/edit, but all authenticated users can view
    Route::get('misconducts', [MisconductController::class, 'index'])->name('misconducts.index');

    // Routes restricted to minibus owners only - MUST come before {misconduct} route
    Route::middleware(['role:minibus owner'])->group(function () {
        Route::get('misconducts/create', [MisconductController::class, 'create'])->name('misconducts.create');
        Route::post('misconducts', [MisconductController::class, 'store'])->name('misconducts.store');
        Route::get('misconducts/{misconduct}/edit', [MisconductController::class, 'edit'])->name('misconducts.edit');
        Route::put('misconducts/{misconduct}', [MisconductController::class, 'update'])->name('misconducts.update');
    });

    // Show route must come after create route to avoid conflicts
    Route::get('misconducts/{misconduct}', [MisconductController::class, 'show'])->name('misconducts.show');
    Route::get('users/{user}/misconducts', [MisconductController::class, 'userMisconducts'])->name('users.misconducts');
    Route::get('drivers/{driver}/misconducts', [MisconductController::class, 'driverMisconducts'])->name('drivers.misconducts');

    // Delete routes for managers and admins only
    Route::middleware(['role:association manager|system admin'])->group(function () {
        Route::delete('misconducts/{misconduct}', [MisconductController::class, 'destroy'])->name('misconducts.destroy');
    });

    // Analytics and resolution routes for admins and clerks
    Route::middleware(['role:system admin|association clerk|association manager'])->group(function () {
        Route::get('misconduct-analytics', [\App\Http\Controllers\MisconductAnalyticsController::class, 'index'])->name('misconduct-analytics.index');
        Route::get('misconduct-analytics/report', [\App\Http\Controllers\MisconductAnalyticsController::class, 'generateReport'])->name('misconduct-analytics.report');
        Route::patch('misconducts/{misconduct}/resolution', [MisconductController::class, 'updateResolution'])->name('misconducts.update-resolution');
        Route::patch('misconducts/{misconduct}/severity', [MisconductController::class, 'updateSeverity'])->name('misconducts.update-severity');
    });
});