CTECHPAY API DOCUMENTATION

CARD PAYMENTS

Request Payment from your customer

Overview
This API allows you to initiate a payment request through the CtechPay platform using the card payment method.

The API is a RESTful API and utilizes HTTP request methods. The API is designed to have predictable, resource-oriented URLs and to use HTTP response codes to indicate API errors. JSON will be returned in all responses from the API, including errors.

HTTP Request Method: POST
Resource (URI): https://api-sandbox.ctechpay.com/?endpoint=order
Request Parameters:
Include the following mandatory JSON data in your request's form or body content.

Parameter	Type	Value	Description
token	string	Your service API token	A valid authentication token
amount	string	100	The amount to be paid
merchantAttributes	boolean	true	Set to true to receive additional merchant attributes in the response
redirectUrl	string	A compulsory URL to redirect the user after a successful payment.	https://example.com/

Note: Only https requests are supported
cancelUrl	A compulsory URL to redirect the user when they choose to cancel the payment.	https://example.com/

Note: Only https requests are supported
redirectUrl	An optional custom text on the cancelUrl link	"Go back to shop," "Cancel Payment"
Example requests.
Shell
PHP (Curl)
Python
                              
http --ignore-stdin --form --follow --timeout 3600 POST 'https://api-sandbox.ctechpay.com/?endpoint=order' \
'token'='UcccctqLpVp1ZfYEujpjRpEKLplxoYbdtu123456ruchAtrwAvDOVcbj1QtZbCMf803==' \
'amount'='100'
                             
                          
Example Response
                            
{
    "order_reference": "0123erf-834fg-bsh73-dhaydm2aa",
    "payment_page_URL": "https://paypage.sandbox.com/?code=6a6a6a5b8hdgd6"
}
                    
                          
PAYMENT STATUS CHECK

Check Payment Status

Overview
This API allows you to check the status of a payment transaction using the order reference.

HTTP Request Method: POST
Resource (URI): https://api-sandbox.ctechpay.com/get_order_status/
Request Parameters:
Include the following mandatory JSON data in your request's form or body content.

Parameter	Type	Value	Description
token	string	Your service API token	A valid authentication token
orderRef	string	Order reference from payment initiation	The order reference returned when creating the payment

Example request:
                              
http --ignore-stdin --form --follow --timeout 3600 POST 'https://api-sandbox.ctechpay.com/get_order_status/' \
'token'='UcccctqLpVp1ZfYEujpjRpEKLplxoYbdtu123456ruchAtrwAvDOVcbj1QtZbCMf803==' \
'orderRef'='0123erf-834fg-bsh73-dhaydm2aa'
                             
                          
Example Response (Successful Payment)
                            
{
    "status": "PURCHASED",
    "currency_code": "MWK",
    "formatted_amount": "MWK 100",
    "card_holder_name": "JOHN DOE"
}
                            
Example Response (Failed/Pending Payment)
                            
{
    "status": "PENDING",
    "error": "Payment not completed"
}
                            
                          
AIRTEL MONEY PAYMENT                        
Request Payment from your customer

Overview
This API allows you to initiate a payment request through the CtechPay platform using the Airtel payment method.

The API is a RESTful API and utilizes HTTP request methods. The API is designed to have predictable, resource-oriented URLs and to use HTTP response codes to indicate API errors. JSON will be returned in all responses from the API, including errors.

HTTP Request Method: POST
Resource (URI): https://api-sandbox.ctechpay.com/airtel/access/
Request Parameters:
Include the following mandatory JSON data in your request's form or body content.

Parameter	Type	Value	Description
airtel	integer	1	Set to 1 to indicate that the payment is being made using the Airtel payment method.
token	string	Your service API token	A valid authentication token
amount	string	100	The amount to be paid
phone	string	Valid Airtel phonenumber eg, 0999xxxxxx	The phone number of the payer. It should start with either +265, 265, or 0
Example request body.
                              
{
    "airtel": 1,
    "token": "your_authentication_token",
    "amount": "1000",
    "phone": "+265999123456"
}                           
                          
Example Response
                            
{
    "order_reference": "0123erf-834fg-bsh73-dhaydm2aa",
    "payment_page_URL": "https://paypage.sandbox.com/?code=6a6a6a5b8hdgd6"
}
                            
                          
WEBHOOK NOTIFICATIONS

Webhook Endpoint Setup

Overview
CtechPay can send webhook notifications to your application when payment status changes occur.

HTTP Request Method: POST (from CtechPay to your endpoint)
Your Webhook URL: https://yourdomain.com/webhook/ctechpay
Request Parameters:
CtechPay will send the following data to your webhook endpoint.

Parameter	Type	Description
order_reference	string	The order reference for the payment
status	string	Payment status (PURCHASED, FAILED, CANCELLED, etc.)
amount	string	Payment amount
currency_code	string	Currency code (e.g., MWK)
timestamp	string	Timestamp of the status change

Example webhook payload:
                              
{
    "order_reference": "0123erf-834fg-bsh73-dhaydm2aa",
    "status": "PURCHASED",
    "amount": "1000",
    "currency_code": "MWK",
    "timestamp": "2024-01-15T10:30:00Z"
}
                             
                          
TESTING

Sandbox Environment
Use the sandbox environment for testing:
- API Base URL: https://api-sandbox.ctechpay.com/
- Payment Page: https://paypage.sandbox.com/

Test Credentials:
- Use test API tokens provided by CtechPay
- Test card numbers and phone numbers are available in the CtechPay documentation

                  
prompt: using the info in this file, update the card payments system in the database so that:
- User is redirected to the payment page url once they pay.
- Once the user is redirected back to redirecturl, check the status of the payment, and use that information to update the database accordingly
- update user membership once payment is confirmed
- 

MY environment configuration
CTECHPAY_API_KEY=af63128cf669c71e202a665340916c80
CTECHPAY_REGISTRATION=BICT1121
CTECHPAY_ENVIRONMENT=sandbox
