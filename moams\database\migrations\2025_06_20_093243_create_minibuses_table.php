<?php

// NOTE: The minibus owner is a user with the 'minibus_owner' role, not a separate model.

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('minibuses', function (Blueprint $table) {
            // user_id refers to a user with the 'minibus_owner' role
            $table->id();
            $table->string('number_plate')->unique();
            $table->string('make');
            $table->string('model');
            $table->year('year_of_make');
            $table->string('main_colour');
            $table->foreignId('route_id')->nullable()->constrained('vehicle_routes')->nullOnDelete();
            $table->foreignId('owner_id')->nullable()->constrained('users')->nullOnDelete();
            $table->string('proof_of_ownership');
            $table->boolean('archived')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('minibuses');
    }
};
