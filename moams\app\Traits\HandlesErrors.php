<?php

namespace App\Traits;

use App\Services\ErrorRecoveryService;
use App\Services\ErrorMonitoringService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Throwable;
use Closure;

trait HandlesErrors
{
    /**
     * Execute an operation with error handling and recovery
     */
    protected function executeWithErrorHandling(
        Closure $operation,
        string $successMessage = null,
        string $errorMessage = 'An error occurred. Please try again.',
        bool $useRetry = true,
        bool $redirectOnError = true
    ) {
        try {
            if ($useRetry) {
                $result = ErrorRecoveryService::retry($operation);
            } else {
                $result = $operation();
            }
            
            if ($successMessage && request()->expectsJson()) {
                return response()->json(['message' => $successMessage, 'data' => $result]);
            } elseif ($successMessage) {
                return redirect()->back()->with('success', $successMessage);
            }
            
            return $result;
            
        } catch (Throwable $exception) {
            // Log the error
            $errorId = ErrorMonitoringService::logError($exception, [
                'controller' => static::class,
                'method' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 2)[1]['function'] ?? 'unknown',
                'user_id' => auth()->id(),
            ]);
            
            return $this->handleError($exception, $errorMessage, $errorId, $redirectOnError);
        }
    }

    /**
     * Execute a database operation with transaction and retry
     */
    protected function executeDbOperation(
        Closure $operation,
        string $successMessage = null,
        string $errorMessage = 'Database operation failed. Please try again.'
    ) {
        try {
            $result = ErrorRecoveryService::transactionWithRetry($operation);
            
            if ($successMessage && request()->expectsJson()) {
                return response()->json(['message' => $successMessage, 'data' => $result]);
            } elseif ($successMessage) {
                return redirect()->back()->with('success', $successMessage);
            }
            
            return $result;
            
        } catch (Throwable $exception) {
            $errorId = ErrorMonitoringService::logError($exception, [
                'operation_type' => 'database',
                'controller' => static::class,
                'user_id' => auth()->id(),
            ]);
            
            return $this->handleError($exception, $errorMessage, $errorId);
        }
    }

    /**
     * Execute an HTTP request with recovery mechanisms
     */
    protected function executeHttpRequest(
        string $serviceName,
        Closure $httpRequest,
        Closure $fallback = null,
        string $errorMessage = 'External service is temporarily unavailable. Please try again later.'
    ) {
        try {
            return ErrorRecoveryService::httpRequestWithRecovery(
                $serviceName,
                $httpRequest,
                $fallback
            );
            
        } catch (Throwable $exception) {
            $errorId = ErrorMonitoringService::logError($exception, [
                'operation_type' => 'http_request',
                'service_name' => $serviceName,
                'controller' => static::class,
                'user_id' => auth()->id(),
            ]);
            
            return $this->handleError($exception, $errorMessage, $errorId);
        }
    }

    /**
     * Execute a file operation with retry
     */
    protected function executeFileOperation(
        Closure $fileOperation,
        string $successMessage = null,
        string $errorMessage = 'File operation failed. Please try again.'
    ) {
        try {
            $result = ErrorRecoveryService::fileOperationWithRetry($fileOperation);
            
            if ($successMessage && request()->expectsJson()) {
                return response()->json(['message' => $successMessage, 'data' => $result]);
            } elseif ($successMessage) {
                return redirect()->back()->with('success', $successMessage);
            }
            
            return $result;
            
        } catch (Throwable $exception) {
            $errorId = ErrorMonitoringService::logError($exception, [
                'operation_type' => 'file_operation',
                'controller' => static::class,
                'user_id' => auth()->id(),
            ]);
            
            return $this->handleError($exception, $errorMessage, $errorId);
        }
    }

    /**
     * Handle errors with appropriate response format
     */
    protected function handleError(
        Throwable $exception,
        string $userMessage,
        string $errorId,
        bool $redirectOnError = true
    ) {
        // For JSON requests (API/AJAX)
        if (request()->expectsJson()) {
            return $this->handleJsonError($exception, $userMessage, $errorId);
        }
        
        // For web requests
        return $this->handleWebError($exception, $userMessage, $errorId, $redirectOnError);
    }

    /**
     * Handle JSON error responses
     */
    protected function handleJsonError(Throwable $exception, string $userMessage, string $errorId): JsonResponse
    {
        $statusCode = method_exists($exception, 'getStatusCode') ? $exception->getStatusCode() : 500;
        
        $response = [
            'error' => true,
            'message' => $userMessage,
            'error_id' => $errorId,
        ];
        
        // Add specific error details for certain exception types
        if ($exception instanceof \Illuminate\Validation\ValidationException) {
            $response['errors'] = $exception->errors();
            $statusCode = 422;
        } elseif ($exception instanceof \App\Exceptions\PaymentException) {
            $response['payment_error'] = true;
            $response['payment_provider'] = $exception->getPaymentProvider();
            $statusCode = 422;
        } elseif ($exception instanceof \App\Exceptions\FileUploadException) {
            $response['file_upload_error'] = true;
            $response['file_name'] = $exception->getFileName();
            $statusCode = 422;
        }
        
        return response()->json($response, $statusCode);
    }

    /**
     * Handle web error responses
     */
    protected function handleWebError(
        Throwable $exception,
        string $userMessage,
        string $errorId,
        bool $redirectOnError = true
    ): RedirectResponse {
        $errorData = [
            'error' => $userMessage,
            'error_id' => $errorId,
        ];
        
        // Add validation errors to session
        if ($exception instanceof \Illuminate\Validation\ValidationException) {
            return redirect()->back()
                ->withErrors($exception->errors())
                ->withInput()
                ->with('error_id', $errorId);
        }
        
        if ($redirectOnError) {
            return redirect()->back()->with($errorData)->withInput();
        } else {
            // For cases where we don't want to redirect back (e.g., after successful creation)
            return redirect()->route('dashboard')->with($errorData);
        }
    }

    /**
     * Get user-friendly error message based on exception type
     */
    protected function getUserFriendlyErrorMessage(Throwable $exception): string
    {
        switch (true) {
            case $exception instanceof \Illuminate\Database\QueryException:
                return 'A database error occurred. Please try again later.';
                
            case $exception instanceof \GuzzleHttp\Exception\ConnectException:
            case $exception instanceof \Illuminate\Http\Client\ConnectionException:
                return 'Unable to connect to external service. Please try again later.';
                
            case $exception instanceof \Illuminate\Validation\ValidationException:
                return 'Please check your input and try again.';
                
            case $exception instanceof \App\Exceptions\PaymentException:
                return $exception->getUserMessage();
                
            case $exception instanceof \App\Exceptions\FileUploadException:
                return $exception->getUserMessage();
                
            case $exception instanceof \App\Exceptions\BusinessLogicException:
                return $exception->getUserMessage();
                
            case str_contains($exception->getMessage(), 'timeout'):
                return 'The operation timed out. Please try again.';
                
            case str_contains($exception->getMessage(), 'permission'):
                return 'You do not have permission to perform this action.';
                
            default:
                return 'An unexpected error occurred. Please try again later.';
        }
    }

    /**
     * Check if an operation should be retried based on the exception
     */
    protected function shouldRetryOperation(Throwable $exception): bool
    {
        $retryableExceptions = [
            \Illuminate\Database\QueryException::class,
            \GuzzleHttp\Exception\ConnectException::class,
            \Illuminate\Http\Client\ConnectionException::class,
        ];
        
        foreach ($retryableExceptions as $retryableClass) {
            if ($exception instanceof $retryableClass) {
                return true;
            }
        }
        
        // Check for specific error messages
        $retryableMessages = ['timeout', 'connection', 'temporary'];
        $message = strtolower($exception->getMessage());
        
        foreach ($retryableMessages as $retryableMessage) {
            if (str_contains($message, $retryableMessage)) {
                return true;
            }
        }
        
        return false;
    }
}
