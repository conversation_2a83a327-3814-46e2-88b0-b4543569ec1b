<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Hash;

class Driver extends Model
{
    /** @use HasFactory<\Database\Factories\DriverFactory> */
    use HasFactory;

    protected $fillable = [
        'first_name',
        'last_name',
        'phone_number',
        'license_number',
        'license',
        'district',
        'village_town',
        'owner_id',
        'archived',
        'minibus_id',
        'trust_score',
    ];

    protected $casts = [
        'archived' => 'boolean',
    ];

    protected $hidden = [
        'license_number', // Hide license number from JSON/array output
    ];

    /**
     * Boot the model and add event listeners
     */
    protected static function boot()
    {
        parent::boot();

        // Hash license number before saving
        static::saving(function ($driver) {
            if ($driver->isDirty('license_number') && !empty($driver->license_number)) {
                $driver->license_number = Hash::make($driver->license_number);
            }
        });
    }

    /**
     * Check if a license number already exists
     */
    public static function licenseNumberExists($licenseNumber): bool
    {
        if (empty($licenseNumber)) {
            return false;
        }

        $drivers = self::whereNotNull('license_number')->get();

        foreach ($drivers as $driver) {
            if (Hash::check($licenseNumber, $driver->license_number)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Verify if the given license number matches this driver's license number
     */
    public function verifyLicenseNumber($licenseNumber): bool
    {
        if (empty($licenseNumber) || empty($this->license_number)) {
            return false;
        }

        return Hash::check($licenseNumber, $this->license_number);
    }

    public function minibusOwner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id')->whereHas('roles', function ($q) {
            $q->where('name', 'minibus owner');
        });
    }

    public function minibus(): BelongsTo
    {
        return $this->belongsTo(Minibus::class, 'minibus_id');
    }

    public function employmentHistories(): HasMany
    {
        return $this->hasMany(DriverEmploymentHistory::class);
    }

    public function clearanceRequests(): HasMany
    {
        return $this->hasMany(DriverClearanceRequest::class);
    }

    public function misconducts(): MorphMany
    {
        return $this->morphMany(Misconduct::class, 'offender');
    }

    public function getFullNameAttribute(): string
    {
        return "{$this->first_name} {$this->last_name}";
    }

    public function scopeActive($query)
    {
        return $query->where('archived', false);
    }

    public function scopeArchived($query)
    {
        return $query->where('archived', true);
    }

    /**
     * Update trust score by deducting points
     */
    public function deductTrustScore(int $points): void
    {
        $newScore = max(0, $this->trust_score - $points);
        $this->update(['trust_score' => $newScore]);
    }

    /**
     * Get trust score status color for UI
     */
    public function getTrustScoreColorAttribute(): string
    {
        return match (true) {
            $this->trust_score >= 80 => 'green',
            $this->trust_score >= 60 => 'orange',
            default => 'red'
        };
    }

    /**
     * Get trust score status text
     */
    public function getTrustScoreStatusAttribute(): string
    {
        return match (true) {
            $this->trust_score >= 80 => 'Excellent',
            $this->trust_score >= 60 => 'Good',
            $this->trust_score >= 40 => 'Fair',
            default => 'Poor'
        };
    }

    /**
     * Check if driver has low trust score (below 60)
     */
    public function hasLowTrustScore(): bool
    {
        return $this->trust_score < 60;
    }
}
