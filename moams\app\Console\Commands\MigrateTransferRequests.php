<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class MigrateTransferRequests extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:transfer-requests';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate transfer requests from minibus_ownership_histories to minibus_transfer_requests';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $oldRequests = DB::table('minibus_ownership_histories')
            ->whereNotNull('status')
            ->whereNotNull('transfer_type')
            ->get();

        $count = 0;
        foreach ($oldRequests as $req) {
            DB::table('minibus_transfer_requests')->insert([
                'minibus_id' => $req->minibus_id,
                'owner_id' => $req->previous_owner_id,
                'transfer_type' => $req->transfer_type,
                'status' => $req->status,
                'reason' => $req->reason,
                'created_at' => $req->created_at,
                'updated_at' => $req->updated_at,
            ]);
            $count++;
        }
        $this->info("Migrated $count transfer requests.");

        // Add the following code to update user commitment statements
        $statement = 'I commit to abide by the Constitution, rules, and regulations of the Minibus Owners Association of Malawi (MOAM), and to uphold the values and objectives of the Association at all times.';
        $affected = DB::table('users')
            ->where(function ($query) {
                $query->where('commitment_statement', '1')
                    ->orWhereNull('commitment_statement');
            })
            ->update(['commitment_statement' => $statement]);
        $this->info("Updated $affected users' commitment statements.");
    }
}