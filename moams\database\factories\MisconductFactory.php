<?php

namespace Database\Factories;

use App\Models\Misconduct;
use App\Models\User;
use App\Models\Driver;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Misconduct>
 */
class MisconductFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $misconductTypes = [
            'Reckless Driving',
            'Speeding',
            'Overloading',
            'Route Deviation',
            'Poor Customer Service',
            'Vehicle Maintenance Issues',
            'Unauthorized Stops',
            'Inappropriate Behavior',
            'Operating Without Valid License',
            'Operating Outside Designated Hours',
            'Unauthorized Fare Increase',
        ];

        return [
            'name' => $this->faker->randomElement($misconductTypes),
            'description' => $this->faker->paragraph(2),
            'offense_date' => $this->faker->dateTimeBetween('-3 months', 'now'),
            'severity' => $this->faker->randomElement(['low', 'medium', 'high']),
            'resolution_status' => $this->faker->randomElement(['resolved', 'unresolved']),
        ];
    }

    /**
     * Create a misconduct for a specific user (minibus owner)
     */
    public function forUser(User $user): static
    {
        return $this->state(fn(array $attributes) => [
            'offender_type' => User::class,
            'offender_id' => $user->id,
        ]);
    }

    /**
     * Create a misconduct for a specific driver
     */
    public function forDriver(Driver $driver): static
    {
        return $this->state(fn(array $attributes) => [
            'offender_type' => Driver::class,
            'offender_id' => $driver->id,
        ]);
    }
}
