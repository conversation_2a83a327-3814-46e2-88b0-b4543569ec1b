<?php

namespace Tests\Feature;

use App\Models\Driver;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DriverLicenseNumberTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->createRoles();
    }

    private function createRoles()
    {
        \Spatie\Permission\Models\Role::create(['name' => 'association clerk']);
        \Spatie\Permission\Models\Role::create(['name' => 'minibus owner']);
        \Spatie\Permission\Models\Role::create(['name' => 'system admin']);
        \Spatie\Permission\Models\Role::create(['name' => 'association manager']);
    }

    public function test_license_number_is_hashed_when_driver_is_created()
    {
        $clerk = User::factory()->create();
        $clerk->assignRole('association clerk');

        $owner = User::factory()->create();
        $owner->assignRole('minibus owner');

        $licenseNumber = 'AB123456';

        $driver = Driver::create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'phone_number' => '**********',
            'license_number' => $licenseNumber,
            'district' => 'Blantyre',
            'village_town' => 'Area 47',
            'owner_id' => $owner->id,
        ]);

        // Verify the license number was hashed
        $this->assertNotEquals($licenseNumber, $driver->license_number);
        $this->assertTrue($driver->verifyLicenseNumber($licenseNumber));
    }

    public function test_license_number_exists_check_works_with_hashed_values()
    {
        $clerk = User::factory()->create();
        $clerk->assignRole('association clerk');

        $owner = User::factory()->create();
        $owner->assignRole('minibus owner');

        $licenseNumber = 'AB123456';

        // Create first driver
        Driver::create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'phone_number' => '**********',
            'license_number' => $licenseNumber,
            'district' => 'Blantyre',
            'village_town' => 'Area 47',
            'owner_id' => $owner->id,
        ]);

        // Check that the license number exists
        $this->assertTrue(Driver::licenseNumberExists($licenseNumber));

        // Check that a different license number doesn't exist
        $this->assertFalse(Driver::licenseNumberExists('CD789012'));
    }

    public function test_license_number_is_hidden_from_json_output()
    {
        $clerk = User::factory()->create();
        $clerk->assignRole('association clerk');

        $owner = User::factory()->create();
        $owner->assignRole('minibus owner');

        $driver = Driver::create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'phone_number' => '**********',
            'license_number' => 'AB123456',
            'district' => 'Blantyre',
            'village_town' => 'Area 47',
            'owner_id' => $owner->id,
        ]);

        $driverArray = $driver->toArray();

        // Verify license number is not in the array output
        $this->assertArrayNotHasKey('license_number', $driverArray);
    }

    public function test_license_number_verification_works_correctly()
    {
        $clerk = User::factory()->create();
        $clerk->assignRole('association clerk');

        $owner = User::factory()->create();
        $owner->assignRole('minibus owner');

        $licenseNumber = 'AB123456';

        $driver = Driver::create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'phone_number' => '**********',
            'license_number' => $licenseNumber,
            'district' => 'Blantyre',
            'village_town' => 'Area 47',
            'owner_id' => $owner->id,
        ]);

        // Verify correct license number
        $this->assertTrue($driver->verifyLicenseNumber($licenseNumber));

        // Verify incorrect license number
        $this->assertFalse($driver->verifyLicenseNumber('CD789012'));

        // Verify empty license number
        $this->assertFalse($driver->verifyLicenseNumber(''));
        $this->assertFalse($driver->verifyLicenseNumber(null));
    }

    public function test_license_number_uniqueness_is_maintained_with_hashing()
    {
        $clerk = User::factory()->create();
        $clerk->assignRole('association clerk');

        $owner = User::factory()->create();
        $owner->assignRole('minibus owner');

        $licenseNumber = 'AB123456';

        // Create first driver through form request
        $response = $this->actingAs($clerk)
            ->post('/drivers', [
                'first_name' => 'John',
                'last_name' => 'Doe',
                'phone_number' => '**********',
                'license_number' => $licenseNumber,
                'district' => 'Blantyre',
                'village_town' => 'Area 47',
                'owner_id' => $owner->id,
            ]);

        $response->assertRedirect('/drivers');

        // Try to create second driver with same license number
        $response = $this->actingAs($clerk)
            ->from('/drivers/create')
            ->post('/drivers', [
                'first_name' => 'Jane',
                'last_name' => 'Smith',
                'phone_number' => '**********',
                'license_number' => $licenseNumber, // Same license number
                'district' => 'Blantyre',
                'village_town' => 'Area 25',
                'owner_id' => $owner->id,
            ]);

        // Should redirect back with validation errors
        $response->assertRedirect('/drivers/create');
        $response->assertSessionHasErrors('license_number');
    }
}
