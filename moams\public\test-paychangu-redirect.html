<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Paychangu Redirect</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .result {
            background: #e8f5e8;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid #4caf50;
        }
        button {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .test-link {
            display: block;
            margin: 10px 0;
            padding: 10px;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            text-decoration: none;
            color: #333;
        }
        .test-link:hover {
            background: #f0f0f0;
        }
    </style>
</head>
<body>
    <h1>Paychangu Redirect Test</h1>
    
    <div class="test-section">
        <h2>Server-Side Redirect Tests</h2>
        <p>These links test the new server-side redirect route that should handle the port issue:</p>
        
        <a href="/paychangu-redirect?tx_ref=MOAMS_1234567890_TEST&status=successful" class="test-link" target="_blank">
            <strong>Test Success Redirect</strong><br>
            <small>/paychangu-redirect?tx_ref=MOAMS_1234567890_TEST&status=successful</small>
        </a>
        
        <a href="/paychangu-redirect?tx_ref=MOAMS_1234567890_TEST&status=failed" class="test-link" target="_blank">
            <strong>Test Failed Redirect</strong><br>
            <small>/paychangu-redirect?tx_ref=MOAMS_1234567890_TEST&status=failed</small>
        </a>
        
        <a href="/paychangu-redirect?tx_ref=MOAMS_1234567890_TEST" class="test-link" target="_blank">
            <strong>Test TX_REF Only</strong><br>
            <small>/paychangu-redirect?tx_ref=MOAMS_1234567890_TEST</small>
        </a>
        
        <a href="/paychangu-redirect" class="test-link" target="_blank">
            <strong>Test No Parameters</strong><br>
            <small>/paychangu-redirect</small>
        </a>
    </div>
    
    <div class="test-section">
        <h2>JavaScript Redirect Tests (Backup)</h2>
        <p>These test the HTML file redirect (backup method):</p>
        
        <a href="/redirect-paychangu.html?tx_ref=MOAMS_1234567890_TEST&status=successful" class="test-link" target="_blank">
            <strong>Test HTML Success Redirect</strong><br>
            <small>/redirect-paychangu.html?tx_ref=MOAMS_1234567890_TEST&status=successful</small>
        </a>
        
        <a href="/redirect-paychangu.html?tx_ref=MOAMS_1234567890_TEST" class="test-link" target="_blank">
            <strong>Test HTML TX_REF Only</strong><br>
            <small>/redirect-paychangu.html?tx_ref=MOAMS_1234567890_TEST</small>
        </a>
    </div>
    
    <div class="test-section">
        <h2>Expected Behavior</h2>
        <ul>
            <li><strong>Server-side redirects:</strong> Should immediately redirect to <code>http://127.0.0.1:8000/payments/paychangu/return?...</code></li>
            <li><strong>HTML redirects:</strong> Should show loading page, then redirect via JavaScript</li>
            <li><strong>All redirects:</strong> Should preserve the tx_ref and status parameters</li>
            <li><strong>Port handling:</strong> Should always include <code>:8000</code> in the final URL</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>Debugging</h2>
        <p>Check the browser developer console and Laravel logs for debugging information.</p>
        <p>Laravel logs location: <code>storage/logs/laravel.log</code></p>
    </div>
</body>
</html>
