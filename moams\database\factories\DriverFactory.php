<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\Member;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Driver>
 */
class DriverFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $minibusId = \App\Models\Minibus::inRandomOrder()->first()?->id;
        return [
            'owner_id' => User::factory()->afterCreating(function ($user) {
                $user->assignRole('minibus owner');
            }),
            'first_name' => fake()->firstName(),
            'last_name' => fake()->lastName(),
            'phone_number' => fake()->unique()->phoneNumber(),
            'license_number' => fake()->unique()->regexify('[A-Z]{2}[0-9]{6}'),
            'license' => 'licenses/' . fake()->uuid . '.pdf',
            'district' => fake()->city(),
            'village_town' => fake()->city(),
            'archived' => false,
            'minibus_id' => $minibusId,
        ];
    }

    /**
     * Indicate that the driver is archived.
     */
    public function archived(): static
    {
        return $this->state(fn(array $attributes) => [
            'archived' => true,
        ]);
    }
}
