import{r as u,u as P,j as e,H as B,L as l,B as i,C as r,a as c,b as o,c as d,h as E,e as j}from"./app-DL-qYY5V.js";import{A as F,b as M,c as U,d as V,B as h}from"./app-layout-YqstQnqE.js";import{D as q,a as O,b as I,c as W,d as z}from"./dialog-CXtul0Wy.js";import{T as y}from"./textarea-SHrtPYpi.js";import{C as Q}from"./confirm-dialog-B1e93Onq.js";import{A as C}from"./arrow-left-DCW23wrL.js";import{U as G}from"./users-DNGXY-sJ.js";import{S as J}from"./square-pen-BT4HeE62.js";import{H as K}from"./history-Boy80jYJ.js";import{A as L}from"./archive-uWgoYkgZ.js";import"./index-1tPPImJd.js";import"./index-BTzg1GwG.js";import"./app-logo-icon-BnXlkpcX.js";import"./bus-CKTBKI0T.js";import"./dollar-sign-Cx0-nQIX.js";import"./clipboard-list-CxRumT8v.js";function ue({driver:s,userRole:t,flash:X,employmentHistories:Y}){var D,_;const $=[{title:"Dashboard",href:"/dashboard"},{title:t==="minibus owner"?"My Drivers":"Driver Management",href:"/drivers"},{title:`${s.first_name} ${s.last_name}`,href:`/drivers/${s.id}`}],[a,x]=u.useState({open:!1,action:null}),[k,f]=u.useState(!1),{data:p,setData:g,patch:A,processing:S,errors:m,reset:N}=P({reason:"",admin_notes:""}),[T,b]=u.useState(!1),v=n=>{x({open:!0,action:n})},H=()=>{x({open:!1,action:null}),N()},w=async()=>{f(!0),a.action==="delete"?await j.delete(route("drivers.destroy",s.id)):a.action==="unarchive"?await j.patch(route("drivers.unarchive",s.id),{},{onSuccess:()=>j.reload()}):a.action==="clear"&&await A(route("drivers.direct-clear",s.id),{onSuccess:()=>{N()}}),f(!1),x({open:!1,action:null})};return e.jsxs(F,{breadcrumbs:$,children:[e.jsx(B,{title:`Driver - ${s.first_name} ${s.last_name}`}),e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{className:"flex items-center space-x-4 mb-6",children:[e.jsx(l,{href:route("drivers.index"),children:e.jsxs(i,{variant:"outline",size:"sm",children:[e.jsx("span",{className:"block sm:hidden",children:e.jsxs(M,{children:[e.jsx(U,{asChild:!0,children:e.jsx("span",{children:e.jsx(C,{className:"h-4 w-4"})})}),e.jsx(V,{children:"Back to Drivers"})]})}),e.jsxs("span",{className:"hidden sm:flex items-center",children:[e.jsx(C,{className:"mr-2 h-4 w-4"}),"Back to Drivers"]})]})}),e.jsx("span",{className:"text-muted-foreground text-base font-medium",children:"View detailed information about this driver and their employment history."})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[e.jsxs("div",{className:"lg:col-span-2 space-y-8",children:[e.jsxs(r,{className:"mb-6",children:[e.jsx(c,{children:e.jsxs(o,{className:"flex items-center gap-2",children:[e.jsx(G,{className:"h-5 w-5"}),"Personal Information"]})}),e.jsx(d,{className:"space-y-4",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"First Name"}),e.jsx("p",{className:"text-sm",children:s.first_name})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Last Name"}),e.jsx("p",{className:"text-sm",children:s.last_name})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Phone Number"}),e.jsx("p",{className:"text-sm",children:s.phone_number})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Status"}),e.jsx("div",{className:"flex items-center gap-2",children:s.archived?e.jsx(h,{className:"bg-red-100 text-red-800 border border-red-600",children:"Archived"}):e.jsx(h,{className:"bg-green-100 text-green-800 border border-green-600",children:"Active"})})]})]})})]}),e.jsxs(r,{className:"mb-6",children:[e.jsx(c,{children:e.jsx(o,{children:"Location Information"})}),e.jsx(d,{className:"space-y-4",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"District"}),e.jsx("p",{className:"text-sm",children:s.district})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Village/Town"}),e.jsx("p",{className:"text-sm",children:s.village_town})]})]})})]}),e.jsxs(r,{children:[e.jsx(c,{children:e.jsx(o,{children:"Employment Information"})}),e.jsx(d,{className:"space-y-4",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Minibus Owner"}),e.jsx("p",{className:"text-sm",children:s.minibus_owner?`${s.minibus_owner.first_name??((D=s.minibus_owner.user)==null?void 0:D.first_name)??""} ${s.minibus_owner.last_name??((_=s.minibus_owner.user)==null?void 0:_.last_name)??""}`.trim():"Not assigned"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Registration Date"}),e.jsx("p",{className:"text-sm",children:new Date(s.created_at).toLocaleDateString()})]})]})})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs(r,{className:"mb-6",children:[e.jsx(c,{children:e.jsx(o,{children:"Actions"})}),e.jsx(d,{children:e.jsxs("div",{className:"flex flex-col gap-3",children:[t==="minibus owner"&&e.jsx(l,{href:`/misconducts/create?driver_id=${s.id}`,className:"w-full",children:e.jsxs(i,{variant:"outline",className:"w-full border-red-600 text-red-600 hover:bg-red-50",children:[e.jsx(E,{className:"mr-2 h-4 w-4"}),"Report Misconduct"]})}),t==="association clerk"&&e.jsx(l,{href:route("drivers.edit",s.id),className:"w-full",children:e.jsxs(i,{variant:"outline",className:"w-full",children:[e.jsx(J,{className:"mr-2 h-4 w-4"}),"Edit Driver"]})}),t==="association clerk"&&e.jsx(l,{href:route("drivers.history",s.id),className:"w-full",children:e.jsxs(i,{variant:"outline",className:"w-full",children:[e.jsx(K,{className:"mr-2 h-4 w-4"}),"View History"]})}),!s.archived&&t==="minibus owner"&&e.jsx(l,{href:route("drivers.request-clearance",s.id),className:"w-full",children:e.jsxs(i,{variant:"outline",className:"w-full",children:[e.jsx(L,{className:"mr-2 h-4 w-4"}),"Request Clearance"]})}),s.license&&t&&(t==="association clerk"||t==="system admin")&&e.jsx(i,{variant:"outline",className:"w-full justify-center bg-blue-100 text-blue-800 border-blue-300 hover:bg-blue-200",onClick:()=>b(!0),children:"View License"}),t==="association clerk"&&s.archived&&e.jsxs(i,{variant:"outline",className:"w-full",onClick:()=>v("unarchive"),children:[e.jsx(L,{className:"mr-2 h-4 w-4"}),"Unarchive Driver"]}),t==="association clerk"&&!s.archived&&e.jsx(i,{variant:"destructive",className:"w-full",onClick:()=>v("clear"),children:"Clear Driver"})]})})]}),e.jsxs(r,{children:[e.jsx(c,{children:e.jsx(o,{children:"Quick Stats"})}),e.jsxs(d,{className:"space-y-3",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Status"}),s.archived?e.jsx(h,{className:"bg-red-100 text-red-800 border border-red-600",children:"Archived"}):e.jsx(h,{className:"bg-green-100 text-green-800 border border-green-600",children:"Active"})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Registered"}),e.jsx("span",{className:"text-sm",children:new Date(s.created_at).toLocaleDateString()})]})]})]})]})]})]})}),e.jsx(q,{open:T,onOpenChange:b,children:e.jsxs(O,{className:"max-w-2xl",children:[e.jsxs(I,{children:[e.jsx(W,{className:"text-center w-full",children:"Driver's License"}),e.jsx(z,{className:"text-center w-full",children:s.license&&s.license.endsWith(".pdf")?"Preview of the uploaded Driver License (PDF).":"Preview of the uploaded Driver License (image)."})]}),s.license&&s.license.endsWith(".pdf")?e.jsx("iframe",{src:`/storage/${s.license}`,title:"Driver License PDF",className:"w-full h-[70vh] border"}):s.license?e.jsx("img",{src:`/storage/${s.license}`,alt:"Driver License",className:"w-full max-h-[70vh] object-contain"}):null]})}),e.jsx(Q,{open:a.open,title:a.action==="delete"?`Delete ${s.first_name} ${s.last_name}?`:a.action==="unarchive"?`Unarchive ${s.first_name} ${s.last_name}?`:a.action==="clear"?`Clear ${s.first_name} ${s.last_name}?`:"Are you sure?",description:a.action==="delete"?"This action cannot be undone and will permanently remove the driver from the system.":a.action==="unarchive"?"This driver will be restored and regain active status.":a.action==="clear"?"Please provide a reason for clearing this driver. This action will archive the driver and update their employment history.":"",confirmText:a.action==="delete"?"Delete":a.action==="unarchive"?"Unarchive":a.action==="clear"?"Clear Driver":"Confirm",confirmVariant:a.action==="delete"||a.action==="clear"?"destructive":"default",loading:k||S,onCancel:H,onConfirm:w,children:a.action==="clear"&&e.jsxs("form",{className:"space-y-4 mt-4",onSubmit:n=>{n.preventDefault(),w()},children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"reason",className:"block text-sm font-medium",children:"Reason for Clearance *"}),e.jsx(y,{id:"reason",value:p.reason,onChange:n=>g("reason",n.target.value),placeholder:"Enter reason...",rows:4,required:!0}),m.reason&&e.jsx("div",{className:"text-sm text-red-500 mt-1",children:m.reason})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"admin_notes",className:"block text-sm font-medium",children:"Administrative Notes (optional)"}),e.jsx(y,{id:"admin_notes",value:p.admin_notes,onChange:n=>g("admin_notes",n.target.value),placeholder:"Enter notes...",rows:3}),m.admin_notes&&e.jsx("div",{className:"text-sm text-red-500 mt-1",children:m.admin_notes})]})]})})]})}export{ue as default};
