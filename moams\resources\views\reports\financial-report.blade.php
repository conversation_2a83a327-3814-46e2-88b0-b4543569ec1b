<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $title }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
            line-height: 1.4;
        }

        .report-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #2563eb;
            padding-bottom: 20px;
        }

        .header h1 {
            color: #2563eb;
            margin: 0;
            font-size: 24px;
        }

        .header h2 {
            color: #64748b;
            margin: 5px 0 0 0;
            font-size: 16px;
            font-weight: normal;
        }

        .report-info {
            background-color: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 25px;
            border-left: 4px solid #2563eb;
        }

        .report-info h3 {
            margin: 0 0 10px 0;
            color: #2563eb;
            font-size: 16px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
        }

        .info-label {
            font-weight: bold;
            color: #475569;
        }
        
        .summary-section {
            margin-bottom: 30px;
        }
        
        .section-title {
            color: #1e40af;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 5px;
        }
        
        .summary-grid {
            display: table;
            width: 100%;
            margin-bottom: 20px;
        }
        
        .summary-item {
            display: table-cell;
            width: 25%;
            text-align: center;
            padding: 15px;
            background: #f8fafc;
            border: 1px solid #e5e7eb;
        }
        
        .summary-item:first-child {
            border-radius: 8px 0 0 8px;
        }
        
        .summary-item:last-child {
            border-radius: 0 8px 8px 0;
        }
        
        .summary-label {
            font-size: 12px;
            color: #6b7280;
            text-transform: uppercase;
            font-weight: bold;
        }
        
        .summary-value {
            font-size: 20px;
            font-weight: bold;
            color: #059669;
            margin-top: 5px;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        
        .data-table th {
            background: #1e40af;
            color: white;
            padding: 12px;
            text-align: left;
            font-weight: bold;
            font-size: 14px;
        }
        
        .data-table td {
            padding: 10px 12px;
            border-bottom: 1px solid #e5e7eb;
            font-size: 13px;
        }
        
        .data-table tr:nth-child(even) {
            background: #f8fafc;
        }
        
        .data-table tr:hover {
            background: #e5e7eb;
        }
        
        .amount {
            text-align: right;
            font-weight: bold;
            color: #059669;
        }
        
        .count {
            text-align: center;
            font-weight: bold;
        }
        
        .payment-methods {
            margin-bottom: 30px;
        }
        
        .method-item {
            display: table;
            width: 100%;
            margin-bottom: 10px;
            background: #f8fafc;
            border-radius: 6px;
            padding: 10px;
        }
        
        .method-name {
            display: table-cell;
            width: 40%;
            font-weight: bold;
            color: #374151;
        }
        
        .method-stats {
            display: table-cell;
            width: 60%;
            text-align: right;
        }
        
        .top-members {
            margin-bottom: 30px;
        }
        
        .member-item {
            display: table;
            width: 100%;
            margin-bottom: 8px;
            padding: 8px;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
        }
        
        .member-name {
            display: table-cell;
            width: 50%;
            font-weight: bold;
        }
        
        .member-stats {
            display: table-cell;
            width: 50%;
            text-align: right;
            color: #059669;
            font-weight: bold;
        }
        
        .report-footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            text-align: center;
            font-size: 12px;
            color: #6b7280;
        }
        
        .page-break {
            page-break-before: always;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 15px;
            }

            .header {
                margin-bottom: 20px;
                padding-bottom: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="report-container">
        <!-- Report Header -->
        <div class="header">
            <!-- Professional Text Logo - No image dependencies -->
            <div style="width: 120px; height: 60px; margin: 0 auto 15px auto; background-color: #2563eb; border-radius: 12px; position: relative; color: white; font-weight: bold;">
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 18px; letter-spacing: 1px; text-align: center; white-space: nowrap;">
                    MOAMS
                </div>
            </div>
            <h1>{{ $company_info['name'] }}</h1>
            <h2>{{ $title }}</h2>
        </div>

        <!-- Report Information -->
        <div class="report-info">
            <h3>Report Information</h3>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">Report Period:</span>
                    <span>{{ ucfirst($period) }}
                        @if($period === 'monthly')
                            - {{ $monthName }} {{ $year }}
                        @else
                            - {{ $year }}
                        @endif
                    </span>
                </div>
                <div class="info-item">
                    <span class="info-label">Generated By:</span>
                    <span>{{ $generated_by }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Generated On:</span>
                    <span>{{ $generated_at }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Report Type:</span>
                    <span>Financial Analytics</span>
                </div>
            </div>
        </div>
        
        <!-- Summary Section -->
        <div class="summary-section">
            <h2 class="section-title">Financial Summary</h2>
            <div class="summary-grid">
                <div class="summary-item">
                    <div class="summary-label">Total Revenue</div>
                    <div class="summary-value">MWK {{ number_format($summary['total_revenue'], 2) }}</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">Total Payments</div>
                    <div class="summary-value">{{ number_format($summary['total_payments']) }}</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">Registration Fees</div>
                    <div class="summary-value">MWK {{ number_format($summary['registration_revenue'], 2) }}</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">Affiliation Fees</div>
                    <div class="summary-value">MWK {{ number_format($summary['affiliation_revenue'], 2) }}</div>
                </div>
            </div>
        </div>
        
        <!-- Detailed Data -->
        <div class="data-section">
            <h2 class="section-title">Detailed {{ ucfirst($period) }} Data</h2>
            <table class="data-table">
                <thead>
                    <tr>
                        @if($period === 'daily')
                            <th>Date</th>
                        @elseif($period === 'weekly')
                            <th>Week Period</th>
                        @elseif($period === 'monthly')
                            <th>Month</th>
                        @else
                            <th>Year</th>
                        @endif
                        <th>Total Payments</th>
                        <th>Total Amount</th>
                        <th>Registration Fees</th>
                        <th>Affiliation Fees</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($data as $row)
                        <tr>
                            <td>
                                @if($period === 'daily')
                                    {{ \Carbon\Carbon::parse($row->date)->format('M j, Y') }}
                                @elseif($period === 'weekly')
                                    {{ \Carbon\Carbon::parse($row->week_start)->format('M j') }} - {{ \Carbon\Carbon::parse($row->week_end)->format('M j, Y') }}
                                @elseif($period === 'monthly')
                                    {{ \Carbon\Carbon::create($year, $row->month, 1)->format('F') }}
                                @else
                                    {{ $row->year }}
                                @endif
                            </td>
                            <td class="count">{{ number_format($row->total_payments) }}</td>
                            <td class="amount">MWK {{ number_format($row->total_amount, 2) }}</td>
                            <td class="amount">MWK {{ number_format($row->registration_amount, 2) }}</td>
                            <td class="amount">MWK {{ number_format($row->affiliation_amount, 2) }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        
        <!-- Payment Methods Breakdown -->
        @if($payment_methods->isNotEmpty())
            <div class="payment-methods">
                <h2 class="section-title">Payment Methods Breakdown</h2>
                @foreach($payment_methods as $method)
                    <div class="method-item">
                        <div class="method-name">{{ ucfirst(str_replace('_', ' ', $method->payment_method)) }}</div>
                        <div class="method-stats">
                            {{ number_format($method->count) }} payments | MWK {{ number_format($method->total_amount, 2) }}
                        </div>
                    </div>
                @endforeach
            </div>
        @endif
        
        <!-- Top Paying Members -->
        @if($top_members->isNotEmpty())
            <div class="top-members">
                <h2 class="section-title">Top Paying Members</h2>
                @foreach($top_members as $member)
                    <div class="member-item">
                        <div class="member-name">
                            {{ $member->user->first_name }} {{ $member->user->last_name }}
                            <br><small style="color: #6b7280;">{{ $member->user->email }}</small>
                        </div>
                        <div class="member-stats">
                            {{ number_format($member->payment_count) }} payments | MWK {{ number_format($member->total_amount, 2) }}
                        </div>
                    </div>
                @endforeach
            </div>
        @endif
        
        <!-- Report Footer -->
        <div class="report-footer">
            <p><strong>Confidential Financial Report</strong></p>
            <p>This report contains confidential financial information and should be handled according to organizational policies.</p>
            <p>Generated by MOAMS Financial Management System</p>
        </div>
    </div>
</body>
</html>
