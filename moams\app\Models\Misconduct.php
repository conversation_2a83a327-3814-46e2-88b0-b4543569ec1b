<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Misconduct extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'offense_date',
        'severity',
        'original_severity',
        'minibus_id',
        'reported_by',
        'evidence_file',
        'points_deducted',
        'resolution_status',
        'resolution_notes',
        'resolved_by',
        'resolved_at',
        'severity_reviewed_by',
        'severity_reviewed_at',
        'severity_review_notes',
        'offender_type',
        'offender_id',
    ];

    protected $casts = [
        'offense_date' => 'date',
        'resolved_at' => 'datetime',
    ];

    public function offender(): MorphTo
    {
        return $this->morphTo();
    }

    public function minibus()
    {
        return $this->belongsTo(\App\Models\Minibus::class);
    }

    public function reportedBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'reported_by');
    }

    public function resolvedBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'resolved_by');
    }

    public function severityReviewedBy()
    {
        return $this->belongsTo(\App\Models\User::class, 'severity_reviewed_by');
    }

    /**
     * Get the severity color for UI display
     */
    public function getSeverityColorAttribute(): string
    {
        return match ($this->severity) {
            'low' => 'green',
            'medium' => 'orange',
            'high' => 'red',
            default => 'gray'
        };
    }

    /**
     * Get points to deduct based on severity
     */
    public static function getPointsForSeverity(string $severity): int
    {
        return match ($severity) {
            'low' => 5,
            'medium' => 10,
            'high' => 20,
            default => 10
        };
    }

    /**
     * Check if misconduct is resolved
     */
    public function isResolved(): bool
    {
        return $this->resolution_status === 'resolved';
    }

    /**
     * Get resolution status color for UI
     */
    public function getResolutionStatusColorAttribute(): string
    {
        return $this->isResolved() ? 'green' : 'orange';
    }

    /**
     * Mark misconduct as resolved
     */
    public function markAsResolved(int $userId, string $notes = null): void
    {
        $this->update([
            'resolution_status' => 'resolved',
            'resolution_notes' => $notes,
            'resolved_by' => $userId,
            'resolved_at' => now(),
        ]);
    }

    /**
     * Mark misconduct as unresolved
     */
    public function markAsUnresolved(): void
    {
        $this->update([
            'resolution_status' => 'unresolved',
            'resolution_notes' => null,
            'resolved_by' => null,
            'resolved_at' => null,
        ]);
    }
}
