import * as React from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
    Command,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
    CommandList,
} from "@/components/ui/command";
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@/components/ui/popover";
import { ChevronsUpDown, Check, Loader2 } from "lucide-react";

// Debounce hook
function useDebounce(value, delay) {
    const [debounced, setDebounced] = React.useState(value);
    React.useEffect(() => {
        const handler = setTimeout(() => setDebounced(value), delay);
        return () => clearTimeout(handler);
    }, [value, delay]);
    return debounced;
}

export function DriverCombobox({
    value,
    onChange,
    placeholder = "Select driver...",
    fetchDrivers, // async function: (search) => Promise<drivers[]>
}) {
    const [open, setOpen] = React.useState(false);
    const [search, setSearch] = React.useState("");
    const [loading, setLoading] = React.useState(false);
    const [options, setOptions] = React.useState([]);
    const debouncedSearch = useDebounce(search, 300);

    // Fetch drivers when popover is opened and search is empty (initial load)
    React.useEffect(() => {
        if (open && search === "") {
            setLoading(true);
            fetchDrivers("").then(results => {
                const driversWithStringId = results.map(d => ({ ...d, id: String(d.id) }));
                const uniqueDrivers = [];
                const seenIds = new Set();
                for (const driver of driversWithStringId) {
                    if (!seenIds.has(driver.id)) {
                        uniqueDrivers.push(driver);
                        seenIds.add(driver.id);
                    }
                }
                setOptions(uniqueDrivers);
                setLoading(false);
            });
        }
        // eslint-disable-next-line
    }, [open]);

    // Fetch drivers when debounced search changes (user types)
    React.useEffect(() => {
        if (debouncedSearch !== "") {
            setLoading(true);
            fetchDrivers(debouncedSearch).then(results => {
                const driversWithStringId = results.map(d => ({ ...d, id: String(d.id) }));
                const uniqueDrivers = [];
                const seenIds = new Set();
                for (const driver of driversWithStringId) {
                    if (!seenIds.has(driver.id)) {
                        uniqueDrivers.push(driver);
                        seenIds.add(driver.id);
                    }
                }
                setOptions(uniqueDrivers);
                setLoading(false);
            });
        }
    }, [debouncedSearch, fetchDrivers]);

    return (
        <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
                <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={open}
                    className="w-full justify-between h-9 px-3 py-1 text-base rounded-md border border-input bg-transparent font-normal"
                >
                    {value
                        ? value.name || placeholder
                        : <span className="text-muted-foreground">{placeholder}</span>}
                    {open && loading ? (
                        <Loader2 className="ml-2 h-4 w-4 animate-spin opacity-50" />
                    ) : (
                        <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
                    )}
                </Button>
            </PopoverTrigger>
            <PopoverContent className="w-full p-0">
                <Command>
                    <CommandInput
                        placeholder="Search driver..."
                        value={search}
                        onValueChange={setSearch}
                    />
                    <CommandList>
                        <CommandEmpty>{loading ? "Searching..." : "Drivers not found."}</CommandEmpty>
                        <CommandGroup>
                            {options.map((driver) => (
                                <CommandItem
                                    key={driver.id}
                                    value={driver.name}
                                    onSelect={() => {
                                        onChange(driver);
                                        setOpen(false);
                                    }}
                                >
                                    <Check
                                        className={
                                            value && value.id === driver.id
                                                ? "mr-2 h-4 w-4 opacity-100"
                                                : "mr-2 h-4 w-4 opacity-0"
                                        }
                                    />
                                    {driver.name} {driver.phone_number ? <span className="text-xs text-muted-foreground">({driver.phone_number})</span> : null}
                                </CommandItem>
                            ))}
                        </CommandGroup>
                    </CommandList>
                </Command>
            </PopoverContent>
        </Popover>
    );
} 