import{i as D,r as j,u as M,j as e,H as I,L as g,C as T,a as L,b as F,h as E,c as O,B as N}from"./app-DL-qYY5V.js";import{I as v}from"./input-Dm4SEXxy.js";import{L as d}from"./label-e3QxUH-L.js";import{T as P}from"./textarea-SHrtPYpi.js";import{S as y,a as _,b,c as S,d as p}from"./select-Cp8NjZe8.js";import{A as k,X as U}from"./app-layout-YqstQnqE.js";import{I as l}from"./input-error-C6jcuIY6.js";import{C as B}from"./chevron-left-DFeVEtK7.js";import"./index-BTzg1GwG.js";import"./index-1tPPImJd.js";import"./index-uC6ZAdKJ.js";import"./app-logo-icon-BnXlkpcX.js";import"./bus-CKTBKI0T.js";import"./dollar-sign-Cx0-nQIX.js";import"./users-DNGXY-sJ.js";import"./clipboard-list-CxRumT8v.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const H=[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]],V=D("Save",H);function $({value:t,onChange:o,drivers:m,placeholder:f}){const[u,n]=j.useState(!1),[i,h]=j.useState(""),c=m.filter(a=>{var x;return`${a.first_name} ${a.last_name}`.toLowerCase().includes(i.toLowerCase())||((x=a.phone_number)==null?void 0:x.includes(i))}),r=m.find(a=>a.id===(t==null?void 0:t.id));return e.jsxs("div",{className:"relative",children:[e.jsx(v,{placeholder:f,value:r?`${r.first_name} ${r.last_name}`:i,onChange:a=>h(a.target.value),onFocus:()=>n(!0),onBlur:()=>setTimeout(()=>n(!1),200)}),u&&e.jsx("div",{className:"absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto",children:c.length===0?e.jsx("div",{className:"px-3 py-2 text-gray-500",children:"No drivers found"}):c.map(a=>e.jsxs("div",{className:"px-3 py-2 hover:bg-gray-100 cursor-pointer",onClick:()=>{o(a),h(""),n(!1)},children:[e.jsxs("div",{className:"font-medium",children:[a.first_name," ",a.last_name]}),e.jsx("div",{className:"text-sm text-gray-500",children:a.phone_number})]},a.id))})]})}function re({misconduct:t,drivers:o}){const m=s=>s?new Date(s).toISOString().split("T")[0]:"",[f,u]=j.useState(o.find(s=>s.id===t.offender_id)||null),{data:n,setData:i,post:h,processing:c,errors:r}=M({name:t.name,description:t.description||"",offense_date:m(t.offense_date),severity:t.severity||"medium",evidence_file:null,offender_type:"driver",offender_id:t.offender_id}),a=["Reckless Driving","Speeding","Overloading","Route Deviation","Poor Customer Service","Vehicle Maintenance Issues","Late Arrival","Unauthorized Stops","Inappropriate Behavior","Other"],x=s=>{s.preventDefault(),h(`/misconducts/${t.id}`,{forceFormData:!0,data:{...n,_method:"PUT"}})},C=s=>{u(s),i("offender_id",(s==null?void 0:s.id)||"")},w=[{title:"Dashboard",href:"/dashboard"},{title:"Misconduct Management",href:"/misconducts"},{title:"Edit Misconduct",href:`/misconducts/${t.id}/edit`}];return e.jsxs(k,{breadcrumbs:w,children:[e.jsx(I,{title:"Edit Driver Misconduct"}),e.jsxs("div",{className:"container mx-auto px-4 py-8 max-w-2xl",children:[e.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[e.jsxs(g,{href:"/misconducts",className:"flex items-center px-3 py-1 rounded hover:bg-gray-100 border border-gray-200",children:[e.jsx(B,{className:"h-4 w-4 mr-1"})," Back"]}),e.jsx("div",{children:e.jsx("p",{className:"text-gray-600",children:"Update misconduct incident details"})})]}),e.jsxs(T,{children:[e.jsx(L,{children:e.jsxs(F,{className:"flex items-center gap-2",children:[e.jsx(E,{className:"h-5 w-5 text-indigo-600"}),"Update Misconduct Details"]})}),e.jsx(O,{children:e.jsxs("form",{onSubmit:x,className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx(d,{htmlFor:"name",children:"Misconduct Type *"}),e.jsxs(y,{value:n.name,onValueChange:s=>i("name",s),children:[e.jsx(_,{children:e.jsx(b,{placeholder:"Select misconduct type"})}),e.jsx(S,{children:a.map(s=>e.jsx(p,{value:s,children:s},s))})]}),e.jsx(l,{message:r.name,className:"mt-2"})]}),e.jsxs("div",{children:[e.jsx(d,{htmlFor:"offender_id",children:"Select Driver *"}),e.jsx($,{value:f,onChange:C,drivers:o,placeholder:"Search and select a driver..."}),e.jsx(l,{message:r.offender_id,className:"mt-2"})]}),e.jsxs("div",{children:[e.jsx(d,{htmlFor:"offense_date",children:"Offense Date *"}),e.jsx(v,{id:"offense_date",type:"date",value:n.offense_date,onChange:s=>i("offense_date",s.target.value),max:new Date().toISOString().split("T")[0],required:!0}),e.jsx(l,{message:r.offense_date,className:"mt-2"})]}),e.jsxs("div",{children:[e.jsx(d,{htmlFor:"severity",children:"Severity Level *"}),e.jsxs(y,{value:n.severity,onValueChange:s=>i("severity",s),children:[e.jsx(_,{children:e.jsx(b,{placeholder:"Select severity level"})}),e.jsxs(S,{children:[e.jsx(p,{value:"low",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-3 h-3 rounded-full bg-green-500"}),"Low (5 points deducted)"]})}),e.jsx(p,{value:"medium",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-3 h-3 rounded-full bg-orange-500"}),"Medium (10 points deducted)"]})}),e.jsx(p,{value:"high",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-3 h-3 rounded-full bg-red-500"}),"High (20 points deducted)"]})})]})]}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Points will be adjusted based on severity changes."}),e.jsx(l,{message:r.severity,className:"mt-2"})]}),e.jsxs("div",{children:[e.jsx(d,{htmlFor:"description",children:"Description"}),e.jsx(P,{id:"description",value:n.description,onChange:s=>i("description",s.target.value),placeholder:"Provide detailed description of the misconduct incident...",rows:4,className:"resize-none"}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Include specific details about what happened and any relevant circumstances."}),e.jsx(l,{message:r.description,className:"mt-2"})]}),e.jsxs("div",{children:[e.jsx(d,{htmlFor:"evidence_file",children:"Evidence (Optional)"}),t.evidence_file&&e.jsxs("p",{className:"text-sm text-gray-600 mb-2",children:["Current file: ",t.evidence_file.split("/").pop()]}),e.jsx(v,{id:"evidence_file",type:"file",accept:".pdf,.jpg,.jpeg,.png",onChange:s=>i("evidence_file",s.target.files[0])}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Upload new evidence to replace existing file (PDF, JPG, JPEG, PNG - Max 5MB)"}),e.jsx(l,{message:r.evidence_file,className:"mt-2"})]}),e.jsxs("div",{className:"flex gap-4 pt-4",children:[e.jsxs(N,{type:"submit",disabled:c,className:"flex-1",children:[e.jsx(V,{className:"h-4 w-4 mr-2"}),c?"Updating...":"Update Misconduct"]}),e.jsx(g,{href:"/misconducts",className:"flex-1",children:e.jsxs(N,{type:"button",variant:"outline",className:"w-full",children:[e.jsx(U,{className:"h-4 w-4 mr-2"}),"Cancel"]})})]})]})})]})]})]})}export{re as default};
