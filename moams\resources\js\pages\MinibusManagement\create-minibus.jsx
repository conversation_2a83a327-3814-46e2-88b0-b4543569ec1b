import React, { useState, useCallback } from 'react';
import { Head, useForm, Link } from '@inertiajs/react';
import { navigateToLogicalParent } from '@/utils/navigation';
import { LoaderCircle, ArrowLeft } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AppLayout from '@/layouts/app-layout';
import InputError from '@/components/input-error';
import { MinibusOwnerCombobox } from '@/components/ui/minibus-owner-combobox';
import { RouteCombobox } from '@/components/ui/route-combobox';

export default function CreateMinibus({ minibusOwners = [], flash, userRole }) {
    const owners = minibusOwners || [];
    const [selectedOwner, setSelectedOwner] = useState(null);
    const [selectedRoute, setSelectedRoute] = useState(null);
    const { data, setData, post, processing, errors, reset } = useForm({
        number_plate: '',
        make: '',
        model: '',
        year_of_make: '',
        main_colour: '',
        proof_of_ownership: null,
        assigned_route: '',
        owner_id: '',
        route_id: '',
    });

    const [lastProofFile, setLastProofFile] = useState(null);
    const handleFileChange = (e) => {
        const file = e.target.files[0];
        if (
            lastProofFile &&
            file &&
            file.name === lastProofFile.name &&
            file.size === lastProofFile.size &&
            file.lastModified === lastProofFile.lastModified
        ) {
            alert("You have already selected this file. Please choose a different file.");
            e.target.value = "";
            return;
        }
        setLastProofFile(file);
        setData('proof_of_ownership', file);
    };

    const years = Array.from({ length: 45 }, (_, i) => new Date().getFullYear() - i);

    const breadcrumbs = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: userRole === 'minibus owner' ? 'My Minibuses' : 'Minibus Management', href: '/minibuses' },
        { title: 'Add Minibus', href: '/minibuses/create' },
    ];

    const handleChange = (e) => {
        const { id, value } = e.target;
        setData(id, value);
    };

    const fetchOwners = useCallback(async (search) => {
        const res = await fetch(`/api/minibus-owners?search=${encodeURIComponent(search)}`);
        return (await res.json()).filter(owner => owner.id !== null && owner.id !== undefined && owner.id !== "");
    }, []);

    const fetchRoutes = useCallback(async (search) => {
        const res = await fetch(`/api/vehicle-routes?search=${encodeURIComponent(search)}`);
        return await res.json();
    }, []);

    const submit = (e) => {
        e.preventDefault();
        post(route('minibuses.store'), {
            onFinish: () => reset('number_plate', 'assigned_route', 'owner_id'),
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={userRole === 'minibus owner' ? 'Add My Minibus' : 'Add Minibus'} />
            <div className="p-8">
                {/* Header */}
                <div className='flex justify-between items-center mb-8'>
                    <div className="flex items-center space-x-4">
                        <Button variant="outline" onClick={() => navigateToLogicalParent(breadcrumbs)} className="w-full sm:w-auto">
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Back
                        </Button>
                        <div>
                            <p className="text-gray-600 dark:text-gray-400 mt-1">
                                Register a new minibus for the minibus owner
                            </p>
                        </div>
                    </div>
                </div>
                <div className="max-w-2xl">
                    <form className="space-y-6" onSubmit={submit}>
                        <div>
                            <Label htmlFor="number_plate">Number Plate</Label>
                            <Input
                                id="number_plate"
                                type="text"
                                autoFocus
                                autoComplete="number_plate"
                                value={data.number_plate}
                                onChange={handleChange}
                                disabled={processing}
                                placeholder="e.g. DZ 4536"
                            />
                            <InputError message={errors.number_plate} className="mt-1" />
                        </div>
                        <div>
                            <Label htmlFor="make">Make</Label>
                            <Input
                                id="make"
                                type="text"
                                value={data.make}
                                onChange={handleChange}
                                disabled={processing}
                                placeholder="e.g. Toyota"
                            />
                            <InputError message={errors.make} className="mt-1" />
                        </div>
                        <div>
                            <Label htmlFor="model">Model</Label>
                            <Input
                                id="model"
                                type="text"
                                value={data.model}
                                onChange={handleChange}
                                disabled={processing}
                                placeholder="e.g. Hiace"
                            />
                            <InputError message={errors.model} className="mt-1" />
                        </div>
                        <div>
                            <Label htmlFor="year_of_make">Year of Make</Label>
                            <select
                                id="year_of_make"
                                value={data.year_of_make}
                                onChange={handleChange}
                                disabled={processing}
                                className="w-full border rounded px-3 py-2"
                            >
                                <option value="">Select year</option>
                                {years.map((year) => (
                                    <option key={year} value={year}>{year}</option>
                                ))}
                            </select>
                            <InputError message={errors.year_of_make} className="mt-1" />
                        </div>
                        <div>
                            <Label htmlFor="main_colour">Main Colour</Label>
                            <Input
                                id="main_colour"
                                type="text"
                                value={data.main_colour}
                                onChange={handleChange}
                                disabled={processing}
                                placeholder="e.g. White/Blue"
                            />
                            <InputError message={errors.main_colour} className="mt-1" />
                        </div>
                        <div>
                            <Label htmlFor="proof_of_ownership">Proof of Ownership (PDF/Image)</Label>
                            <Input
                                id="proof_of_ownership"
                                type="file"
                                accept=".pdf,.jpg,.jpeg,.png"
                                onChange={handleFileChange}
                                disabled={processing}
                            />
                            <InputError message={errors.proof_of_ownership} className="mt-1" />
                        </div>
                        <div>
                            <Label htmlFor="route_id">Assigned Route</Label>
                            <RouteCombobox
                                value={selectedRoute}
                                onChange={route => {
                                    setSelectedRoute(route);
                                    setData('route_id', route?.id || '');
                                }}
                                fetchRoutes={fetchRoutes}
                                placeholder="Select route..."
                            />
                            <InputError message={errors.route_id} className="mt-1" />
                        </div>
                        {userRole === 'association clerk' && (
                            <div>
                                <Label htmlFor="owner_id">Owner *</Label>
                                <MinibusOwnerCombobox
                                    value={selectedOwner || null}
                                    onChange={owner => {
                                        setSelectedOwner(owner);
                                        setData('owner_id', owner?.id || '');
                                    }}
                                    fetchOwners={fetchOwners}
                                    placeholder="Select minibus owner..."
                                />
                                {errors.owner_id && (
                                    <p className="text-sm text-red-500">{errors.owner_id}</p>
                                )}
                            </div>
                        )}

                        <div className="flex space-x-4 pt-6">
                            <Button
                                type="submit"
                                className="bg-blue-600 hover:bg-blue-700"
                                disabled={processing}
                            >
                                {processing && <LoaderCircle className="h-4 w-4 animate-spin mr-2" />}
                                Add minibus
                            </Button>
                            <Link href="/minibuses">
                                <Button type="button" variant="outline">
                                    Cancel
                                </Button>
                            </Link>
                        </div>
                    </form>
                </div>
            </div>
        </AppLayout>
    );
}