<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreMisconductRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && auth()->user()->hasR<PERSON>('minibus owner');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'offense_date' => 'required|date|before_or_equal:today',
            'severity' => 'required|in:low,medium,high',
            'minibus_id' => 'nullable|exists:minibuses,id',
            'evidence_file' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:5120', // 5MB max
            'offender_type' => 'required|in:driver',
            'offender_id' => 'required|integer',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'name.required' => 'The misconduct name is required.',
            'name.max' => 'The misconduct name cannot exceed 255 characters.',
            'offense_date.required' => 'The offense date is required.',
            'offense_date.before_or_equal' => 'The offense date cannot be in the future.',
            'severity.required' => 'Please select the severity level.',
            'severity.in' => 'Invalid severity level selected.',
            'minibus_id.exists' => 'Selected minibus does not exist.',
            'evidence_file.file' => 'Evidence must be a valid file.',
            'evidence_file.mimes' => 'Evidence file must be a PDF, JPG, JPEG, or PNG.',
            'evidence_file.max' => 'Evidence file cannot exceed 5MB.',
            'offender_type.required' => 'Please select the driver who committed the misconduct.',
            'offender_type.in' => 'Only drivers can be selected as offenders.',
            'offender_id.required' => 'Please select a driver.',
            'offender_id.integer' => 'Invalid driver selected.',
        ];
    }
}
