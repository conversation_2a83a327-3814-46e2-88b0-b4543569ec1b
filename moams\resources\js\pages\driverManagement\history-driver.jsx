import { Head, <PERSON> } from '@inertiajs/react';
import { navigateToLogicalParent } from '@/utils/navigation';
import { ArrowLeft, Calendar, User, Building, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import AppLayout from '@/layouts/app-layout';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';

export default function DriverHistory({ driver, history, userRole }) {
    const breadcrumbs = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: userRole === 'minibus owner' ? 'My Drivers' : 'Driver Management', href: '/drivers' },
        { title: `${driver.first_name} ${driver.last_name}`, href: `/drivers/${driver.id}` },
        { title: 'Employment History', href: '#' },
    ];

    const getEmploymentTypeIcon = (type) => {
        switch (type) {
            case 'hired':
                return <User className="h-4 w-4 text-green-500" />;
            case 'cleared':
                return <Building className="h-4 w-4 text-red-500" />;
            case 'rehired':
                return <User className="h-4 w-4 text-blue-500" />;
            default:
                return <Clock className="h-4 w-4 text-gray-500" />;
        }
    };

    const getEmploymentTypeBadge = (type) => {
        switch (type) {
            case 'hired':
                return <Badge variant="default" className="bg-green-600">Hired</Badge>;
            case 'cleared':
                return <Badge variant="destructive">Cleared</Badge>;
            case 'rehired':
                return <Badge variant="default" className="bg-blue-600">Rehired</Badge>;
            default:
                return <Badge variant="outline">Unknown</Badge>;
        }
    };

    const formatDate = (dateString) => {
        if (!dateString) return 'N/A';
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Employment History - ${driver.first_name} ${driver.last_name}`} />
            <div className="container mx-auto px-4 py-8">
                <div className="max-w-4xl mx-auto">
                    <div className="space-y-8">
                        <div className="flex flex-col sm:flex-row gap-4 items-center sm:justify-between mb-8">
                            <Button
                                variant="outline"
                                onClick={() => navigateToLogicalParent(breadcrumbs)}
                                className="w-full sm:w-auto"
                            >
                                <span className="block sm:hidden">
                                    <Tooltip>
                                        <TooltipTrigger asChild>
                                            <span><ArrowLeft className="h-4 w-4" /></span>
                                        </TooltipTrigger>
                                        <TooltipContent>Back</TooltipContent>
                                    </Tooltip>
                                </span>
                                <span className="hidden sm:flex items-center">
                                    <ArrowLeft className="mr-2 h-4 w-4" />
                                    Back
                                </span>
                            </Button>
                            <h1 className="text-2xl font-bold text-center sm:text-left">Driver Employment History</h1>
                            <span className="text-muted-foreground text-base font-medium">
                                View the full employment history for this driver.
                            </span>
                        </div>

                        <Link href={route('drivers.show', driver.id)}>
                            <Button variant="outline" size="sm" className="mb-8">
                                View Driver Details
                            </Button>
                        </Link>

                        {/* Driver Summary */}
                        <Card className="mb-8">
                            <CardHeader>
                                <CardTitle>Driver Information</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">Driver Name</label>
                                        <p className="text-lg font-medium">{driver.first_name} {driver.last_name}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">Phone Number</label>
                                        <p className="text-lg">{driver.phone_number}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">Current Status</label>
                                        <p className="text-lg">
                                            {driver.archived ? (
                                                <Badge variant="destructive">Archived</Badge>
                                            ) : (
                                                <Badge variant="default">Active</Badge>
                                            )}
                                        </p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Employment History Timeline */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Calendar className="h-5 w-5" />
                                    Employment Timeline
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                {history.length === 0 ? (
                                    <div className="text-center py-8">
                                        <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                                        <div className="text-lg font-medium text-gray-700 mb-1">No employment history found.</div>
                                        <div className="text-gray-500 text-sm">Employment records will appear here as the driver's employment changes.</div>
                                    </div>
                                ) : (
                                    <div className="space-y-6">
                                        {history.map((record, index) => (
                                            <div key={record.id} className="relative">
                                                {/* Timeline connector */}
                                                {index < history.length - 1 && (
                                                    <div className="absolute left-6 top-12 w-0.5 h-16 bg-gray-200"></div>
                                                )}

                                                <div className="flex items-start gap-4">
                                                    {/* Timeline dot */}
                                                    <div className="flex-shrink-0 w-12 h-12 rounded-full bg-gray-100 border-2 border-gray-200 flex items-center justify-center">
                                                        {getEmploymentTypeIcon(record.employment_change_type)}
                                                    </div>

                                                    {/* Content */}
                                                    <div className="flex-1 min-w-0">
                                                        <Card>
                                                            <CardHeader className="pb-3">
                                                                <CardTitle className="flex items-center justify-between text-base">
                                                                    <span className="flex items-center gap-2">
                                                                        {getEmploymentTypeBadge(record.employment_change_type)}
                                                                        <span>
                                                                            {record.employment_change_type === 'hired' && 'Driver Hired'}
                                                                            {record.employment_change_type === 'cleared' && 'Driver Cleared'}
                                                                            {record.employment_change_type === 'rehired' && 'Driver Rehired'}
                                                                        </span>
                                                                    </span>
                                                                    <span className="text-sm text-gray-500">
                                                                        {formatDate(record.created_at)}
                                                                    </span>
                                                                </CardTitle>
                                                            </CardHeader>
                                                            <CardContent className="pt-0">
                                                                <div className="space-y-3">
                                                                    {/* Previous Owner */}
                                                                    {record.previous_owner && (
                                                                        <div>
                                                                            <label className="text-sm font-medium text-gray-500">Previous Owner</label>
                                                                            <p className="text-sm">
                                                                                {record.previous_owner.first_name} {record.previous_owner.last_name}
                                                                                <span className="text-gray-500 ml-2">({record.previous_owner.phone_number})</span>
                                                                            </p>
                                                                        </div>
                                                                    )}

                                                                    {/* New Owner */}
                                                                    {record.new_owner && (
                                                                        <div>
                                                                            <label className="text-sm font-medium text-gray-500">New Owner</label>
                                                                            <p className="text-sm">
                                                                                {record.new_owner.first_name} {record.new_owner.last_name}
                                                                                <span className="text-gray-500 ml-2">({record.new_owner.phone_number})</span>
                                                                            </p>
                                                                        </div>
                                                                    )}

                                                                    {/* Employment Dates */}
                                                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                                        {record.employment_start_date && (
                                                                            <div>
                                                                                <label className="text-sm font-medium text-gray-500">Employment Start</label>
                                                                                <p className="text-sm">{formatDate(record.employment_start_date)}</p>
                                                                            </div>
                                                                        )}
                                                                        {record.employment_end_date && (
                                                                            <div>
                                                                                <label className="text-sm font-medium text-gray-500">Employment End</label>
                                                                                <p className="text-sm">{formatDate(record.employment_end_date)}</p>
                                                                            </div>
                                                                        )}
                                                                    </div>

                                                                    {/* Reason */}
                                                                    {record.reason && (
                                                                        <div>
                                                                            <label className="text-sm font-medium text-gray-500">Reason</label>
                                                                            <p className="text-sm p-2 bg-gray-50 rounded">{record.reason}</p>
                                                                        </div>
                                                                    )}

                                                                    {/* Status */}
                                                                    <div>
                                                                        <label className="text-sm font-medium text-gray-500">Status</label>
                                                                        <p className="text-sm">
                                                                            {record.status === 'completed' ? (
                                                                                <Badge variant="default" className="bg-green-600 text-xs">Completed</Badge>
                                                                            ) : record.status === 'pending' ? (
                                                                                <Badge variant="outline" className="text-amber-600 border-amber-600 text-xs">Pending</Badge>
                                                                            ) : (
                                                                                <Badge variant="outline" className="text-xs">{record.status}</Badge>
                                                                            )}
                                                                        </p>
                                                                    </div>
                                                                </div>
                                                            </CardContent>
                                                        </Card>
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        {/* Summary Statistics */}
                        {history.length > 0 && (
                            <Card className="mt-6">
                                <CardHeader>
                                    <CardTitle>Employment Summary</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                                        <div className="text-center">
                                            <div className="text-2xl font-bold text-blue-600">
                                                {history.length}
                                            </div>
                                            <div className="text-sm text-gray-600">Total Records</div>
                                        </div>
                                        <div className="text-center">
                                            <div className="text-2xl font-bold text-green-600">
                                                {history.filter(h => h.employment_change_type === 'hired').length}
                                            </div>
                                            <div className="text-sm text-gray-600">Times Hired</div>
                                        </div>
                                        <div className="text-center">
                                            <div className="text-2xl font-bold text-red-600">
                                                {history.filter(h => h.employment_change_type === 'cleared').length}
                                            </div>
                                            <div className="text-sm text-gray-600">Times Cleared</div>
                                        </div>
                                        <div className="text-center">
                                            <div className="text-2xl font-bold text-blue-600">
                                                {history.filter(h => h.employment_change_type === 'rehired').length}
                                            </div>
                                            <div className="text-sm text-gray-600">Times Rehired</div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        )}
                    </div>
                </div>
            </div>
        </AppLayout>
    );
} 