<?php

namespace App\Http\Controllers;

use App\Models\Minibus;
use App\Models\MinibusOwnershipHistory;
use App\Models\User;
use App\Notifications\TransferRequestNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification;
use Inertia\Inertia;
use App\Models\MinibusTransferRequest;
use App\Notifications\TransferSuccessNotification;

class MinibusOwnershipController extends Controller
{
    public function requestTransfer(Minibus $minibus)
    {
        // Ensure the current user is the owner of the minibus
        if ($minibus->owner_id !== auth()->id()) {
            abort(403, 'You are not authorized to request transfer for this minibus.');
        }

        $minibus->load('minibusOwner');
        $owner = $minibus->minibusOwner;
        return Inertia::render('MinibusManagement/request-transfer-minibus', [
            'minibus' => $minibus,
            'ownerName' => $owner ? ($owner->first_name . ' ' . $owner->last_name) : '',
            'ownerPhone' => $owner ? $owner->phone_number : '',
        ]);
    }

    public function storeTransferRequest(Request $request, Minibus $minibus)
    {
        // Ensure the current user is the owner of the minibus
        if ($minibus->owner_id !== auth()->id()) {
            abort(403, 'You are not authorized to request transfer for this minibus.');
        }

        // Prevent duplicate pending transfer requests
        $existing = MinibusTransferRequest::where('minibus_id', $minibus->id)
            ->where('owner_id', $minibus->owner_id)
            ->where('status', 'pending')
            ->first();
        if ($existing) {
            return redirect()->back()->with('error', 'You already have a pending transfer request for this minibus.');
        }

        $request->validate([
            'new_owner_type' => 'required|in:member,non_member',
            'reason' => 'required|string|min:10',
            'ownership_transfer_certificate' => 'required|file|mimes:pdf,jpg,jpeg,png',
        ]);

        try {
            DB::transaction(function () use ($request, $minibus) {
                $data = [
                    'minibus_id' => $minibus->id,
                    'owner_id' => $minibus->owner_id,
                    'transfer_type' => $request->new_owner_type === 'member' ? 'internal' : 'external',
                    'status' => 'pending',
                    'reason' => $request->reason,
                ];
                // Handle ownership_transfer_certificate file upload
                if ($request->hasFile('ownership_transfer_certificate')) {
                    $file = $request->file('ownership_transfer_certificate');
                    $path = $file->store('ownership_transfer_certificates', 'public');
                    $data['ownership_transfer_certificate'] = $path;
                }
                // Create transfer request record in the correct table
                $transferRequest = MinibusTransferRequest::create($data);

                // Notify all association clerks
                $clerks = User::role('association clerk')->get();
                Notification::send($clerks, new TransferRequestNotification($transferRequest));
            });

            return redirect()->route('minibuses.show', $minibus->id)
                ->with('success', 'Transfer request submitted successfully. An association clerk will review your request.');
        } catch (\Exception $e) {
            \Log::error('Transfer request failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return redirect()->back()
                ->with('error', 'Failed to submit transfer request. Please try again.');
        }
    }

    public function listTransferRequests()
    {
        $transferRequests = MinibusTransferRequest::with(['minibus', 'owner'])
            ->where('status', 'pending')
            ->orderBy('created_at', 'desc')
            ->get();

        return Inertia::render('MinibusManagement/transfer-requests', [
            'transferRequests' => $transferRequests
        ]);
    }

    public function transfer(Minibus $minibus)
    {
        $minibus->load('minibusOwner');
        // Ensure the data structure matches what the frontend expects
        $minibusArr = $minibus->toArray();
        $minibusArr['owner'] = [
            'user' => $minibus->minibusOwner ? [
                'id' => $minibus->minibusOwner->id,
                'first_name' => $minibus->minibusOwner->first_name,
                'last_name' => $minibus->minibusOwner->last_name,
                'email' => $minibus->minibusOwner->email,
            ] : null
        ];

        return Inertia::render('MinibusManagement/transfer-minibus', [
            'minibus' => $minibusArr,
            'users' => User::role('minibus owner')->get(),
        ]);
    }

    public function history(Minibus $minibus)
    {
        $history = MinibusOwnershipHistory::where('minibus_id', $minibus->id)
            ->where('status', 'completed')
            ->with(['previous_owner', 'new_owner'])
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($entry) {
                return [
                    'id' => $entry->id,
                    'previous_owner' => $entry->previous_owner,
                    'new_owner' => $entry->new_owner,
                    'created_at' => $entry->created_at,
                    'transfer_type' => $entry->transfer_type,
                    'status' => $entry->status,
                    'reason' => $entry->reason,
                ];
            });

        return Inertia::render('MinibusManagement/history-minibus', [
            'minibus' => $minibus,
            'history' => $history,
            'userRole' => auth()->user()->roles->first()->name ?? null,
        ]);
    }

    public function processTransfer(Request $request, Minibus $minibus)
    {
        \Log::info('Transfer request received', [
            'minibus_id' => $minibus->id,
            'transfer_type' => $request->transfer_type,
            'new_owner_id' => $request->new_owner_id,
        ]);

        $request->validate([
            'transfer_type' => 'required|in:internal,external',
            'new_owner_id' => [
                'nullable',
                'required_if:transfer_type,internal',
                'prohibited_if:transfer_type,external',
                function ($attribute, $value, $fail) use ($minibus) {
                    if ($value && $value == $minibus->owner_id) {
                        $fail('New owner cannot be the same as current owner.');
                    }
                },
            ],
        ]);

        try {
            DB::transaction(function () use ($request, $minibus) {
                \Log::info('Creating ownership history record');
                // Create history record
                MinibusOwnershipHistory::create([
                    'minibus_id' => $minibus->id,
                    'previous_owner_id' => $minibus->owner_id,
                    'new_owner_id' => $request->transfer_type === 'internal' ? $request->new_owner_id : null,
                    'transfer_type' => $request->transfer_type,
                    'status' => 'completed',
                ]);

                // Update the transfer request status to 'transferred'
                $transferRequest = MinibusTransferRequest::where('minibus_id', $minibus->id)
                    ->where('status', 'pending')
                    ->latest('created_at')
                    ->first();
                if ($transferRequest) {
                    $transferRequest->status = 'transferred';
                    $transferRequest->save();

                    // Notify the original owner of successful transfer
                    $originalOwner = User::find($minibus->owner_id);
                    if ($originalOwner && !$originalOwner->archived_at) {
                        try {
                            $originalOwner->notify(new TransferSuccessNotification($transferRequest));
                            session()->flash('success', 'Notification sent to original owner successfully.');
                            \Log::info('TransferSuccessNotification sent successfully to owner', [
                                'owner_id' => $originalOwner->id,
                                'owner_email' => $originalOwner->email,
                            ]);
                        } catch (\Exception $e) {
                            \Log::error('Failed to send TransferSuccessNotification', [
                                'error' => $e->getMessage(),
                                'owner_id' => $originalOwner->id,
                            ]);
                            session()->flash('error', 'Failed to send notification to original owner.');
                        }
                    } else {
                        \Log::warning('Original owner not found or archived for transfer notification', [
                            'minibus_id' => $minibus->id,
                            'owner_id' => $minibus->owner_id,
                        ]);
                        session()->flash('error', 'Original owner not found or archived. Notification not sent.');
                    }
                }

                // Update minibus owner if internal transfer
                if ($request->transfer_type === 'internal' && $request->new_owner_id) {
                    $minibus->owner_id = $request->new_owner_id;
                    $minibus->archived = false;
                    $minibus->save();
                } else if ($request->transfer_type === 'external') {
                    $minibus->archived = true;
                    $minibus->save();
                }
            });

            return redirect()->route('minibuses.show', $minibus->id)
                ->with('success', 'Minibus ownership transferred successfully.');
        } catch (\Exception $e) {
            \Log::error('Transfer processing failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return redirect()->back()
                ->with('error', 'Failed to process transfer. Please try again.');
        }
    }

    public function historyPage(Minibus $minibus)
    {
        $history = MinibusOwnershipHistory::where('minibus_id', $minibus->id)
            ->with(['previous_owner', 'new_owner'])
            ->orderBy('created_at', 'desc')
            ->get();

        return Inertia::render('MinibusManagement/history-minibus', [
            'minibus' => $minibus->load('user'),
            'history' => $history,
        ]);
    }

    public function indexTransferRequests(Request $request)
    {
        $query = MinibusTransferRequest::with(['minibus', 'owner']);

        // Apply search filter
        if ($request->has('search') && $request->get('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->whereHas('minibus', function ($minibusQuery) use ($search) {
                    $minibusQuery->where('number_plate', 'like', "%{$search}%");
                })
                    ->orWhereHas('owner', function ($ownerQuery) use ($search) {
                        $ownerQuery->where('first_name', 'like', "%{$search}%")
                            ->orWhere('last_name', 'like', "%{$search}%")
                            ->orWhere('email', 'like', "%{$search}%");
                    })
                    ->orWhere('reason', 'like', "%{$search}%")
                    ->orWhere('transfer_type', 'like', "%{$search}%");
            });
        }

        // Apply status filter
        if ($request->has('status') && $request->get('status') !== 'all') {
            $status = $request->get('status');
            $query->where('status', $status);
        }

        // Always sort pending requests first, then by created date
        $query->orderByRaw("CASE WHEN status = 'pending' THEN 0 ELSE 1 END")
            ->orderBy('created_at', 'desc');

        // Paginate results
        $transferRequests = $query->paginate(10)->withQueryString();

        $breadcrumbs = [
            ['title' => 'Dashboard', 'href' => '/dashboard'],
            ['title' => 'Minibus Management', 'href' => '/minibuses'],
            ['title' => 'Transfer Requests', 'href' => '/minibuses/transfer-requests'],
        ];

        return Inertia::render('MinibusManagement/transfer-requests', [
            'transferRequests' => $transferRequests,
            'breadcrumbs' => $breadcrumbs,
            'filters' => [
                'search' => $request->get('search'),
                'status' => $request->get('status', 'all'),
            ],
        ]);
    }

    public function showTransferRequest($id)
    {
        $transferRequest = MinibusTransferRequest::with(['minibus', 'owner'])->findOrFail($id);
        $breadcrumbs = [
            ['title' => 'Dashboard', 'href' => '/dashboard'],
            ['title' => 'Minibus Management', 'href' => '/minibuses'],
            ['title' => 'Transfer Requests', 'href' => '/minibuses/transfer-requests'],
            ['title' => 'Request #' . $transferRequest->id, 'href' => "/minibuses/transfer-requests/{$transferRequest->id}"],
        ];
        return Inertia::render('MinibusManagement/show-transfer-request', [
            'transferRequest' => $transferRequest,
            'breadcrumbs' => $breadcrumbs,
        ]);
    }

    public function destroyTransferRequest($id)
    {
        $transferRequest = MinibusTransferRequest::findOrFail($id);
        $transferRequest->delete();
        return redirect()->route('minibuses.transfer-requests.index')->with('success', 'Transfer request deleted successfully.');
    }

    /**
     * Process transfer request (approve/reject)
     */
    public function processTransferRequest(Request $request, MinibusTransferRequest $transferRequest)
    {
        $user = auth()->user();
        $userRole = $user->roles->first()->name ?? null;

        if (!in_array($userRole, ['association clerk', 'association manager'])) {
            abort(403, 'Only association clerks and managers can process transfer requests.');
        }

        $request->validate([
            'action' => 'required|in:approve,reject',
            'admin_notes' => 'nullable|string',
        ]);

        try {
            DB::transaction(function () use ($request, $transferRequest, $user) {
                $transferRequest->update([
                    'status' => $request->action === 'approve' ? 'approved' : 'rejected',
                    'processed_by' => $user->id,
                    'processed_at' => now(),
                ]);
            });

            // Notify the original owner
            try {
                $transferRequest->owner->notify(new TransferSuccessNotification($transferRequest));
            } catch (\Exception $e) {
                \Log::error('Failed to send transfer notification', [
                    'error' => $e->getMessage(),
                    'transfer_request_id' => $transferRequest->id,
                ]);
                // Don't fail the whole operation for notification failure
            }

            $message = $request->action === 'approve'
                ? 'Transfer request approved successfully.'
                : 'Transfer request rejected.';

            return redirect()->route('minibuses.transfer-requests.index')
                ->with('success', $message);
        } catch (\Exception $e) {
            \Log::error('Transfer request processing failed', [
                'error' => $e->getMessage(),
                'transfer_request_id' => $transferRequest->id,
            ]);
            return redirect()->back()
                ->with('error', 'Failed to process transfer request. Please try again.');
        }
    }
}