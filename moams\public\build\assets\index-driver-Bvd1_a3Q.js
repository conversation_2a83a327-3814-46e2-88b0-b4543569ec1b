import{i as W,r as p,j as e,H as G,L as l,T as n,B as i,C as D,a as T,b as A,c as M,e as c}from"./app-DL-qYY5V.js";import{n as J}from"./navigation-DAA2N51J.js";import{I as K}from"./input-Dm4SEXxy.js";import{L as P}from"./label-e3QxUH-L.js";import{A as Q,B as g,U as F,T as X,b as d,c as m,d as o,C as Y}from"./app-layout-YqstQnqE.js";import{S as Z,a as R,b as ee,c as se,d as v}from"./select-Cp8NjZe8.js";import{C as ae}from"./confirm-dialog-B1e93Onq.js";import{C as L}from"./chevron-left-DFeVEtK7.js";import{P as re}from"./plus-CU6rIcI2.js";import{S as z}from"./search-B4sum6Qx.js";import{E as te}from"./eye-sp8vtjJC.js";import{H as ie}from"./history-Boy80jYJ.js";import{S as ne}from"./square-pen-BT4HeE62.js";import{F as le}from"./file-text-BcxQqvSb.js";import"./index-BTzg1GwG.js";import"./index-1tPPImJd.js";import"./app-logo-icon-BnXlkpcX.js";import"./bus-CKTBKI0T.js";import"./dollar-sign-Cx0-nQIX.js";import"./users-DNGXY-sJ.js";import"./clipboard-list-CxRumT8v.js";import"./index-uC6ZAdKJ.js";import"./dialog-CXtul0Wy.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ce=[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h2",key:"tvwodi"}],["path",{d:"M20 8v11a2 2 0 0 1-2 2h-2",key:"1gkqxj"}],["path",{d:"m9 15 3-3 3 3",key:"1pd0qc"}],["path",{d:"M12 12v9",key:"192myk"}]],de=W("ArchiveRestore",ce);function Fe({drivers:a,clearanceRequests:h,userRole:t,auth:B}){var b,y,C,S,_;const[u,E]=p.useState(""),[j,H]=p.useState("all"),[r,f]=p.useState({open:!1,action:null,driver:null}),[$,N]=p.useState(!1),w=[{title:"Dashboard",href:"/dashboard"},{title:t==="minibus owner"?"My Drivers":"Driver Management",href:"/drivers"}],U=s=>{E(s),c.get("/drivers",{search:s,status:j},{preserveState:!0,preserveScroll:!0})},I=s=>{H(s),c.get("/drivers",{search:u,status:s},{preserveState:!0,preserveScroll:!0})},V=(s,x)=>{f({open:!0,action:s,driver:x})},O=()=>f({open:!1,action:null,driver:null}),q=async()=>{N(!0),r.action==="delete"?await c.delete(n("drivers.destroy",r.driver.id)):r.action==="unarchive"&&await c.patch(n("drivers.unarchive",r.driver.id)),N(!1),f({open:!1,action:null,driver:null})};return e.jsxs(Q,{breadcrumbs:w,children:[e.jsx(G,{title:t==="minibus owner"?"My Drivers":"Driver Management"}),e.jsx("div",{className:"w-full max-w-full overflow-hidden",children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex flex-col gap-4 mb-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4",children:[e.jsxs("button",{className:"flex items-center px-3 py-1 rounded hover:bg-gray-100 border border-gray-200 w-fit",onClick:()=>J(w),children:[e.jsx(L,{className:"h-4 w-4 mr-1"})," Back"]}),e.jsx("div",{className:"flex-1 sm:flex-none",children:e.jsx("span",{className:"text-muted-foreground text-sm sm:text-base font-medium break-words",children:t==="minibus owner"?"Manage your registered drivers and view their information.":"Browse all drivers and manage driver registrations."})})]}),e.jsxs("div",{className:"flex flex-wrap items-center gap-2 justify-start sm:justify-end",children:[t==="association clerk"&&e.jsx(l,{href:n("drivers.clearance-requests.index"),children:e.jsxs(i,{variant:"outline",className:"w-fit flex items-center gap-2",children:[e.jsx("span",{className:"hidden sm:inline",children:"Pending Clearance Requests"}),e.jsx("span",{className:"sm:hidden",children:"Pending"}),(h==null?void 0:h.filter(s=>s.status==="pending").length)>0&&e.jsx(g,{variant:"secondary",className:"ml-2 bg-red-100 text-red-800 font-bold",children:h.filter(s=>s.status==="pending").length})]})}),(t==="association clerk"||t==="minibus owner")&&e.jsx(l,{href:n("drivers.create"),children:e.jsxs(i,{className:"w-fit flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2",children:[e.jsx(re,{className:"h-4 w-4"}),"Add New Driver"]})})]})]}),e.jsxs(D,{className:"mb-6 w-full",children:[e.jsx(T,{children:e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(z,{className:"h-5 w-5"}),"Search & Filter"]})}),e.jsx(M,{children:e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx(P,{htmlFor:"search",children:"Search Drivers"}),e.jsxs("div",{className:"relative",children:[e.jsx(z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),e.jsx(K,{id:"search",placeholder:"Search by name, phone, district...",value:u,onChange:s=>U(s.target.value),className:"pl-10"})]})]}),t==="association clerk"&&e.jsxs("div",{children:[e.jsx(P,{htmlFor:"status",children:"Filter by Status"}),e.jsxs(Z,{value:j,onValueChange:I,children:[e.jsx(R,{children:e.jsx(ee,{placeholder:"All statuses"})}),e.jsxs(se,{children:[e.jsx(v,{value:"all",children:"All Drivers"}),e.jsx(v,{value:"active",children:"Active Only"}),e.jsx(v,{value:"archived",children:"Archived Only"})]})]})]})]})})]}),e.jsx("div",{className:"w-full",style:{maxWidth:"calc(100vw - 2rem)",overflow:"hidden"},children:e.jsxs(D,{className:"w-full border-0 shadow-none",children:[e.jsx(T,{className:"px-0",children:e.jsxs(A,{className:"flex items-center gap-2",children:[e.jsx(F,{className:"h-5 w-5"}),t==="minibus owner"?"My Drivers":"Drivers"," (",a.total||((b=a.data)==null?void 0:b.length)||0,")"]})}),e.jsx(M,{className:"p-0",children:a.data&&a.data.length>0?e.jsx("div",{className:"overflow-x-auto mb-8",children:e.jsx(X,{children:e.jsxs("table",{className:"min-w-full w-full bg-white border border-gray-200 rounded-lg",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"bg-gray-100 text-gray-700",children:[e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Name"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Phone"}),t!=="minibus owner"&&e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Owner"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Status"}),e.jsx("th",{className:"px-4 py-3 text-left font-medium",children:"Actions"})]})}),e.jsx("tbody",{children:a.data.map(s=>{var x,k;return e.jsxs("tr",{className:"border-b hover:bg-gray-50 text-sm",children:[e.jsx("td",{className:"font-medium px-4 py-3",children:e.jsxs("div",{className:"truncate max-w-[120px] sm:max-w-none",children:[s.first_name," ",s.last_name]})}),e.jsx("td",{className:"px-4 py-3",children:e.jsx("div",{className:"truncate max-w-[100px] sm:max-w-none",children:s.phone_number})}),t!=="minibus owner"&&e.jsx("td",{className:"px-4 py-3",children:e.jsx("div",{className:"truncate max-w-[100px] sm:max-w-none",children:s.minibus_owner?`${(x=s.minibus_owner.user)==null?void 0:x.first_name} ${(k=s.minibus_owner.user)==null?void 0:k.last_name}`:"N/A"})}),e.jsx("td",{className:"px-4 py-3",children:s.archived?e.jsx(g,{className:"bg-red-100 text-red-800 border border-red-600 text-sm",children:"Archived"}):e.jsx(g,{className:"bg-green-100 text-green-800 border border-green-600 text-sm",children:"Active"})}),e.jsx("td",{className:"px-4 py-3",children:e.jsxs("div",{className:"flex items-center gap-1 sm:gap-2",children:[e.jsxs(d,{children:[e.jsx(m,{asChild:!0,children:e.jsx(l,{href:n("drivers.show",s.id),children:e.jsx(i,{size:"sm",variant:"outline",className:"h-6 w-6 sm:h-8 sm:w-8 p-0",children:e.jsx(te,{className:"h-3 w-3 sm:h-4 sm:w-4"})})})}),e.jsx(o,{children:"View Details"})]}),t==="association clerk"&&e.jsxs(d,{children:[e.jsx(m,{asChild:!0,children:e.jsx(l,{href:n("drivers.history",s.id),children:e.jsx(i,{variant:"outline",size:"sm",className:"h-6 w-6 sm:h-8 sm:w-8 p-0",children:e.jsx(ie,{className:"h-3 w-3 sm:h-4 sm:w-4"})})})}),e.jsx(o,{children:"Employment History"})]}),t==="association clerk"&&!s.archived&&e.jsxs(d,{children:[e.jsx(m,{asChild:!0,children:e.jsx(l,{href:n("drivers.edit",s.id),children:e.jsx(i,{variant:"outline",size:"sm",className:"h-6 w-6 sm:h-8 sm:w-8 p-0",children:e.jsx(ne,{className:"h-3 w-3 sm:h-4 sm:w-4"})})})}),e.jsx(o,{children:"Edit Driver"})]}),t==="minibus owner"&&!s.archived&&s.user_id===B.user.id&&e.jsxs(d,{children:[e.jsx(m,{asChild:!0,children:e.jsx(l,{href:n("drivers.request-clearance",s.id),children:e.jsx(i,{size:"sm",variant:"outline",className:"h-6 w-6 sm:h-8 sm:w-8 p-0",children:e.jsx(le,{className:"h-3 w-3 sm:h-4 sm:w-4"})})})}),e.jsx(o,{children:"Request Clearance"})]}),s.archived&&t==="association clerk"&&e.jsxs(d,{children:[e.jsx(m,{asChild:!0,children:e.jsx(i,{variant:"outline",size:"sm",className:"h-6 w-6 sm:h-8 sm:w-8 p-0",onClick:()=>V("unarchive",s),children:e.jsx(de,{className:"h-3 w-3 sm:h-4 sm:w-4"})})}),e.jsx(o,{children:"Unarchive Driver"})]})]})})]},s.id)})})]})})}):e.jsxs("div",{className:"text-center py-8 px-6",children:[e.jsx(F,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),e.jsx("h3",{className:"text-lg font-medium mb-2",children:"No drivers found"}),e.jsx("p",{className:"text-muted-foreground mb-4",children:u||j!=="all"?"Try adjusting your search or filters":t==="minibus owner"?"No drivers have been registered for your minibuses yet":"No drivers have been registered yet"})]})})]})}),a&&a.total>0&&e.jsx("div",{className:"mt-6 w-full",children:e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 px-4 py-4 border-t bg-gray-50/50 rounded-b-lg",children:[e.jsxs("div",{className:"text-sm text-gray-700 text-center sm:text-left",children:["Showing ",a.from," to ",a.to," of ",a.total," results"]}),a.last_page>1&&e.jsxs("div",{className:"flex items-center justify-center sm:justify-end space-x-2",children:[e.jsxs(i,{variant:"outline",size:"sm",onClick:()=>c.get(a.prev_page_url),disabled:!a.prev_page_url,className:"flex items-center gap-1",children:[e.jsx(L,{className:"h-4 w-4"}),e.jsx("span",{className:"hidden sm:inline",children:"Previous"}),e.jsx("span",{className:"sm:hidden",children:"Prev"})]}),e.jsxs("span",{className:"text-sm text-gray-600 px-2",children:[a.current_page," of ",a.last_page]}),e.jsxs(i,{variant:"outline",size:"sm",onClick:()=>c.get(a.next_page_url),disabled:!a.next_page_url,className:"flex items-center gap-1",children:[e.jsx("span",{className:"hidden sm:inline",children:"Next"}),e.jsx("span",{className:"sm:hidden",children:"Next"}),e.jsx(Y,{className:"h-4 w-4"})]})]})]})})]})}),e.jsx(ae,{open:r.open,title:r.action==="delete"?`Delete ${(y=r.driver)==null?void 0:y.first_name} ${(C=r.driver)==null?void 0:C.last_name}?`:r.action==="unarchive"?`Unarchive ${(S=r.driver)==null?void 0:S.first_name} ${(_=r.driver)==null?void 0:_.last_name}?`:"Are you sure?",description:r.action==="delete"?"This action cannot be undone and will permanently remove the driver from the system.":r.action==="unarchive"?"This driver will be restored and regain active status.":"",confirmText:r.action==="delete"?"Delete":r.action==="unarchive"?"Unarchive":"Confirm",confirmVariant:r.action==="delete"?"destructive":"default",loading:$,onCancel:O,onConfirm:q})]})}export{Fe as default};
