import{d as D,r as M,u as T,T as x,j as e,H as F,C as R,a as P,b as I,h as O,c as k,B as p}from"./app-DL-qYY5V.js";import{n as L}from"./navigation-DAA2N51J.js";import{I as f}from"./input-Dm4SEXxy.js";import{L as i}from"./label-e3QxUH-L.js";import{A as B}from"./app-layout-YqstQnqE.js";import{S as h,a as u,b as j,c as g,d as v}from"./select-Cp8NjZe8.js";import{T as V}from"./textarea-SHrtPYpi.js";import{C as $}from"./chevron-left-DFeVEtK7.js";import"./index-BTzg1GwG.js";import"./index-1tPPImJd.js";import"./app-logo-icon-BnXlkpcX.js";import"./bus-CKTBKI0T.js";import"./dollar-sign-Cx0-nQIX.js";import"./users-DNGXY-sJ.js";import"./clipboard-list-CxRumT8v.js";import"./index-uC6ZAdKJ.js";function ae({drivers:d=[]}){var m;const{userRoles:A}=D().props,[n,l]=M.useState(null),{data:r,setData:a,post:N,processing:c,errors:t,reset:b}=T({name:"",description:"",offense_date:"",severity:"low",evidence_file:null,offender_type:"driver",offender_id:""}),y=["Reckless Driving","Speeding","Overloading","Route Deviation","Poor Customer Service","Vehicle Maintenance Issues","Unauthorized Stops","Inappropriate Behavior","Operating Without Valid License","Operating Outside Designated Hours","Unauthorized Fare Increase","Other"],o=[{title:"Misconduct Management",href:x("misconducts.index")},{title:"Report Misconduct",href:x("misconducts.create")}],w=s=>{l(s),a("offender_id",(s==null?void 0:s.id)||"")},_=s=>{s.preventDefault(),N("/misconducts",{forceFormData:!0,onSuccess:()=>{b(),l(null)}})};return e.jsxs(B,{breadcrumbs:o,children:[e.jsx(F,{title:"Report Driver Misconduct"}),e.jsx("div",{className:"w-full max-w-full overflow-hidden",children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx("div",{className:"flex flex-col gap-4 mb-6",children:e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4",children:[e.jsxs("button",{className:"flex items-center px-3 py-1 rounded hover:bg-gray-100 border border-gray-200 w-fit",onClick:()=>L(o),children:[e.jsx($,{className:"h-4 w-4 mr-1"})," Back"]}),e.jsx("div",{className:"flex-1 sm:flex-none",children:e.jsx("span",{className:"text-muted-foreground text-sm sm:text-base font-medium break-words",children:"Report a misconduct incident for one of your drivers"})})]})}),e.jsxs(R,{className:"max-w-4xl mx-auto",children:[e.jsxs(P,{children:[e.jsxs(I,{className:"flex items-center gap-2 text-red-600",children:[e.jsx(O,{className:"h-6 w-6"}),"Report Driver Misconduct"]}),e.jsx("p",{className:"text-gray-600 mt-2",children:"Please provide detailed information about the misconduct incident. This will affect the driver's trust score and may result in disciplinary action."})]}),e.jsx(k,{children:e.jsxs("form",{onSubmit:_,className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"lg:col-span-2",children:[e.jsx(i,{htmlFor:"driver-select",className:"text-base font-medium",children:"Select Driver *"}),e.jsxs(h,{value:((m=n==null?void 0:n.id)==null?void 0:m.toString())||"",onValueChange:s=>{const C=d.find(S=>S.id.toString()===s);w(C)},children:[e.jsx(u,{className:`mt-2 ${t.offender_id?"border-red-500":""}`,children:e.jsx(j,{placeholder:"Choose a driver..."})}),e.jsx(g,{children:d.map(s=>e.jsx(v,{value:s.id.toString(),children:e.jsxs("div",{className:"flex flex-col",children:[e.jsxs("span",{className:"font-medium",children:[s.first_name," ",s.last_name]}),e.jsxs("span",{className:"text-sm text-gray-500",children:[s.phone_number,s.minibus&&` • ${s.minibus.plate_number}`]})]})},s.id))})]}),t.offender_id&&e.jsx("p",{className:"text-sm text-red-500 mt-1",children:t.offender_id})]}),e.jsxs("div",{children:[e.jsx(i,{htmlFor:"misconduct-type",className:"text-base font-medium",children:"Misconduct Type *"}),e.jsxs(h,{value:r.name,onValueChange:s=>a("name",s),children:[e.jsx(u,{className:`mt-2 ${t.name?"border-red-500":""}`,children:e.jsx(j,{placeholder:"Select misconduct type..."})}),e.jsx(g,{children:y.map(s=>e.jsx(v,{value:s,children:s},s))})]}),t.name&&e.jsx("p",{className:"text-sm text-red-500 mt-1",children:t.name})]}),e.jsxs("div",{children:[e.jsx(i,{htmlFor:"offense-date",className:"text-base font-medium",children:"Offense Date *"}),e.jsx(f,{id:"offense-date",type:"date",value:r.offense_date,onChange:s=>a("offense_date",s.target.value),className:`mt-2 ${t.offense_date?"border-red-500":""}`,max:new Date().toISOString().split("T")[0]}),t.offense_date&&e.jsx("p",{className:"text-sm text-red-500 mt-1",children:t.offense_date})]}),e.jsxs("div",{children:[e.jsx(i,{htmlFor:"evidence-file",className:"text-base font-medium",children:"Evidence File (Optional)"}),e.jsxs("div",{className:"mt-2",children:[e.jsx(f,{id:"evidence-file",type:"file",onChange:s=>a("evidence_file",s.target.files[0]),className:t.evidence_file?"border-red-500":"",accept:".pdf,.jpg,.jpeg,.png"}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Accepted formats: Images (JPG, JPEG, PNG) and PDF files only (Max: 5MB)"})]}),t.evidence_file&&e.jsx("p",{className:"text-sm text-red-500 mt-1",children:t.evidence_file})]})]}),e.jsxs("div",{children:[e.jsx(i,{htmlFor:"description",className:"text-base font-medium",children:"Description"}),e.jsx(V,{id:"description",placeholder:"Provide detailed information about the misconduct incident, including what happened, when, where, and any other relevant details...",value:r.description,onChange:s=>a("description",s.target.value),className:`mt-2 min-h-[120px] ${t.description?"border-red-500":""}`,rows:5}),t.description&&e.jsx("p",{className:"text-sm text-red-500 mt-1",children:t.description})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 pt-6 border-t",children:[e.jsx(p,{type:"button",variant:"outline",onClick:()=>window.history.back(),className:"sm:w-auto",children:"Cancel"}),e.jsx(p,{type:"submit",disabled:c,className:"bg-red-600 hover:bg-red-700 text-white sm:w-auto",children:c?"Reporting...":"Report Misconduct"})]})]})})]})]})})]})}export{ae as default};
