import{j as e,C as t,a as c,b as x,k as n,c as d}from"./app-DL-qYY5V.js";import{A as i}from"./app-logo-icon-BnXlkpcX.js";function m({children:s,title:a,description:l}){return e.jsx("div",{className:"bg-muted flex min-h-svh flex-col items-center justify-center gap-6 p-6 md:p-10 relative",children:e.jsx("div",{className:"flex w-full max-w-md flex-col gap-6",children:e.jsx("div",{className:"flex flex-col gap-6",children:e.jsxs(t,{className:"rounded-xl",children:[e.jsxs(c,{className:"px-10 pt-2 pb-0 text-center",children:[e.jsx("div",{className:"flex items-center justify-center",children:e.jsx(i,{className:"h-[auto] w-25 rounded-xl"})}),e.jsx(x,{className:"text-xl",children:a}),e.jsx(n,{children:l})]}),e.jsx(d,{className:"px-10 py-2",children:s})]})})})})}function u({children:s,title:a,description:l,...r}){return e.jsx(m,{title:a,description:l,...r,children:s})}export{u as A};
