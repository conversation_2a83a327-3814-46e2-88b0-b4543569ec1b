import{i as u,j as e,L as m,B as r,C as j}from"./app-DL-qYY5V.js";import{n as w}from"./navigation-DAA2N51J.js";import{A as f}from"./app-layout-YqstQnqE.js";import{A as v}from"./arrow-left-DCW23wrL.js";import{H as g}from"./history-Boy80jYJ.js";import"./index-1tPPImJd.js";import"./index-BTzg1GwG.js";import"./app-logo-icon-BnXlkpcX.js";import"./bus-CKTBKI0T.js";import"./dollar-sign-Cx0-nQIX.js";import"./users-DNGXY-sJ.js";import"./clipboard-list-CxRumT8v.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const y=[["path",{d:"m18 14 4 4-4 4",key:"10pe0f"}],["path",{d:"m18 2 4 4-4 4",key:"pucp1d"}],["path",{d:"M2 18h1.973a4 4 0 0 0 3.3-1.7l5.454-8.6a4 4 0 0 1 3.3-1.7H22",key:"1ailkh"}],["path",{d:"M2 6h1.972a4 4 0 0 1 3.6 2.2",key:"km57vx"}],["path",{d:"M22 18h-6.041a4 4 0 0 1-3.3-1.8l-.359-.45",key:"os18l9"}]],b=u("Shuffle",y);function z({minibus:s,userRole:a,history:l=[]}){var x;if(!s)return e.jsx(f,{children:e.jsxs("div",{className:"container mx-auto px-4 py-8 text-center",children:[e.jsx("h1",{className:"text-2xl font-bold mb-4",children:"Minibus Not Found"}),e.jsx("p",{className:"text-gray-600",children:"The minibus you are looking for does not exist or has been removed."}),e.jsx(m,{href:"/minibuses",children:e.jsx(r,{variant:"outline",className:"mt-6",children:"Back to Minibuses"})})]})});const i=[{title:"Dashboard",href:"/dashboard"},{title:a==="minibus owner"?"My Minibuses":"Minibus Management",href:"/minibuses"},{title:s.number_plate,href:`/minibuses/${s.id}`}];return a==="minibus owner"&&s.archived?null:e.jsx(f,{breadcrumbs:i,children:e.jsxs("div",{className:"p-2 sm:p-4 md:p-8",children:[e.jsx("div",{className:"flex flex-col sm:flex-row gap-2 sm:gap-4 items-center mb-4 sm:mb-6",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(r,{variant:"outline",onClick:()=>w(i),className:"w-full sm:w-auto",children:[e.jsx(v,{className:"h-4 w-4 mr-2"}),"Back"]}),e.jsx("span",{className:"text-muted-foreground text-base font-medium",children:"View detailed information about this minibus and its ownership history."})]})}),e.jsx("div",{className:"w-full mx-auto",children:e.jsx(j,{className:"mb-2 sm:mb-6",children:e.jsxs("div",{className:"p-2 sm:p-6",children:[e.jsx("h3",{className:"text-base sm:text-lg md:text-xl font-semibold mb-3",children:"Minibus Information"}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"mb-2 text-sm sm:text-base",children:[e.jsx("strong",{children:"Number Plate:"})," ",s.number_plate]}),e.jsxs("div",{className:"mb-2 text-sm sm:text-base",children:[e.jsx("strong",{children:"Make:"})," ",s.make||"-"]}),e.jsxs("div",{className:"mb-2 text-sm sm:text-base",children:[e.jsx("strong",{children:"Model:"})," ",s.model||"-"]}),e.jsxs("div",{className:"mb-2 text-sm sm:text-base",children:[e.jsx("strong",{children:"Year of Make:"})," ",s.year_of_make||"-"]}),e.jsxs("div",{className:"mb-2 text-sm sm:text-base",children:[e.jsx("strong",{children:"Main Colour:"})," ",s.main_colour||"-"]}),e.jsxs("div",{className:"mb-2 text-sm sm:text-base",children:[e.jsx("strong",{children:"Route:"})," ",((x=s.route)==null?void 0:x.name)||"-"]}),a!=="minibus owner"&&e.jsxs("div",{className:"mb-2 text-sm sm:text-base",children:[e.jsx("strong",{children:"Owner:"})," ",s.archived?"Non-member":s.minibusOwner?`${s.minibusOwner.first_name} ${s.minibusOwner.last_name}`:"No owner assigned"]}),s.archived&&e.jsxs("div",{className:"mb-2 text-sm sm:text-base text-amber-600",children:[e.jsx("strong",{children:"Status:"})," Archived (Transferred to Non-Member)"]})]}),e.jsxs("div",{className:"mt-4 flex justify-end gap-2",children:[a==="minibus owner"&&e.jsx(m,{href:route("minibuses.transfer.request",s.id),children:e.jsxs(r,{variant:"outline",size:"sm",children:[e.jsx(b,{className:"h-4 w-4 mr-2"}),"Request Transfer"]})}),a==="association clerk"&&!s.archived&&e.jsx(m,{href:route("minibuses.transfer",s.id),children:e.jsxs(r,{variant:"default",size:"sm",className:"bg-blue-600 hover:bg-blue-700",children:[e.jsx(b,{className:"h-4 w-4 mr-2"}),"Transfer Ownership"]})})]})]})})}),a!=="minibus owner"&&e.jsxs("div",{className:"w-full mx-auto",children:[e.jsx("div",{className:"flex justify-end mb-4",children:e.jsx(m,{href:route("minibuses.history",s.id),children:e.jsxs(r,{variant:"outline",children:[e.jsx(g,{className:"h-4 w-4 mr-2"}),"View Detailed History"]})})}),e.jsx(j,{children:e.jsxs("div",{className:"p-2 sm:p-4",children:[e.jsx("h3",{className:"text-base sm:text-lg md:text-2xl font-semibold mb-5 text-center",children:"Ownership History"}),e.jsx("div",{className:"overflow-x-auto w-full",children:e.jsxs("table",{className:"w-full bg-white border text-xs sm:text-sm md:text-base",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"bg-gray-50",children:[e.jsx("th",{className:"py-2 px-2 sm:px-3 md:px-6 border text-left font-semibold text-xs sm:text-sm",children:"Previous Owner"}),e.jsx("th",{className:"py-2 px-2 sm:px-3 md:px-6 border text-left font-semibold text-xs sm:text-sm",children:"New Owner"}),e.jsx("th",{className:"py-2 px-2 sm:px-3 md:px-6 border text-left font-semibold text-xs sm:text-sm",children:"Transferred At"}),e.jsx("th",{className:"py-2 px-2 sm:px-3 md:px-6 border text-left font-semibold text-xs sm:text-sm",children:"Transfer Type"})]})}),e.jsx("tbody",{children:l.length>0?l.map((t,N)=>{var n,d,c,o,h,p;return e.jsxs("tr",{className:"hover:bg-gray-50",children:[e.jsx("td",{className:"py-2 px-2 sm:px-3 md:px-6 border",children:e.jsxs("div",{className:"flex flex-col",children:[e.jsxs("span",{className:"font-medium text-xs sm:text-sm",children:[(n=t.previous_owner)==null?void 0:n.first_name," ",(d=t.previous_owner)==null?void 0:d.last_name]}),e.jsx("span",{className:"text-gray-500 text-xs",children:(c=t.previous_owner)==null?void 0:c.email})]})}),e.jsx("td",{className:"py-2 px-2 sm:px-3 md:px-6 border",children:e.jsx("div",{className:"flex flex-col",children:t.transfer_type==="external"?e.jsx("span",{className:"font-medium text-xs sm:text-sm text-gray-400",children:"Non-member"}):e.jsxs(e.Fragment,{children:[e.jsxs("span",{className:"font-medium text-xs sm:text-sm",children:[(o=t.new_owner)==null?void 0:o.first_name," ",(h=t.new_owner)==null?void 0:h.last_name]}),e.jsx("span",{className:"text-gray-500 text-xs",children:(p=t.new_owner)==null?void 0:p.email})]})})}),e.jsx("td",{className:"py-2 px-2 sm:px-3 md:px-6 border",children:e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"font-medium text-xs sm:text-sm",children:new Date(t.created_at).toLocaleDateString()}),e.jsx("span",{className:"text-gray-500 text-xs",children:new Date(t.created_at).toLocaleTimeString()})]})}),e.jsx("td",{className:"py-2 px-2 sm:px-3 md:px-6 border",children:e.jsx("span",{className:"font-medium text-xs sm:text-sm capitalize",children:t.transfer_type})})]},N)}):e.jsx("tr",{children:e.jsx("td",{colSpan:"4",className:"py-6 sm:py-8 px-3 sm:px-6 text-center text-gray-500 text-sm sm:text-base",children:"No ownership history available"})})})]})})]})})]})]})})}export{z as default};
