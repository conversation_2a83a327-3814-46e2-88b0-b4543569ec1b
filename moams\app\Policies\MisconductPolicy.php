<?php

namespace App\Policies;

use App\Models\Misconduct;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class MisconductPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->hasRole('association clerk') ||
            $user->hasRole('association manager') ||
            $user->hasRole('system admin');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Misconduct $misconduct): bool
    {
        return $user->hasRole('association clerk') ||
            $user->hasRole('association manager') ||
            $user->hasRole('system admin');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->hasRole('minibus owner');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Misconduct $misconduct): bool
    {
        return $user->hasRole('minibus owner');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Misconduct $misconduct): bool
    {
        return $user->hasRole('association manager') ||
            $user->hasRole('system admin');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Misconduct $misconduct): bool
    {
        return $user->hasRole('system admin');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Misconduct $misconduct): bool
    {
        return $user->hasRole('system admin');
    }
}
