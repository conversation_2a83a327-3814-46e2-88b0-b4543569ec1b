import { router } from '@inertiajs/react';

/**
 * Navigate to the logical parent breadcrumb instead of browser history
 * @param {Array} breadcrumbs - Array of breadcrumb objects with href property
 * @returns {void}
 */
export const navigateToLogicalParent = (breadcrumbs) => {
    if (breadcrumbs && breadcrumbs.length > 1) {
        // Navigate to the second-to-last breadcrumb (logical parent)
        const parentUrl = breadcrumbs[breadcrumbs.length - 2].href;
        router.visit(parentUrl);
    } else {
        // Fallback to browser history if no logical parent
        window.history.back();
    }
};

/**
 * Get the logical parent breadcrumb URL
 * @param {Array} breadcrumbs - Array of breadcrumb objects with href property
 * @returns {string|null} - Parent URL or null if no parent
 */
export const getLogicalParent = (breadcrumbs) => {
    if (breadcrumbs && breadcrumbs.length > 1) {
        return breadcrumbs[breadcrumbs.length - 2].href;
    }
    return null;
};
