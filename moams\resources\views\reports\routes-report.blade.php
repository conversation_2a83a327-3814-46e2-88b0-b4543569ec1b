<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Route Usage Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
            line-height: 1.4;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #2563eb;
            padding-bottom: 20px;
        }

        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 15px auto;
            display: block;
        }

        .header h1 {
            color: #2563eb;
            margin: 0;
            font-size: 24px;
        }

        .header h2 {
            color: #64748b;
            margin: 5px 0 0 0;
            font-size: 16px;
            font-weight: normal;
        }
        
        .report-info {
            background-color: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 25px;
            border-left: 4px solid #2563eb;
        }
        
        .report-info h3 {
            margin: 0 0 10px 0;
            color: #2563eb;
            font-size: 16px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
        }
        
        .info-label {
            font-weight: bold;
            color: #475569;
        }
        
        .summary-stats {
            width: 100%;
            margin-bottom: 25px;
            border-collapse: collapse;
            text-align: center;
        }

        .stat-card {
            background-color: #f8fafc;
            padding: 20px 15px;
            text-align: center;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            width: 25%;
            display: inline-block;
            vertical-align: top;
            box-sizing: border-box;
            margin-right: -4px;
        }

        .stat-card:first-child {
            border-top-left-radius: 12px;
            border-bottom-left-radius: 12px;
            border-right: 1px solid #e2e8f0;
        }

        .stat-card:last-child {
            border-top-right-radius: 12px;
            border-bottom-right-radius: 12px;
            border-left: 1px solid #e2e8f0;
        }

        .stat-card:not(:first-child):not(:last-child) {
            border-left: 1px solid #e2e8f0;
            border-right: 1px solid #e2e8f0;
            border-radius: 0;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .routes-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            background-color: white;
            border: 1px solid #e2e8f0;
        }
        
        .routes-table th {
            background-color: #2563eb;
            color: white;
            padding: 12px 8px;
            text-align: left;
            font-weight: bold;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .routes-table td {
            padding: 10px 8px;
            border-bottom: 1px solid #e2e8f0;
            font-size: 14px;
        }
        
        .routes-table tr:nth-child(even) {
            background-color: #f8fafc;
        }
        
        .routes-table tr:hover {
            background-color: #f1f5f9;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .status-active {
            background-color: #dcfce7;
            color: #166534;
        }
        
        .status-inactive {
            background-color: #f1f5f9;
            color: #475569;
        }
        
        .usage-high {
            color: #dc2626;
            font-weight: bold;
        }
        
        .usage-medium {
            color: #ea580c;
            font-weight: bold;
        }
        
        .usage-low {
            color: #16a34a;
        }
        
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
            text-align: center;
            color: #64748b;
            font-size: 12px;
        }
        
        .page-break {
            page-break-before: always;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 15px;
            }
            
            .header {
                margin-bottom: 20px;
                padding-bottom: 15px;
            }
            
            .summary-stats {
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <!-- Professional Text Logo - No image dependencies -->
        <div style="width: 120px; height: 60px; margin: 0 auto 15px auto; background-color: #2563eb; border-radius: 12px; position: relative; color: white; font-weight: bold;">
            <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 18px; letter-spacing: 1px; text-align: center; white-space: nowrap;">
                MOAMS
            </div>
        </div>
        <h1>Minibus Owners Association Management System</h1>
        <h2>Route Usage Report</h2>
    </div>

    <div class="report-info">
        <h3>Report Information</h3>
        <div class="info-grid">
            <div class="info-item">
                <span class="info-label">Generated On:</span>
                <span>{{ $reportDate }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Generated By:</span>
                <span>{{ $generatedBy }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Total Routes:</span>
                <span>{{ $totalRoutes }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Total Minibuses:</span>
                <span>{{ $totalMinibuses }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Report Type:</span>
                <span>Complete Route Analysis</span>
            </div>
        </div>
    </div>

    <div style="text-align: center; margin-bottom: 25px;">
        <table style="width: 100%; margin: 0 auto; border-collapse: separate; border-spacing: 10px;">
            <tr>
                <td style="width: 25%; background-color: #f8fafc; padding: 20px 15px; text-align: center; border: 2px solid #e2e8f0; border-radius: 8px;">
                    <div style="font-size: 24px; font-weight: bold; color: #2563eb; margin-bottom: 5px;">{{ $totalRoutes }}</div>
                    <div style="font-size: 10px; color: #64748b; text-transform: uppercase; letter-spacing: 0.5px;">Total Routes</div>
                </td>
                <td style="width: 25%; background-color: #f8fafc; padding: 20px 15px; text-align: center; border: 2px solid #e2e8f0; border-radius: 8px;">
                    <div style="font-size: 24px; font-weight: bold; color: #2563eb; margin-bottom: 5px;">{{ $activeRoutes }}</div>
                    <div style="font-size: 10px; color: #64748b; text-transform: uppercase; letter-spacing: 0.5px;">Active Routes</div>
                </td>
                <td style="width: 25%; background-color: #f8fafc; padding: 20px 15px; text-align: center; border: 2px solid #e2e8f0; border-radius: 8px;">
                    <div style="font-size: 24px; font-weight: bold; color: #2563eb; margin-bottom: 5px;">{{ $inactiveRoutes }}</div>
                    <div style="font-size: 10px; color: #64748b; text-transform: uppercase; letter-spacing: 0.5px;">Inactive Routes</div>
                </td>
                <td style="width: 25%; background-color: #f8fafc; padding: 20px 15px; text-align: center; border: 2px solid #e2e8f0; border-radius: 8px;">
                    <div style="font-size: 24px; font-weight: bold; color: #2563eb; margin-bottom: 5px;">{{ $totalMinibuses }}</div>
                    <div style="font-size: 10px; color: #64748b; text-transform: uppercase; letter-spacing: 0.5px;">Total Minibuses</div>
                </td>
            </tr>
        </table>
    </div>

    <table class="routes-table">
        <thead>
            <tr>
                <th style="width: 5%;">#</th>
                <th style="width: 40%;">Route Name</th>
                <th style="width: 15%;">Status</th>
                <th style="width: 20%;">Usage Count</th>
                <th style="width: 20%;">Usage Level</th>
            </tr>
        </thead>
        <tbody>
            @foreach($routes as $index => $route)
            <tr>
                <td>{{ $index + 1 }}</td>
                <td>{{ $route->name }}</td>
                <td>
                    <span class="status-badge {{ $route->status === 'active' ? 'status-active' : 'status-inactive' }}">
                        {{ ucfirst($route->status) }}
                    </span>
                </td>
                <td>
                    <span class="
                        @if($route->minibuses_count >= 10) usage-high
                        @elseif($route->minibuses_count >= 5) usage-medium
                        @else usage-low
                        @endif
                    ">
                        {{ $route->minibuses_count }} minibus{{ $route->minibuses_count !== 1 ? 'es' : '' }}
                    </span>
                </td>
                <td>
                    @if($route->minibuses_count >= 10)
                        <span class="usage-high">High Usage</span>
                    @elseif($route->minibuses_count >= 5)
                        <span class="usage-medium">Medium Usage</span>
                    @elseif($route->minibuses_count > 0)
                        <span class="usage-low">Low Usage</span>
                    @else
                        <span style="color: #64748b;">No Usage</span>
                    @endif
                </td>
            </tr>
            @endforeach
        </tbody>
    </table>

    @if($routes->isEmpty())
    <div style="text-align: center; padding: 40px; color: #64748b;">
        <p style="font-size: 16px; margin: 0;">No routes found in the system.</p>
    </div>
    @endif

    <div class="footer">
        <p>This report was generated automatically by the Minibus Owners Association Management System.</p>
        <p>For questions or concerns, please contact the system administrator.</p>
        <p style="margin-top: 10px; font-style: italic;">
            Report generated on {{ $reportDate }} at {{ $reportTime }}
        </p>
    </div>
</body>
</html>
