<?php

namespace App\Http\Controllers;

use App\Models\Payment;
use App\Models\Membership;
use App\Models\User;
use App\Http\Requests\StorePaymentRequest;
use App\Http\Requests\UpdatePaymentRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Notifications\PaymentSuccessNotification;
use Inertia\Inertia;

class PaymentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage (cash payments only).
     */
    public function store(StorePaymentRequest $request)
    {
        $payment = Payment::create([
            'user_id' => $request->user_id,
            'membership_id' => $request->membership_id,
            'amount' => $request->amount,
            'fee_type' => $request->fee_type, // 'registration' or 'affiliation'
            'status' => $request->status ?? 'pending',
            'payment_method' => $request->payment_method ?? 'cash',
            'paid_at' => $request->status === 'fulfilled' ? now() : null,
        ]);

        // If payment is fulfilled at creation, update membership status and send notifications
        if ($payment->status === 'fulfilled' && $payment->membership) {
            // Update verification details for clerk payments
            $payment->update([
                'verification_method' => 'clerk_manual',
                'verification_notes' => 'Marked as paid by association clerk: ' . auth()->user()->first_name . ' ' . auth()->user()->last_name,
            ]);

            // Update membership status
            $this->updateMembershipStatus($payment);

            // Send payment notifications
            $this->sendPaymentNotifications($payment);

            return redirect()->back()->with('success', 'Payment saved successfully! Confirmation email sent to member.');
        }

        return redirect()->back()->with('success', 'Payment saved successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Payment $payment)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Payment $payment)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdatePaymentRequest $request, Payment $payment)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Payment $payment)
    {
        //
    }

    /**
     * Show all unpaid memberships for a given user (for clerks to select and pay).
     */
    public function unpaidMemberships($userId)
    {
        $user = User::with([
            'memberships' => function ($q) {
                $q->orderBy('start_date');
            }
        ])->findOrFail($userId);
        $unpaidMemberships = $user->memberships->filter(function ($membership) {
            return in_array($membership->status, [
                'Unregistered',
                'Need affiliation fee',
                'Affiliation fee not paid',
            ]);
        });
        return Inertia::render('paymentManagement/unpaid-memberships', [
            'user' => $user,
            'unpaidMemberships' => $unpaidMemberships->values(),
        ]);
    }

    /**
     * Show membership summary for a given user (for clerks to view payment history).
     */
    public function membershipSummary($userId)
    {
        $user = User::with([
            'memberships' => function ($q) {
                $q->orderBy('start_date');
            }
        ])->findOrFail($userId);
        
        return Inertia::render('MembershipManagement/summary-membership', [
            'user' => $user,
            'memberships' => $user->memberships,
            'userRoles' => auth()->user()->roles->pluck('name')->toArray(),
            'currentFees' => [
                'registration' => \App\Models\FeeSetting::getCurrentFeeAmount('registration'),
                'affiliation' => \App\Models\FeeSetting::getCurrentFeeAmount('affiliation'),
            ],
        ]);
    }

    /**
     * Mark a membership as paid (fulfilled) by a clerk.
     */
    public function markPaid($membershipId)
    {
        $membership = Membership::findOrFail($membershipId);
        $user = $membership->user;
        $feeType = strtolower($membership->type); // e.g., 'registration' or 'affiliation'
        $amount = \App\Models\FeeSetting::getCurrentFeeAmount($feeType);

        // Create payment record
        $payment = Payment::create([
            'user_id' => $user->id,
            'membership_id' => $membership->id,
            'amount' => $amount,
            'fee_type' => $feeType,
            'status' => 'fulfilled',
            'payment_method' => 'cash',
            'paid_at' => now(),
            'verification_method' => 'clerk_manual',
            'verification_notes' => 'Marked as paid by association clerk: ' . auth()->user()->first_name . ' ' . auth()->user()->last_name,
        ]);

        // Update membership status
        $this->updateMembershipStatus($payment);

        // Send payment notifications
        $this->sendPaymentNotifications($payment);

        return redirect()->back()->with('success', 'Payment marked as paid successfully! Confirmation email sent to member.');
    }

    /**
     * Update membership status based on payment
     */
    private function updateMembershipStatus($payment)
    {
        $membership = $payment->membership;
        if (!$membership) return;

        if ($payment->fee_type === 'registration') {
            $membership->update(['status' => 'Registered']);
        } elseif ($payment->fee_type === 'affiliation') {
            $membership->update(['status' => 'Affiliation fee paid']);
        }
    }

    /**
     * Send payment notifications
     */
    private function sendPaymentNotifications($payment)
    {
        try {
            // Send notification to the member
            $payment->user->notify(new PaymentSuccessNotification($payment, false));

            // Send notification to association clerks
            $clerks = User::role('association clerk')->get();
            foreach ($clerks as $clerk) {
                $clerk->notify(new PaymentSuccessNotification($payment, true));
            }
        } catch (\Exception $e) {
            Log::error('Failed to send payment notifications', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}
