import { Head, Link, usePage, router } from '@inertiajs/react';
import { navigateToLogicalParent } from '@/utils/navigation';
import { ArrowLeft, Edit, Trash2, Mail, Phone, Calendar, User, Shield, ChevronRight, Home, Archive, RotateCcw } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarInitials } from '@/components/ui/avatar';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import AppLayout from '@/layouts/app-layout';
import ConfirmDialog from '@/components/ui/confirm-dialog';
import { useState } from 'react';

export default function UserDetail() {
    const { user, users = [] } = usePage().props;
    const [confirmDialog, setConfirmDialog] = useState({ open: false, action: null, user: null });
    const [loading, setLoading] = useState(false);

    // Count unarchived system admins
    const unarchivedAdmins = users.filter(u => u.roles.some(r => r.name === 'system admin') && !u.archived_at);
    const isLastUnarchivedAdmin = user.roles.some(r => r.name === 'system admin') && unarchivedAdmins.length === 1;

    const handleAction = (action, user) => {
        setConfirmDialog({ open: true, action, user });
    };
    const handleCancel = () => setConfirmDialog({ open: false, action: null, user: null });
    const handleConfirm = async () => {
        if (!confirmDialog.user) return;
        setLoading(true);
        const { id } = confirmDialog.user;
        if (confirmDialog.action === 'archive' || confirmDialog.action === 'unarchive') {
            await router.put(`/admin/users/${id}/${confirmDialog.action}`);
        }
        setLoading(false);
        setConfirmDialog({ open: false, action: null, user: null });
    };

    const breadcrumbs = [
        {
            title: 'Dashboard',
            href: '/dashboard',
        },
        {
            title: 'User Management',
            href: '/admin/users',
        },
        {
            title: `${user.first_name} ${user.last_name}`,
            href: `/admin/users/${user.id}`,
        },
    ];

    const getRoleBadgeColor = (roleName) => {
        switch (roleName) {
            case 'system admin':
                return 'bg-red-100 text-red-800 border-red-200';
            case 'minibus owner':
                return 'bg-green-100 text-green-800 border-green-200';
            case 'association clerk':
                return 'bg-purple-100 text-purple-800 border-purple-200';
            case 'association manager':
                return 'bg-orange-100 text-orange-800 border-orange-200';
            default:
                return 'bg-gray-100 text-gray-800 border-gray-200';
        }
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`User Details - ${user.first_name} ${user.last_name}`} />

            <div className="p-8">
                {/* Header */}
                <div className="flex justify-between items-center mb-8">
                    <div className="flex items-center space-x-4">
                        <Button variant="outline" onClick={() => navigateToLogicalParent(breadcrumbs)} className="w-full sm:w-auto">
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Back
                        </Button>
                    </div>
                    <TooltipProvider>
                        <div className="flex space-x-2">
                            {/* Edit button: hide if last unarchived admin */}
                            {!(isLastUnarchivedAdmin && !user.archived_at) && (
                                <Tooltip>
                                    <TooltipTrigger asChild>
                                        <Link href={`/admin/users/${user.id}/edit`}>
                                            <Button className="bg-green-600 hover:bg-green-700">
                                                <Edit className="h-4 w-4 sm:mr-2" />
                                            </Button>
                                        </Link>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                        <p>Edit user information</p>
                                    </TooltipContent>
                                </Tooltip>
                            )}

                            {/* Only show archive/unarchive button if not last unarchived admin */}
                            {!(isLastUnarchivedAdmin && !user.archived_at) && (
                                <Tooltip>
                                    <TooltipTrigger asChild>
                                        <Button
                                            variant={user.archived_at ? 'success' : 'outline'}
                                            className="ml-2"
                                            onClick={() => handleAction(user.archived_at ? 'unarchive' : 'archive', user)}
                                        >
                                            {user.archived_at ? <RotateCcw className="h-4 w-4 sm:mr-2" /> : <Archive className="h-4 w-4 sm:mr-2" />}
                                        </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                        <p>{user.archived_at ? 'Unarchive user' : 'Archive user'}</p>
                                    </TooltipContent>
                                </Tooltip>
                            )}
                        </div>
                    </TooltipProvider>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    {/* User Profile Card */}
                    <div className="lg:col-span-1">
                        <Card>
                            <CardHeader className="text-center">
                                <div className="flex justify-center mb-4">
                                    <Avatar className="h-24 w-24">
                                        <AvatarFallback>
                                            <AvatarInitials
                                                text={`${user.first_name} ${user.last_name}`}
                                                className="text-2xl"
                                            />
                                        </AvatarFallback>
                                    </Avatar>
                                </div>
                                <CardTitle className="text-xl">
                                    {user.first_name} {user.last_name}
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center space-x-3">
                                    <Mail className="h-4 w-4 text-gray-500" />
                                    <span className="text-sm">{user.email}</span>
                                </div>
                                <div className="flex items-center space-x-3">
                                    <Phone className="h-4 w-4 text-gray-500" />
                                    <span className="text-sm">{user.phone_number}</span>
                                </div>
                                <div className="flex items-center space-x-3">
                                    <User className="h-4 w-4 text-gray-500" />
                                    <span className="text-sm capitalize">{user.gender}</span>
                                </div>
                                {user.roles.some(role => role.name === 'minibus owner') && (
                                    <div className="flex items-center space-x-3">
                                        <Calendar className="h-4 w-4 text-gray-500" />
                                        <span className="text-sm">
                                            Joined on {user.joining_date ? formatDate(user.joining_date) : 'N/A'}
                                        </span>
                                    </div>
                                )}
                                {user.archived_at && (
                                    <Badge variant="destructive" className="ml-2">Archived</Badge>
                                )}
                            </CardContent>
                        </Card>
                        {/* Location Information Card */}
                        <Card className="mt-6">
                            <CardHeader>
                                <CardTitle>Location Information</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label className="text-sm font-medium text-muted-foreground">District</label>
                                        <p className="text-sm">{user.district}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-muted-foreground">Village/Town</label>
                                        <p className="text-sm">{user.village}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* User Details */}
                    <div className="lg:col-span-2 space-y-6">
                        {/* Roles and Permissions */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <Shield className="h-5 w-5 mr-2" />
                                    Roles & Permissions
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    <div>
                                        <h4 className="font-medium mb-2">Assigned Roles:</h4>
                                        <div className="flex flex-wrap gap-2">
                                            {user.roles.map((role) => (
                                                <Badge
                                                    key={role.id}
                                                    variant="outline"
                                                    className={`${getRoleBadgeColor(role.name)}`}
                                                >
                                                    {role.name}
                                                </Badge>
                                            ))}
                                        </div>
                                    </div>
                                    {user.roles.length === 0 && (
                                        <p className="text-gray-500 text-sm">No roles assigned</p>
                                    )}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Account Information */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Account Information</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <Label className="text-sm font-medium text-gray-600">Email Verified</Label>
                                        <p className="text-sm mt-1">
                                            <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
                                                Verified
                                            </Badge>
                                        </p>
                                    </div>
                                    <div>
                                        <Label className="text-sm font-medium text-gray-600">Last Updated</Label>
                                        <p className="text-sm mt-1">{formatDate(user.updated_at)}</p>
                                    </div>
                                    <div>
                                        <Label className="text-sm font-medium text-gray-600">Account Status</Label>
                                        <p className="text-sm mt-1">
                                            {user.archived_at ? (
                                                <Badge variant="destructive" className="bg-red-100 text-red-800 border-red-200">Inactive</Badge>
                                            ) : (
                                                <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">Active</Badge>
                                            )}
                                        </p>
                                    </div>
                                    <div>
                                        <Label className="text-sm font-medium text-gray-600">Created on</Label>
                                        <p className="text-sm mt-1">{formatDate(user.created_at)}</p>
                                        <p className="text-xs text-gray-500 mt-1">Last updated {formatDate(user.updated_at)}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Commitment Statement Section */}
                        {user.roles.some(role => role.name === 'minibus owner') && user.commitment && (
                            <Card>
                                <CardHeader>
                                    <CardTitle>Commitment Statement</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-sm whitespace-pre-line">{user.commitment}</p>
                                </CardContent>
                            </Card>
                        )}
                    </div>
                </div>
            </div>
            <ConfirmDialog
                open={confirmDialog.open}
                title={
                    confirmDialog.action === 'archive' ? `Archive ${confirmDialog.user?.first_name} ${confirmDialog.user?.last_name}?` :
                        confirmDialog.action === 'unarchive' ? `Unarchive ${confirmDialog.user?.first_name} ${confirmDialog.user?.last_name}?` :
                            'Are you sure?'
                }
                description={
                    confirmDialog.action === 'archive' ? 'This user will be archived and will not be able to access the system.' :
                        confirmDialog.action === 'unarchive' ? 'This user will be restored and regain access to the system.' :
                            ''
                }
                confirmText={
                    confirmDialog.action === 'archive' ? 'Archive' :
                        confirmDialog.action === 'unarchive' ? 'Unarchive' :
                            'Confirm'
                }
                confirmVariant={confirmDialog.action === 'delete' ? 'destructive' : 'default'}
                loading={loading}
                onCancel={handleCancel}
                onConfirm={handleConfirm}
            />
        </AppLayout>
    );
} 