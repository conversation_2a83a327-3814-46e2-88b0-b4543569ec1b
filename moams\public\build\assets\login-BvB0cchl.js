import{u as g,j as e,H as j,B as f}from"./app-DL-qYY5V.js";import{I as n}from"./input-error-C6jcuIY6.js";import{T as w}from"./text-link-2FoWZb-l.js";import{C as v}from"./checkbox-CmZhyNb2.js";import{I as d}from"./input-Dm4SEXxy.js";import{L as t}from"./label-e3QxUH-L.js";import{A as N}from"./auth-layout-TDtItk3e.js";import{L}from"./loader-circle-D3J3XKS7.js";import"./index-1tPPImJd.js";import"./index-uC6ZAdKJ.js";import"./index-BTzg1GwG.js";import"./app-logo-icon-BnXlkpcX.js";function T({status:m,canResetPassword:c}){const{data:r,setData:o,post:p,processing:i,errors:s,reset:x}=g({email:"",password:"",remember:!1}),l=a=>{const{id:h,value:b}=a.target;o(h,b)},u=a=>{a.preventDefault(),p(route("login"),{onFinish:()=>x("password")})};return e.jsxs(N,{title:"Log in to your account",description:"",children:[e.jsx(j,{title:"Log in"}),s.email&&e.jsx("div",{className:"mb-4 text-center text-sm font-medium text-red-600",children:s.email}),e.jsx("form",{className:"flex flex-col gap-6",onSubmit:u,children:e.jsxs("div",{className:"grid gap-6",children:[e.jsxs("div",{className:"grid gap-2",children:[e.jsx(t,{htmlFor:"email",children:"Email address"}),e.jsx(d,{id:"email",type:"email",autoFocus:!0,tabIndex:1,autoComplete:"email",value:r.email,onChange:l,placeholder:"<EMAIL>"}),e.jsx(n,{message:s.email&&!s.password?"":s.email})]}),e.jsxs("div",{className:"grid gap-2",children:[e.jsx("div",{className:"flex items-center",children:e.jsx(t,{htmlFor:"password",children:"Password"})}),e.jsx(d,{id:"password",type:"password",tabIndex:2,autoComplete:"current-password",value:r.password,onChange:l,placeholder:"Password"}),e.jsx(n,{message:s.password})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(v,{id:"remember",name:"remember",checked:r.remember,onClick:()=>o("remember",!r.remember),tabIndex:3}),e.jsx(t,{htmlFor:"remember",children:"Remember me"}),c&&e.jsx(w,{href:route("password.request"),className:"ml-auto text-sm text-blue-500 no-underline hover:underline",tabIndex:5,children:"Forgot password?"})]}),e.jsxs(f,{type:"submit",className:"mt-4 w-full bg-blue-400 hover:bg-blue-500",tabIndex:4,disabled:i,children:[i&&e.jsx(L,{className:"h-4 w-4 animate-spin"}),"Log in"]})]})}),m&&e.jsx("div",{className:"mb-4 text-center text-sm font-medium text-green-600",children:m})]})}export{T as default};
