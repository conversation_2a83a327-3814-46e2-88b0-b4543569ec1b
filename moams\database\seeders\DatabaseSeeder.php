<?php

namespace Database\Seeders;

use App\Models\User;
use Spatie\Permission\Models\Role;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();

        // Create all the required roles
        $roles = [
            'association manager',
            'association clerk',
            'minibus owner',
            'system admin'
        ];

        foreach ($roles as $roleName) {
            Role::firstOrCreate(['name' => $roleName]);
        }

        // Create the admin user with all required fields
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => '<PERSON>',
                'last_name' => 'Mike',
                'gender' => 'Male',
                'district' => 'Ntcheu',
                'village' => 'Muwalo',
                'phone_number' => '0934567890',
                'password' => bcrypt('asdfghjkl'),
            ]
        );

        // Assign the system admin role
        $admin->assignRole('system admin');

        // Double-check and ensure the role is assigned
        if (!$admin->hasRole('system admin')) {
            $admin->assignRole('system admin');
        }

        echo "Admin user created/updated: " . $admin->email . "\n";
        echo "Has system admin role: " . ($admin->hasRole('system admin') ? 'Yes' : 'No') . "\n";
        echo "All roles created: " . implode(', ', $roles) . "\n";

        // Create sample users for each role
        $minibusOwners = [
            [
                'first_name' => 'James',
                'last_name' => 'Banda',
                'email' => '<EMAIL>',
                'gender' => 'Male',
                'district' => 'Blantyre',
                'village' => 'Area 25',
                'phone_number' => '0991234567',
            ],
            [
                'first_name' => 'Mary',
                'last_name' => 'Phiri',
                'email' => '<EMAIL>',
                'gender' => 'Female',
                'district' => 'Blantyre',
                'village' => 'Namiwawa',
                'phone_number' => '0881234567',
            ],
            [
                'first_name' => 'John',
                'last_name' => 'Chirwa',
                'email' => '<EMAIL>',
                'gender' => 'Male',
                'district' => 'Zomba',
                'village' => 'Matawale',
                'phone_number' => '0991234568',
            ],
            [
                'first_name' => 'Grace',
                'last_name' => 'Mbewe',
                'email' => '<EMAIL>',
                'gender' => 'Female',
                'district' => 'Mzuzu',
                'village' => 'Chibavi',
                'phone_number' => '0881234568',
            ],
            [
                'first_name' => 'Peter',
                'last_name' => 'Gondwe',
                'email' => '<EMAIL>',
                'gender' => 'Male',
                'district' => 'Kasungu',
                'village' => 'Chipata',
                'phone_number' => '0991234569',
            ],
        ];

        foreach ($minibusOwners as $ownerData) {
            $owner = User::updateOrCreate(
                ['email' => $ownerData['email']],
                array_merge($ownerData, [
                    'password' => bcrypt('password123'),
                    'commitment_statement' => 'I commit to abide by the Constitution, rules, and regulations of the Minibus Owners Association of Malawi (MOAM), and to uphold the values and objectives of the Association at all time',
                    'joining_date' => now(),
                ])
            );
            $owner->assignRole('minibus owner');
            // Create a membership for each minibus owner
            $startDate = now();
            $endDate = now()->addYear()->subDay();
            $owner->memberships()->create([
                'type' => 'Registration',
                'status' => 'Unregistered',
                'start_date' => $startDate,
                'end_date' => $endDate,
            ]);
        }

        // Create association clerk
        $clerk = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'Lasmon',
                'last_name' => 'Kapota',
                'gender' => 'Male',
                'district' => 'Blantyre',
                'village' => 'Area 47',
                'phone_number' => '0881234570',
                'password' => bcrypt('lasmon123'),
            ]
        );
        $clerk->assignRole('association clerk');

        // Create association manager
        $manager = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'Moffat',
                'last_name' => 'Kaunda',
                'gender' => 'Male',
                'district' => 'Blantyre',
                'village' => 'Area 3',
                'phone_number' => '0991234570',
                'password' => bcrypt('moffat123'),
            ]
        );
        $manager->assignRole('association manager');

        /* User::factory()->create([
             'name' => 'Test User',
             'email' => '<EMAIL>',
         ]);*/

        // No additional seeders to call
    }
}
