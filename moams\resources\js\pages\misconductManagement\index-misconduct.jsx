import { Head, Link, router } from '@inertiajs/react';
import { navigateToLogicalParent } from '@/utils/navigation';
import { Search, AlertTriangle, Filter, ChevronDown, ChevronLeft, ChevronRight, User, CheckCircle, XCircle, Eye, BarChart3, Download } from 'lucide-react';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

import { usePage } from '@inertiajs/react';
import { route } from 'ziggy-js';


export default function MisconductIndex({ misconducts }) {
    const { userRoles } = usePage().props;
    const [searchTerm, setSearchTerm] = useState('');
    const [severityFilter, setSeverityFilter] = useState('all');
    const [resolutionFilter, setResolutionFilter] = useState('all');
    const [sortBy, setSortBy] = useState('offense_date');
    const [sortDirection, setSortDirection] = useState('desc');
    const [downloadLoading, setDownloadLoading] = useState(false);

    // User permissions
    const isClerk = userRoles && userRoles.includes('association clerk');
    const isManager = userRoles && userRoles.includes('association manager');
    const isAdmin = userRoles && userRoles.includes('system admin');
    const isMinibusOwner = userRoles && userRoles.includes('minibus owner');
    const canCreate = isMinibusOwner;

    const canResolve = isAdmin || isClerk || isManager;

    // Filter and sort misconducts
    const filteredMisconducts = misconducts.data.filter(misconduct => {
        const term = searchTerm.toLowerCase();
        const matchesSearch = (
            misconduct.name.toLowerCase().includes(term) ||
            misconduct.description?.toLowerCase().includes(term) ||
            misconduct.offender?.first_name?.toLowerCase().includes(term) ||
            misconduct.offender?.last_name?.toLowerCase().includes(term)
        );

        const matchesSeverity = severityFilter === 'all' || misconduct.severity === severityFilter;
        const matchesResolution = resolutionFilter === 'all' || misconduct.resolution_status === resolutionFilter;

        return matchesSearch && matchesSeverity && matchesResolution;
    }).sort((a, b) => {
        let aValue = a[sortBy];
        let bValue = b[sortBy];

        if (sortBy === 'offense_date') {
            aValue = new Date(aValue);
            bValue = new Date(bValue);
        }

        if (sortDirection === 'asc') {
            return aValue > bValue ? 1 : -1;
        } else {
            return aValue < bValue ? 1 : -1;
        }
    });

    const handleSort = (column) => {
        if (sortBy === column) {
            setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
        } else {
            setSortBy(column);
            setSortDirection('asc');
        }
    };

    const handleResolutionToggle = async (misconduct) => {
        const newStatus = misconduct.resolution_status === 'resolved' ? 'unresolved' : 'resolved';

        try {
            await router.patch(`/misconducts/${misconduct.id}/resolution`, {
                resolution_status: newStatus,
                resolution_notes: newStatus === 'resolved' ? 'Marked as resolved via quick action' : null,
            });
        } catch (error) {
            console.error('Error updating resolution status:', error);
        }
    };

    const getSeverityBadge = (severity) => {
        const colors = {
            low: 'bg-green-100 text-green-800',
            medium: 'bg-orange-100 text-orange-800',
            high: 'bg-red-100 text-red-800'
        };
        return (
            <Badge className={colors[severity] || 'bg-gray-100 text-gray-800'}>
                {severity?.toUpperCase()}
            </Badge>
        );
    };

    const getResolutionBadge = (status) => {
        return (
            <Badge className={status === 'resolved' ? 'bg-green-100 text-green-800' : 'bg-orange-100 text-orange-800'}>
                {status === 'resolved' ? 'RESOLVED' : 'UNRESOLVED'}
            </Badge>
        );
    };

    const breadcrumbs = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Misconduct Management', href: '/misconducts' },
    ];

    // Download function similar to analytics page
    const handleDownload = async () => {
        try {
            setDownloadLoading(true);

            // Create a temporary link to download the PDF
            const link = document.createElement('a');
            const params = new URLSearchParams({
                view: 'monthly',
                month: new Date().toISOString().slice(0, 7) // Current month in YYYY-MM format
            });
            link.href = `/misconduct-analytics/report?${params.toString()}`;
            link.download = `misconduct-report-${new Date().toISOString().slice(0, 7)}.pdf`;

            // Trigger download without opening new window
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

        } catch (error) {
            console.error('Error downloading PDF:', error);
        } finally {
            setDownloadLoading(false);
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Misconduct Management" />

            <div className="w-full max-w-full overflow-hidden">
                <div className="container mx-auto px-4 py-8">
                    {/* Header section */}
                    <div className="flex flex-col gap-4 mb-6">
                        <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
                            <button
                                className="flex items-center px-3 py-1 rounded hover:bg-gray-100 border border-gray-200 w-fit"
                                onClick={() => navigateToLogicalParent(breadcrumbs)}
                            >
                                <ChevronLeft className="h-4 w-4 mr-1" /> Back
                            </button>
                            <div className="flex-1 sm:flex-none">
                                <span className="text-muted-foreground text-sm sm:text-base font-medium break-words">
                                    {isMinibusOwner
                                        ? 'Track and manage misconduct reports for your drivers.'
                                        : 'Track and manage driver misconducts.'
                                    }
                                </span>
                            </div>
                        </div>

                        {/* Action buttons row */}
                        <div className="flex flex-wrap items-center gap-2 justify-start sm:justify-end">
                            {(isAdmin || isClerk || isManager) && (
                                <>
                                    <Button
                                        variant="outline"
                                        onClick={handleDownload}
                                        disabled={downloadLoading}
                                        className="w-fit flex items-center gap-2 px-4 py-2"
                                    >
                                        <Download className="h-4 w-4" />
                                        {downloadLoading ? 'Downloading...' : 'Download Report'}
                                    </Button>
                                    <Button
                                        variant="outline"
                                        onClick={() => router.visit('/misconduct-analytics')}
                                        className="w-fit flex items-center gap-2 px-4 py-2"
                                    >
                                        <BarChart3 className="h-4 w-4" />
                                        Analytics
                                    </Button>
                                </>
                            )}
                            {canCreate && (
                                <Link href={route('misconducts.create')}>
                                    <Button
                                        variant="outline"
                                        className="w-fit flex items-center gap-2 border-red-600 text-red-600 hover:bg-red-50 px-4 py-2"
                                    >
                                        <AlertTriangle className="h-4 w-4" />
                                        Report Misconduct
                                    </Button>
                                </Link>
                            )}
                        </div>
                    </div>

                    {/* Search and Filter section */}
                    <Card className="mb-6 w-full">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Search className="h-5 w-5" />
                                Search & Filter
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                                <div>
                                    <Label htmlFor="search">Search Misconducts</Label>
                                    <div className="relative">
                                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                        <Input
                                            id="search"
                                            placeholder="Search misconducts..."
                                            value={searchTerm}
                                            onChange={e => setSearchTerm(e.target.value)}
                                            className="pl-10"
                                        />
                                    </div>
                                </div>
                                <div>
                                    <Label htmlFor="severity">Severity</Label>
                                    <Select value={severityFilter} onValueChange={setSeverityFilter}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="All Severities" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">All Severities</SelectItem>
                                            <SelectItem value="low">Low</SelectItem>
                                            <SelectItem value="medium">Medium</SelectItem>
                                            <SelectItem value="high">High</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                                <div>
                                    <Label htmlFor="resolution">Resolution Status</Label>
                                    <Select value={resolutionFilter} onValueChange={setResolutionFilter}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="All Statuses" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">All Statuses</SelectItem>
                                            <SelectItem value="unresolved">Unresolved</SelectItem>
                                            <SelectItem value="resolved">Resolved</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                                <div className="flex items-end">
                                    <Button
                                        variant="outline"
                                        onClick={() => {
                                            setSearchTerm('');
                                            setSeverityFilter('all');
                                            setResolutionFilter('all');
                                        }}
                                        className="w-full"
                                    >
                                        <Filter className="h-4 w-4 mr-2" />
                                        Clear Filters
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Misconducts Table Container - Fixed width that fits screen */}
                    <div
                        className="w-full border-2 border-blue-200 rounded-lg"
                        style={{
                            maxWidth: 'calc(100vw - 2rem)',
                            overflow: 'hidden'
                        }}
                    >
                        <Card className="w-full border-0">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <AlertTriangle className="h-5 w-5" />
                                    {isMinibusOwner
                                        ? `My Drivers' Misconducts (${misconducts.total})`
                                        : `Driver Misconducts (${misconducts.total})`
                                    }
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="p-0">
                                {filteredMisconducts.length === 0 ? (
                                    <div className="text-center py-8 px-6">
                                        <AlertTriangle className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                                        <h3 className="text-lg font-medium mb-2">No misconducts found</h3>
                                        <p className="text-muted-foreground mb-4">
                                            {searchTerm || severityFilter !== 'all' || resolutionFilter !== 'all'
                                                ? 'Try adjusting your search or filter criteria.'
                                                : 'No driver misconducts have been reported yet.'
                                            }
                                        </p>
                                        {canCreate && !searchTerm && severityFilter === 'all' && resolutionFilter === 'all' && (
                                            <Link href={route('misconducts.create')}>
                                                <Button
                                                    variant="outline"
                                                    className="border-red-600 text-red-600 hover:bg-red-50"
                                                >
                                                    <AlertTriangle className="h-4 w-4 mr-2" />
                                                    Report First Misconduct
                                                </Button>
                                            </Link>
                                        )}
                                    </div>
                                ) : (
                                    <div className="overflow-x-auto">
                                        <TooltipProvider>
                                            <Table className="w-full min-w-[800px]">
                                                <TableHeader>
                                                    <TableRow>
                                                        <TableHead
                                                            className="cursor-pointer hover:bg-muted/50"
                                                            onClick={() => handleSort('name')}
                                                        >
                                                            <div className="flex items-center gap-2">
                                                                Misconduct Type
                                                                {sortBy === 'name' && (
                                                                    <ChevronDown className={`h-4 w-4 transition-transform ${sortDirection === 'desc' ? 'rotate-180' : ''
                                                                        }`} />
                                                                )}
                                                            </div>
                                                        </TableHead>
                                                        <TableHead>Driver</TableHead>
                                                        <TableHead>Trust Score</TableHead>
                                                        <TableHead
                                                            className="cursor-pointer hover:bg-muted/50"
                                                            onClick={() => handleSort('offense_date')}
                                                        >
                                                            <div className="flex items-center gap-2">
                                                                Date
                                                                {sortBy === 'offense_date' && (
                                                                    <ChevronDown className={`h-4 w-4 transition-transform ${sortDirection === 'desc' ? 'rotate-180' : ''
                                                                        }`} />
                                                                )}
                                                            </div>
                                                        </TableHead>
                                                        <TableHead>Severity</TableHead>
                                                        <TableHead>Status</TableHead>
                                                        <TableHead>Actions</TableHead>
                                                    </TableRow>
                                                </TableHeader>
                                                <TableBody>
                                                    {filteredMisconducts.map((misconduct) => (
                                                        <TableRow key={misconduct.id}>
                                                            <TableCell className="font-medium">
                                                                {misconduct.name}
                                                            </TableCell>
                                                            <TableCell>
                                                                <div className="flex items-center gap-2">
                                                                    <User className="h-4 w-4 text-gray-500" />
                                                                    {misconduct.offender?.first_name} {misconduct.offender?.last_name}
                                                                </div>
                                                            </TableCell>
                                                            <TableCell>
                                                                {misconduct.offender?.trust_score !== undefined && (
                                                                    <Badge className={
                                                                        misconduct.offender.trust_score >= 80 ? 'bg-green-100 text-green-800' :
                                                                            misconduct.offender.trust_score >= 60 ? 'bg-orange-100 text-orange-800' :
                                                                                'bg-red-100 text-red-800'
                                                                    }>
                                                                        {misconduct.offender.trust_score}%
                                                                    </Badge>
                                                                )}
                                                            </TableCell>
                                                            <TableCell>
                                                                {new Date(misconduct.offense_date).toLocaleDateString()}
                                                            </TableCell>
                                                            <TableCell>
                                                                {getSeverityBadge(misconduct.severity)}
                                                            </TableCell>
                                                            <TableCell>
                                                                {getResolutionBadge(misconduct.resolution_status)}
                                                            </TableCell>
                                                            <TableCell>
                                                                <div className="flex items-center gap-2">
                                                                    <Tooltip>
                                                                        <TooltipTrigger asChild>
                                                                            <Button
                                                                                variant="outline"
                                                                                size="sm"
                                                                                onClick={() => router.visit(route('misconducts.show', misconduct.id))}
                                                                            >
                                                                                <Eye className="h-4 w-4" />
                                                                            </Button>
                                                                        </TooltipTrigger>
                                                                        <TooltipContent>
                                                                            <p>View Details</p>
                                                                        </TooltipContent>
                                                                    </Tooltip>

                                                                    {canResolve && (
                                                                        <Tooltip>
                                                                            <TooltipTrigger asChild>
                                                                                <Button
                                                                                    variant={misconduct.resolution_status === 'resolved' ? 'outline' : 'default'}
                                                                                    size="sm"
                                                                                    onClick={() => handleResolutionToggle(misconduct)}
                                                                                    className={misconduct.resolution_status === 'resolved'
                                                                                        ? 'text-orange-700 border-orange-300 hover:bg-orange-50'
                                                                                        : 'bg-green-600 text-white hover:bg-green-700'
                                                                                    }
                                                                                >
                                                                                    {misconduct.resolution_status === 'resolved' ? (
                                                                                        <XCircle className="h-4 w-4" />
                                                                                    ) : (
                                                                                        <CheckCircle className="h-4 w-4" />
                                                                                    )}
                                                                                </Button>
                                                                            </TooltipTrigger>
                                                                            <TooltipContent>
                                                                                <p>{misconduct.resolution_status === 'resolved' ? 'Mark as Unresolved' : 'Mark as Resolved'}</p>
                                                                            </TooltipContent>
                                                                        </Tooltip>
                                                                    )}
                                                                </div>
                                                            </TableCell>
                                                        </TableRow>
                                                    ))}
                                                </TableBody>
                                            </Table>
                                        </TooltipProvider>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>

                    {/* Pagination */}
                    {misconducts && misconducts.total > 0 && (
                        <div className="mt-6 w-full">
                            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 px-4 py-4 border-t bg-gray-50/50 rounded-b-lg">
                                <div className="text-sm text-gray-700 text-center sm:text-left">
                                    Showing {misconducts.from} to {misconducts.to} of {misconducts.total} results
                                </div>
                                {misconducts.last_page > 1 && (
                                    <div className="flex items-center justify-center sm:justify-end space-x-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => router.get(misconducts.prev_page_url)}
                                            disabled={!misconducts.prev_page_url}
                                            className="flex items-center gap-1"
                                        >
                                            <ChevronLeft className="h-4 w-4" />
                                            <span className="hidden sm:inline">Previous</span>
                                            <span className="sm:hidden">Prev</span>
                                        </Button>

                                        <span className="text-sm text-gray-600 px-2">
                                            {misconducts.current_page} of {misconducts.last_page}
                                        </span>

                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => router.get(misconducts.next_page_url)}
                                            disabled={!misconducts.next_page_url}
                                            className="flex items-center gap-1"
                                        >
                                            <span className="hidden sm:inline">Next</span>
                                            <span className="sm:hidden">Next</span>
                                            <ChevronRight className="h-4 w-4" />
                                        </Button>
                                    </div>
                                )}
                            </div>
                        </div>
                    )}



                </div>
            </div >
        </AppLayout >
    );
}
