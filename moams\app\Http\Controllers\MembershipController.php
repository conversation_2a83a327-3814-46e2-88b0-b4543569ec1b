<?php

namespace App\Http\Controllers;

use App\Models\Membership;
use Illuminate\Http\Request;

class MembershipController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = \App\Models\User::role('minibus owner');

        // Handle search
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                    ->orWhere('last_name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%")
                    ->orWhere('phone_number', 'like', "%{$search}%")
                    ->orWhere('district', 'like', "%{$search}%")
                    ->orWhere('village', 'like', "%{$search}%")
                    ->orWhereRaw("CONCAT(first_name, ' ', last_name) like ?", ["%{$search}%"]);
            });
        }

        // Handle status filter
        if ($request->has('status') && $request->status !== 'all') {
            if ($request->status === 'active') {
                $query->whereNull('archived_at');
            } elseif ($request->status === 'archived') {
                $query->whereNotNull('archived_at');
            }
        }

        $minibusOwners = $query->withCount([
            'memberships as unpaid_memberships_count' => function ($query) {
                $query->whereIn('status', ['Unregistered', 'Need affiliation fee', 'Affiliation fee not paid']);
            }
        ])->orderBy('created_at', 'desc')->paginate(10);

        return \Inertia\Inertia::render('MembershipManagement/index-membership', [
            'minibusOwners' => $minibusOwners,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(Membership $membership)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Membership $membership)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Membership $membership)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Membership $membership)
    {
        $paidStatuses = ['Registered', 'Affiliation fee paid'];
        if (!in_array($membership->status, $paidStatuses)) {
            return back()->with('error', 'Only paid memberships can be deleted.');
        }
        if ($membership->type === 'Registration') {
            return back()->with('error', 'Registration memberships cannot be deleted.');
        }
        try {
            $membership->delete();
            return back()->with('success', 'Membership deleted successfully.');
        } catch (\Exception $e) {
            return back()->with('error', 'Failed to delete membership. Please try again.');
        }
    }
}