import React from 'react';
import { usePage, router } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';

export default function ImageViewer({ fileUrl, fileType }) {
    // fileUrl: relative path to the file in storage (e.g. /storage/national_ids/xyz.jpg)
    // fileType: 'image' or 'pdf'
    return (
        <AppLayout breadcrumbs={[{ title: 'Back', href: '#' }]}> {/* Optionally add breadcrumbs */}
            <div className="flex flex-col items-center justify-center min-h-[60vh] p-8">
                <Button variant="outline" className="mb-6" onClick={() => window.history.back()}>
                    &larr; Back
                </Button>
                {fileType === 'pdf' ? (
                    <iframe
                        src={fileUrl}
                        title="National ID PDF"
                        className="w-full max-w-2xl h-[80vh] border rounded shadow"
                    />
                ) : (
                    <img
                        src={fileUrl}
                        alt="National ID"
                        className="max-w-full max-h-[80vh] border rounded shadow"
                    />
                )}
            </div>
        </AppLayout>
    );
} 