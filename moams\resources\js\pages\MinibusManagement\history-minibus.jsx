import React, { useState } from 'react';
import { <PERSON> } from '@inertiajs/react';
import { navigateToLogicalParent } from '@/utils/navigation';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Bus, User, BadgeCheck, Users } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

export default function HistoryMinibus({ minibus, history, userRole }) {
    const [displayCount, setDisplayCount] = useState(2);
    const displayedHistory = history.slice(0, displayCount);
    const hasMore = displayedHistory.length < history.length;

    const breadcrumbs = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: userRole === 'minibus owner' ? 'My Minibuses' : 'Minibus Management', href: '/minibuses' },
        { title: minibus.number_plate, href: `/minibuses/${minibus.id}` },
        { title: 'Ownership History', href: '#' },
    ];

    const formatDate = (dateString) => {
        if (!dateString) return 'N/A';
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        });
    };

    const getTransferTypeBadge = (type) => {
        switch (type) {
            case 'internal':
                return <Badge variant="default" className="bg-blue-600">Internal</Badge>;
            case 'external':
                return <Badge variant="destructive">External</Badge>;
            default:
                return <Badge variant="outline">Unknown</Badge>;
        }
    };

    const getStatusBadge = (status) => {
        switch (status) {
            case 'completed':
                return <Badge variant="default" className="bg-green-600">Completed</Badge>;
            case 'pending':
                return <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-300">Pending</Badge>;
            default:
                return <Badge variant="outline">Unknown</Badge>;
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <div className="p-2 sm:p-4 md:p-8 ">
                <div className="flex flex-col sm:flex-row gap-2 sm:gap-4 items-center sm:items-center sm:justify-between mb-4 sm:mb-6">
                    <Button variant="outline" onClick={() => navigateToLogicalParent(breadcrumbs)} className="w-full sm:w-auto">
                        <ArrowLeft className="h-4 w-4 mr-2" />
                        Back
                    </Button>
                    <h1 className="text-lg sm:text-xl md:text-2xl font-bold text-center sm:text-left">Ownership History</h1>
                </div>
                <div className="w-full max-w-3xl mx-auto">
                    <div className="mb-8">
                        <h3 className="text-base sm:text-lg md:text-xl font-semibold mb-5 text-center flex items-center justify-center"><Bus className="inline-block text-gray-500 mr-2 h-5 w-5 align-text-bottom" />{minibus.number_plate} Ownership History</h3>
                    </div>
                    {history.length === 0 ? (
                        <div className="text-center py-8">
                            <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                            <div className="text-lg font-medium text-gray-700 mb-1">No ownership history found.</div>
                            <div className="text-gray-500 text-sm">Ownership records will appear here as the minibus changes owners.</div>
                        </div>
                    ) : (
                        <>
                            <div className="space-y-6">
                                {displayedHistory.map((entry, idx) => (
                                    <Card key={idx} className="shadow-md">
                                        <CardHeader className="pb-3">
                                            <CardTitle className="flex items-center justify-between text-base">
                                                <span className="flex items-center gap-2">
                                                    {getTransferTypeBadge(entry.transfer_type)}
                                                    <span>
                                                        {entry.transfer_type === 'internal' && 'Internal Transfer'}
                                                        {entry.transfer_type === 'external' && 'External Transfer'}
                                                    </span>
                                                </span>
                                                <span className="text-sm text-gray-500">
                                                    {formatDate(entry.created_at)}
                                                </span>
                                            </CardTitle>
                                        </CardHeader>
                                        <CardContent className="pt-0 space-y-3">
                                            {/* Previous Owner */}
                                            <div className="border border-red-200 rounded p-3 bg-red-50">
                                                <label className="text-sm font-semibold text-red-700 block mb-1">Previous Owner</label>
                                                <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-sm">
                                                    <div>
                                                        <span className="font-medium">Name:</span> {entry.previous_owner?.first_name} {entry.previous_owner?.last_name}
                                                    </div>
                                                    <div>
                                                        <span className="font-medium">Email:</span> {entry.previous_owner?.email}
                                                    </div>
                                                    <div>
                                                        <span className="font-medium">Phone:</span> {entry.previous_owner?.phone_number}
                                                    </div>
                                                    <div>
                                                        <span className="font-medium">District:</span> {entry.previous_owner?.district}
                                                    </div>
                                                    <div>
                                                        <span className="font-medium">Village:</span> {entry.previous_owner?.village}
                                                    </div>
                                                    <div>
                                                        <span className="font-medium">Gender:</span> {entry.previous_owner?.gender}
                                                    </div>
                                                </div>
                                            </div>
                                            {/* New Owner */}
                                            <div className="border border-green-200 rounded p-3 bg-green-50">
                                                <label className="text-sm font-semibold text-green-700 block mb-1">New Owner</label>
                                                {entry.transfer_type === 'external' ? (
                                                    <span className="text-gray-400">Non-member</span>
                                                ) : (
                                                    <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-sm">
                                                        <div>
                                                            <span className="font-medium">Name:</span> {entry.new_owner?.first_name} {entry.new_owner?.last_name}
                                                        </div>
                                                        <div>
                                                            <span className="font-medium">Email:</span> {entry.new_owner?.email}
                                                        </div>
                                                        <div>
                                                            <span className="font-medium">Phone:</span> {entry.new_owner?.phone_number}
                                                        </div>
                                                        <div>
                                                            <span className="font-medium">District:</span> {entry.new_owner?.district}
                                                        </div>
                                                        <div>
                                                            <span className="font-medium">Village:</span> {entry.new_owner?.village}
                                                        </div>
                                                        <div>
                                                            <span className="font-medium">Gender:</span> {entry.new_owner?.gender}
                                                        </div>
                                                    </div>
                                                )}
                                            </div>
                                            {/* Transfer Date */}
                                            <div>
                                                <label className="text-sm font-medium text-gray-500">Transferred At</label>
                                                <p className="text-sm">{formatDate(entry.created_at)}</p>
                                            </div>
                                        </CardContent>
                                    </Card>
                                ))}
                            </div>
                            <div className="flex justify-center gap-4 mt-6">
                                {hasMore && (
                                    <Button variant="outline" onClick={() => setDisplayCount(c => c + 2)}>
                                        Load More
                                    </Button>
                                )}
                                {displayCount > 2 && (
                                    <Button variant="outline" onClick={() => setDisplayCount(c => Math.max(2, c - 2))}>
                                        Load Less
                                    </Button>
                                )}
                            </div>
                        </>
                    )}
                </div>
            </div>
        </AppLayout>
    );
} 