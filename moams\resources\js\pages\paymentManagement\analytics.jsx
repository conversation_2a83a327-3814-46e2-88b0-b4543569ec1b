import React, { useState } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardHeader, CardTitle, CardContent } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import { BarChart3, Download, DollarSign, CreditCard, Users, TrendingUp, Calendar, FileText, ChevronLeft } from 'lucide-react';
import { router } from '@inertiajs/react';
import { navigateToLogicalParent } from '@/utils/navigation';

export default function PaymentAnalytics({
    summary = {},
    trends = [],
    feeTypeBreakdown = [],
    paymentMethodBreakdown = [],
    topPayingMembers = [],
    selectedPeriod = 'monthly',
    selectedYear = new Date().getFullYear(),
    selectedMonth = new Date().getMonth() + 1,
    userRoles = []
}) {
    const [period, setPeriod] = useState(selectedPeriod);
    const [year, setYear] = useState(selectedYear);
    const [month, setMonth] = useState(selectedMonth);
    const [downloadLoading, setDownloadLoading] = useState(false);

    const breadcrumbs = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Payment Analytics', href: '/payment-analytics' },
    ];

    const handlePeriodChange = (newPeriod) => {
        setPeriod(newPeriod);
        const params = new URLSearchParams({
            period: newPeriod,
            year: year,
            ...(newPeriod === 'monthly' ? { month: month } : {})
        });
        router.get(`/payment-analytics?${params.toString()}`);
    };

    const handleYearChange = (newYear) => {
        setYear(newYear);
        const params = new URLSearchParams({
            period: period,
            year: newYear,
            ...(period === 'monthly' ? { month: month } : {})
        });
        router.get(`/payment-analytics?${params.toString()}`);
    };

    const handleMonthChange = (newMonth) => {
        setMonth(newMonth);
        const params = new URLSearchParams({
            period: period,
            year: year,
            month: newMonth
        });
        router.get(`/payment-analytics?${params.toString()}`);
    };

    const handleDownload = async () => {
        try {
            setDownloadLoading(true);

            const params = new URLSearchParams({
                period: period,
                year: parseInt(year) || new Date().getFullYear(),
                ...(period === 'monthly' ? { month: parseInt(month) || new Date().getMonth() + 1 } : {})
            });

            // Use fetch to check for errors before downloading
            const response = await fetch(`/payment-analytics/report?${params.toString()}`, {
                method: 'GET',
                headers: {
                    'Accept': 'application/pdf',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                credentials: 'same-origin', // Include cookies for authentication
            });

            if (!response.ok) {
                let errorMessage = 'Failed to generate report. Please try again or contact support.';
                try {
                    const errorData = await response.json();
                    if (errorData.error) {
                        errorMessage = errorData.error;
                    }
                } catch (e) {
                    // If response is not JSON, try to get text
                    try {
                        const errorText = await response.text();
                        if (errorText.includes('Unauthenticated')) {
                            errorMessage = 'Please log in again to download the report.';
                        } else if (errorText.includes('403') || errorText.includes('Access denied')) {
                            errorMessage = 'You do not have permission to download reports.';
                        }
                    } catch (textError) {
                        console.error('Error parsing response:', textError);
                    }
                }
                console.error('Server error:', response.status, errorMessage);
                alert(errorMessage);
                return;
            }

            // Create blob and download
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `financial-report-${period}-${year}${period === 'monthly' ? `-${month}` : ''}.pdf`;

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // Clean up the blob URL
            window.URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Error downloading PDF:', error);
            alert('Failed to download report. Please check your internet connection and try again.');
        } finally {
            setDownloadLoading(false);
        }
    };

    const formatCurrency = (amount) => {
        const numAmount = Number(amount) || 0;
        return `MWK ${numAmount.toLocaleString()}`;
    };

    const currentYear = new Date().getFullYear();
    const years = Array.from({ length: 5 }, (_, i) => currentYear - i);
    const months = [
        { value: 1, label: 'January' },
        { value: 2, label: 'February' },
        { value: 3, label: 'March' },
        { value: 4, label: 'April' },
        { value: 5, label: 'May' },
        { value: 6, label: 'June' },
        { value: 7, label: 'July' },
        { value: 8, label: 'August' },
        { value: 9, label: 'September' },
        { value: 10, label: 'October' },
        { value: 11, label: 'November' },
        { value: 12, label: 'December' },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <div className="max-w-7xl mx-auto p-6">
                {/* Header with back button */}
                <div className="flex flex-col gap-4 mb-6">
                    <div className="flex items-center gap-2">
                        <button
                            className="flex items-center px-3 py-1 rounded hover:bg-gray-100 border border-gray-200"
                            onClick={() => navigateToLogicalParent(breadcrumbs)}
                        >
                            <ChevronLeft className="h-4 w-4 mr-1" /> Back
                        </button>
                    </div>

                    <div className="flex flex-col sm:flex-row sm:items-center gap-4 justify-between">
                        <div>
                            <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                                <BarChart3 className="h-6 w-6 text-blue-600" />
                                Payment Analytics
                            </h1>
                            <p className="text-gray-600 mt-1">Financial insights and payment trends</p>
                        </div>

                        <Button
                            onClick={handleDownload}
                            disabled={downloadLoading}
                            className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2"
                        >
                            <Download className="h-4 w-4" />
                            {downloadLoading ? 'Generating...' : 'Download Report'}
                        </Button>
                    </div>
                </div>

                {/* Filters */}
                <Card className="mb-6">
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Calendar className="h-5 w-5" />
                            Report Filters
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flex flex-wrap gap-4">
                            <div className="flex flex-col gap-2">
                                <label className="text-sm font-medium">Period</label>
                                <Select value={period} onValueChange={handlePeriodChange}>
                                    <SelectTrigger className="w-40">
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="daily">Daily</SelectItem>
                                        <SelectItem value="weekly">Weekly</SelectItem>
                                        <SelectItem value="monthly">Monthly</SelectItem>
                                        <SelectItem value="annually">Annually</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>

                            <div className="flex flex-col gap-2">
                                <label className="text-sm font-medium">Year</label>
                                <Select value={year.toString()} onValueChange={(value) => handleYearChange(parseInt(value))}>
                                    <SelectTrigger className="w-32">
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {years.map(y => (
                                            <SelectItem key={y} value={y.toString()}>{y}</SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>

                            {period === 'monthly' && (
                                <div className="flex flex-col gap-2">
                                    <label className="text-sm font-medium">Month</label>
                                    <Select value={month.toString()} onValueChange={(value) => handleMonthChange(parseInt(value))}>
                                        <SelectTrigger className="w-40">
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {months.map(m => (
                                                <SelectItem key={m.value} value={m.value.toString()}>{m.label}</SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>

                {/* Summary Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                                    <p className="text-2xl font-bold text-green-600">{formatCurrency(summary.total_revenue || 0)}</p>
                                </div>
                                <DollarSign className="h-8 w-8 text-green-600" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">Total Payments</p>
                                    <p className="text-2xl font-bold text-blue-600">{summary.total_payments || 0}</p>
                                </div>
                                <CreditCard className="h-8 w-8 text-blue-600" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">Registration Fees</p>
                                    <p className="text-2xl font-bold text-purple-600">{formatCurrency(summary.registration_revenue || 0)}</p>
                                </div>
                                <Users className="h-8 w-8 text-purple-600" />
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">Affiliation Fees</p>
                                    <p className="text-2xl font-bold text-orange-600">{formatCurrency(summary.affiliation_revenue || 0)}</p>
                                </div>
                                <TrendingUp className="h-8 w-8 text-orange-600" />
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Fee Type Breakdown */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Fee Type Breakdown</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {feeTypeBreakdown.map((item, index) => (
                                    <div key={index} className="flex justify-between items-center">
                                        <span className="capitalize font-medium">{item.fee_type}</span>
                                        <div className="text-right">
                                            <div className="font-bold">{formatCurrency(item.total_paid || 0)}</div>
                                            <div className="text-sm text-gray-500">{item.count || 0} payments</div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Payment Method Breakdown</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {paymentMethodBreakdown.map((item, index) => (
                                    <div key={index} className="flex justify-between items-center">
                                        <span className="capitalize font-medium">{item.payment_method}</span>
                                        <div className="text-right">
                                            <div className="font-bold">{formatCurrency(item.total_amount || 0)}</div>
                                            <div className="text-sm text-gray-500">{item.count || 0} payments</div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Top Paying Members */}
                <Card>
                    <CardHeader>
                        <CardTitle>Top Paying Members</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="overflow-x-auto">
                            <table className="w-full">
                                <thead>
                                    <tr className="border-b">
                                        <th className="text-left py-2">Member</th>
                                        <th className="text-left py-2">Payments</th>
                                        <th className="text-left py-2">Total Amount</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {topPayingMembers.map((member, index) => (
                                        <tr key={index} className="border-b">
                                            <td className="py-2">
                                                {member.user ? `${member.user.first_name} ${member.user.last_name}` : 'Unknown User'}
                                            </td>
                                            <td className="py-2">{member.payment_count || 0}</td>
                                            <td className="py-2 font-medium">{formatCurrency(member.total_amount || 0)}</td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
