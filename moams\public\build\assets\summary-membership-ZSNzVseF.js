import{r as i,j as e,C as T,c as L,B as p,T as v,d as de,l as me,a as q,b as H,h as xe,e as N,f as G}from"./app-DL-qYY5V.js";import{h as w,A as pe,T as he,b as ue,c as ge,d as fe,C as je}from"./app-layout-YqstQnqE.js";import{n as be}from"./navigation-DAA2N51J.js";import{C as ye}from"./confirm-dialog-B1e93Onq.js";import{D as I,a as O,b as F,c as $,d as E}from"./dialog-CXtul0Wy.js";import{I as Ne}from"./input-Dm4SEXxy.js";import{L as J}from"./label-e3QxUH-L.js";import{I as X}from"./input-error-C6jcuIY6.js";import{C as ve}from"./circle-alert-B36J8eZz.js";import{L as we}from"./loader-circle-D3J3XKS7.js";import{C as Q}from"./chevron-left-DFeVEtK7.js";import{U as Z}from"./users-DNGXY-sJ.js";import"./index-1tPPImJd.js";import"./index-BTzg1GwG.js";import"./app-logo-icon-BnXlkpcX.js";import"./bus-CKTBKI0T.js";import"./dollar-sign-Cx0-nQIX.js";import"./clipboard-list-CxRumT8v.js";function Ce({open:t,onClose:a,membership:g,user:l,amount:f,feeType:j}){const[o,c]=i.useState(f||""),[h,b]=i.useState(!1),[d,n]=i.useState({}),m=parseFloat(f)||0,C=x=>{const r=x.target.value;if(r===""){c("");return}if(!/^\d+$/.test(r))return;parseInt(r,10)<=1e6&&c(r)},U=x=>{var P;x.preventDefault(),b(!0),n({});const r=parseInt(o,10);if(!r||r<m||r>1e6){const u=m.toLocaleString("en-US",{maximumFractionDigits:0});n({amount:`Amount must be between MWK ${u} and MWK 1,000,000`}),b(!1);return}const _={user_id:l.id,membership_id:g.id,amount:r,fee_type:j,payment_method:"ctechpay_card"};fetch(v("payments.ctechpay.initiate"),{method:"POST",headers:{"Content-Type":"application/json","X-CSRF-TOKEN":((P=document.querySelector('meta[name="csrf-token"]'))==null?void 0:P.getAttribute("content"))||"",Accept:"application/json"},body:JSON.stringify(_)}).then(async u=>{const y=await u.json();u.ok&&y.success?window.location.href=y.payment_url:(y.errors?n(y.errors):n({error:y.error||"Payment initiation failed"}),b(!1))}).catch(u=>{console.error("Payment initiation error:",u),n({error:"Network error. Please try again."}),b(!1)})},S=()=>{h||(n({}),a())};return e.jsx(I,{open:t,onOpenChange:S,children:e.jsxs(O,{className:"sm:max-w-md",children:[e.jsxs(F,{children:[e.jsxs($,{className:"flex items-center gap-2",children:[e.jsx(w,{className:"h-5 w-5"}),"Online Payment"]}),e.jsxs(E,{children:["Pay your ",j," fee securely using Ctechpay"]})]}),e.jsxs("form",{onSubmit:U,className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(J,{htmlFor:"amount",children:"Amount (MWK)"}),e.jsx(Ne,{id:"amount",type:"number",value:o,onChange:C,placeholder:`Enter amount (min: ${m.toLocaleString("en-US",{maximumFractionDigits:0})})`,min:"1",max:"1000000",step:"1",required:!0,disabled:h,className:"[appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"}),e.jsxs("div",{className:"text-xs text-gray-500",children:["Minimum amount: MWK ",m.toLocaleString("en-US",{maximumFractionDigits:0})]}),o&&parseInt(o,10)<m&&e.jsx("div",{className:"text-xs text-amber-600",children:"The amount is below minimum fee."}),e.jsx(X,{message:d.amount})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx(J,{children:"Payment Method"}),e.jsx("div",{className:"grid grid-cols-1 gap-3",children:e.jsx(T,{className:"ring-2 ring-blue-500 bg-blue-50",children:e.jsx(L,{className:"p-4",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-4 h-4 rounded-full border-2 border-blue-500 bg-blue-500",children:e.jsx("div",{className:"w-2 h-2 bg-white rounded-full mx-auto mt-0.5"})}),e.jsx(w,{className:"h-5 w-5 text-blue-600"}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:"Card Payment"}),e.jsx("div",{className:"text-sm text-gray-500",children:"Pay with Visa, Mastercard, or local cards"})]})]})})})}),e.jsx(X,{message:d.payment_method})]}),d.error&&e.jsxs("div",{className:"flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg",children:[e.jsx(ve,{className:"h-4 w-4 text-red-600"}),e.jsx("span",{className:"text-red-700 text-sm",children:d.error})]}),e.jsxs("div",{className:"flex justify-end gap-3 pt-4",children:[e.jsx(p,{type:"button",variant:"outline",onClick:S,disabled:h,children:"Cancel"}),e.jsx(p,{type:"submit",disabled:h||!o||parseInt(o,10)<m,className:"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed",children:h?e.jsxs(e.Fragment,{children:[e.jsx(we,{className:"h-4 w-4 mr-2 animate-spin"}),"Processing..."]}):e.jsxs(e.Fragment,{children:[e.jsx(w,{className:"h-4 w-4 mr-2"}),"Pay MWK ",o?parseInt(o,10).toLocaleString("en-US"):"0"]})})]})]})]})})}const ee=["Registered","Affiliation fee paid"],se=["Unregistered","Need affiliation fee","Affiliation fee not paid"],Se=({className:t="h-5 w-5"})=>e.jsx("div",{className:`${t} bg-orange-600 rounded flex items-center justify-center text-white font-bold text-xs`,children:"C"});function He({user:t,memberships:a,currentFees:g,flash:l,filters:f,unpaidCount:j}){const{auth:o,userRoles:c}=de().props,h=o.user;c&&c.includes("association clerk");const[b,d]=i.useState(!1);me.useEffect(()=>{l!=null&&l.payment_success&&d(!0)},[l]),c&&c.includes("minibus owner");const n=h.id===t.id,[m,C]=i.useState((f==null?void 0:f.tab)||"all"),[U,S]=i.useState({open:!1,membership:null}),[x,r]=i.useState({open:!1,membership:null}),[_,P]=i.useState(""),[u,y]=i.useState("cash"),[_e,Pe]=i.useState(!1),[De,W]=i.useState(null),[K,R]=i.useState({open:!1,membership:null}),[te,z]=i.useState(!1),[ae,V]=i.useState(!1),[B,D]=i.useState(null),Y=n?[{title:"My Membership",href:"/my-membership"}]:[{title:"Membership Management",href:"/membership-management"},{title:`${t.first_name} ${t.last_name}`,href:`/membership-management/${t.id}`}],ne=s=>{C(s),n||N.get(v("membership.management.summary",t.id),{tab:s},{preserveState:!0,preserveScroll:!0})},M=(a==null?void 0:a.data)||[];M.filter(s=>se.includes(s.status));const ie=()=>R({open:!1,membership:null}),le=async()=>{z(!0),await N.delete(v("memberships.destroy",K.membership.id),{onSuccess:()=>R({open:!1,membership:null}),onFinish:()=>z(!1)})},re=s=>{D(s.id),W(null);const k=s.type.toLowerCase()==="registration"?g.registration:g.affiliation;N.post(v("makePayment.store"),{user_id:t.id,membership_id:s.id,fee_type:s.type.toLowerCase(),amount:k,payment_method:"cash",status:"fulfilled"},{onSuccess:()=>{D(null)},onError:A=>{W(A.amount||A.membership_id||A.fee_type||"Payment failed."),D(null)}})},oe=s=>{const k=s.type.toLowerCase()==="registration"?g.registration:g.affiliation;r({open:!0,membership:s,amount:k,feeType:s.type.toLowerCase()})},ce=[{key:"all",label:`All (${(a==null?void 0:a.total)||0})`},{key:"paid",label:"Paid"},{key:"unpaid",label:"Unpaid"}];return e.jsx(pe,{breadcrumbs:Y,children:e.jsxs("div",{className:"max-w-5xl mx-auto p-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 mb-4 justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("button",{className:"flex items-center px-3 py-1 rounded hover:bg-gray-100 border border-gray-200",onClick:()=>be(Y),children:[e.jsx(Q,{className:"h-4 w-4 mr-1"})," Back"]}),e.jsxs("span",{className:"text-muted-foreground text-base font-medium flex items-center gap-2",children:[e.jsx(Z,{className:"h-5 w-5 text-blue-600"}),n?"My Membership":`${t.first_name} ${t.last_name}`]})]}),e.jsx("div",{className:"flex items-center gap-2",children:t.national_id_path&&c&&(c.includes("association clerk")||c.includes("system admin"))&&e.jsx(p,{variant:"outline",className:"bg-blue-100 text-blue-800 border-blue-300 hover:bg-blue-200",onClick:()=>V(!0),children:"View National ID"})})]}),n&&j>0&&e.jsxs(T,{className:"mb-6 border-orange-200 bg-orange-50",children:[e.jsx(q,{children:e.jsxs(H,{className:"flex items-center gap-2 text-orange-800",children:[e.jsx(xe,{className:"h-5 w-5"}),"Unpaid Memberships"]})}),e.jsx(L,{children:e.jsxs("p",{className:"text-orange-700 mb-4",children:["You have ",j," unpaid membership",j>1?"s":"",". Please settle your outstanding fees to maintain active membership status."]})})]}),!n&&e.jsx("div",{className:"flex gap-2 mb-8 border-b",children:ce.map(s=>e.jsx("button",{className:`px-4 py-2 -mb-px border-b-2 font-medium transition-colors duration-150 ${m===s.key?"border-blue-600 text-blue-600":"border-transparent text-gray-500 hover:text-blue-600"}`,onClick:()=>ne(s.key),children:s.label},s.key))}),e.jsx("div",{className:"overflow-x-auto mb-8",children:e.jsxs("table",{className:"min-w-full w-full bg-white border border-gray-200 rounded-lg",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"bg-gray-100 text-gray-700",children:[e.jsx("th",{className:"px-4 py-2 text-left",children:"Year"}),e.jsx("th",{className:"px-4 py-2 text-left",children:"Type"}),e.jsx("th",{className:"px-4 py-2 text-left",children:"Status"}),e.jsx("th",{className:"px-4 py-2 text-left",children:"Start Date"}),e.jsx("th",{className:"px-4 py-2 text-left",children:"End Date"}),e.jsx("th",{className:"px-4 py-2 text-left",children:"Fee Obligation"}),e.jsx("th",{className:"px-4 py-2 text-left",children:"Actions"})]})}),e.jsx("tbody",{children:M.length===0?e.jsx("tr",{children:e.jsx("td",{colSpan:7,className:"text-center text-gray-500 py-6",children:n?"No unpaid memberships found. All your memberships are up to date!":"No memberships found."})}):M.map(s=>e.jsxs("tr",{className:"border-b hover:bg-gray-50",children:[e.jsx("td",{className:"px-4 py-2",children:new Date(s.start_date).getFullYear()}),e.jsx("td",{className:"px-4 py-2 capitalize",children:s.type}),e.jsx("td",{className:"px-4 py-2",children:e.jsx("span",{className:`inline-block px-2 py-1 rounded text-xs font-semibold ${ee.includes(s.status)?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:s.status})}),e.jsx("td",{className:"px-4 py-2",children:new Date(s.start_date).toLocaleDateString()}),e.jsx("td",{className:"px-4 py-2",children:new Date(s.end_date).toLocaleDateString()}),e.jsx("td",{className:"px-4 py-2",children:ee.includes(s.status)?"Fulfilled":"Not fulfilled"}),e.jsx("td",{className:"px-4 py-2 space-x-2",children:se.includes(s.status)&&e.jsx(e.Fragment,{children:n?e.jsx(he,{children:e.jsx("div",{className:"flex gap-2",children:e.jsxs(ue,{children:[e.jsx(ge,{asChild:!0,children:e.jsxs(p,{size:"sm",variant:"outline",className:"flex items-center justify-center border-orange-600 text-orange-600 hover:bg-orange-50 px-3",onClick:()=>oe(s),children:[e.jsx(Se,{className:"h-4 w-4 mr-1"}),"Pay Online"]})}),e.jsx(fe,{children:e.jsx("p",{children:"Pay with Ctechpay"})})]})})}):e.jsxs(p,{size:"sm",className:"flex items-center justify-center bg-blue-400 hover:bg-blue-500 text-white font-semibold",onClick:()=>re(s),disabled:B===s.id,children:[e.jsx(w,{className:"h-4 w-4 mr-2"}),B===s.id?"Processing...":"Mark Paid"]})})})]},s.id))})]})}),a&&a.total>0&&e.jsx("div",{className:"mt-6 w-full",children:e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 px-4 py-4 border-t bg-gray-50/50 rounded-b-lg",children:[e.jsxs("div",{className:"text-sm text-gray-700 text-center sm:text-left",children:["Showing ",a.from," to ",a.to," of ",a.total," results"]}),a.last_page>1&&e.jsxs("div",{className:"flex items-center justify-center sm:justify-end space-x-2",children:[e.jsxs(p,{variant:"outline",size:"sm",onClick:()=>N.get(a.prev_page_url),disabled:!a.prev_page_url,className:"flex items-center gap-1",children:[e.jsx(Q,{className:"h-4 w-4"}),e.jsx("span",{className:"hidden sm:inline",children:"Previous"}),e.jsx("span",{className:"sm:hidden",children:"Prev"})]}),e.jsxs("span",{className:"text-sm text-gray-600 px-2",children:[a.current_page," of ",a.last_page]}),e.jsxs(p,{variant:"outline",size:"sm",onClick:()=>N.get(a.next_page_url),disabled:!a.next_page_url,className:"flex items-center gap-1",children:[e.jsx("span",{className:"hidden sm:inline",children:"Next"}),e.jsx("span",{className:"sm:hidden",children:"Next"}),e.jsx(je,{className:"h-4 w-4"})]})]})]})}),e.jsx(ye,{open:K.open,title:"Delete Membership",description:"Are you sure you want to delete this paid membership record? This action cannot be undone.",onConfirm:le,onCancel:ie,confirmText:"Delete",cancelText:"Cancel",loading:te}),e.jsx(I,{open:ae,onOpenChange:V,children:e.jsxs(O,{className:"max-w-2xl",children:[e.jsxs(F,{children:[e.jsx($,{children:"National ID"}),e.jsx(E,{children:t.national_id_path&&t.national_id_path.endsWith(".pdf")?"Preview of the uploaded National ID (PDF).":"Preview of the uploaded National ID (image)."})]}),t.national_id_path&&t.national_id_path.endsWith(".pdf")?e.jsx("iframe",{src:`/storage/${t.national_id_path}`,title:"National ID PDF",className:"w-full h-[70vh] border"}):t.national_id_path?e.jsx("img",{src:`/storage/${t.national_id_path}`,alt:"National ID",className:"w-full max-h-[70vh] object-contain"}):null]})}),e.jsx(I,{open:b,onOpenChange:d,children:e.jsxs(O,{className:"sm:max-w-md",children:[e.jsx(F,{children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx(G,{className:"h-8 w-8 text-green-600"})}),e.jsxs("div",{children:[e.jsx($,{className:"text-green-700",children:"Payment Successful!"}),e.jsx(E,{className:"text-green-600",children:"Your payment has been processed successfully"})]})]})}),(l==null?void 0:l.payment_success)&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[e.jsx("p",{className:"text-green-800 font-medium mb-3",children:l.payment_success.message}),e.jsxs("div",{className:"space-y-2 text-sm text-green-700",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"font-medium",children:"Amount:"}),e.jsxs("span",{className:"font-mono",children:["MWK ",Number(l.payment_success.amount).toLocaleString()]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"font-medium",children:"Time:"}),e.jsx("span",{children:new Date(l.payment_success.timestamp).toLocaleString()})]})]})]}),e.jsx("div",{className:"flex justify-end gap-2",children:e.jsxs(p,{onClick:()=>d(!1),className:"bg-green-600 hover:bg-green-700",children:[e.jsx(G,{className:"h-4 w-4 mr-2"}),"Got it!"]})})]})]})}),e.jsx(Ce,{open:x.open,onClose:()=>r({open:!1,membership:null}),membership:x.membership,user:t,amount:x.amount,feeType:x.feeType}),n&&e.jsxs(T,{className:"mb-6 border-blue-200 bg-blue-50",children:[e.jsx(q,{children:e.jsxs(H,{className:"flex items-center gap-2 text-blue-800",children:[e.jsx(Z,{className:"h-5 w-5"}),"Membership Commitment"]})}),e.jsx(L,{children:e.jsx("p",{className:"text-blue-700 mb-2",children:"I commit to abide by the Constitution, rules, and regulations of the Minibus Owners Association of Malawi (MOAM), and to uphold the values and objectives of the Association at all times."})})]})]})})}export{He as default};
