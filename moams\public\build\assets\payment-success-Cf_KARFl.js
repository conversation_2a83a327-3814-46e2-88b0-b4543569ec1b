import{j as e,L as t,T as r,f as o,C as i,a as c,b as d,c as n,B as m}from"./app-DL-qYY5V.js";import{A as x,h}from"./app-layout-YqstQnqE.js";import{D as j}from"./dollar-sign-Cx0-nQIX.js";import{U as f}from"./users-DNGXY-sJ.js";import{C as u}from"./calendar-Bzuvt9Ns.js";import"./index-1tPPImJd.js";import"./index-BTzg1GwG.js";import"./app-logo-icon-BnXlkpcX.js";import"./bus-CKTBKI0T.js";import"./clipboard-list-CxRumT8v.js";function M({payment:s,associationClerk:a}){const l=[{title:"Dashboard",href:"/dashboard"},{title:"My Membership",href:"/my-membership"},{title:"Payment Successful",href:"#"}];return s?e.jsx(x,{title:"Payment Successful",breadcrumbs:l,children:e.jsxs("div",{className:"max-w-2xl mx-auto py-8 px-4",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("div",{className:"flex justify-center mb-4",children:e.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center",children:e.jsx(o,{className:"h-10 w-10 text-green-600"})})}),e.jsx("h1",{className:"text-3xl font-bold text-green-700 mb-2",children:"Payment Successful!"}),e.jsx("p",{className:"text-gray-600",children:"Your payment has been processed successfully"})]}),e.jsxs(i,{className:"mb-6 border-green-200 bg-green-50",children:[e.jsx(c,{children:e.jsxs(d,{className:"flex items-center gap-2 text-green-800",children:[e.jsx(h,{className:"h-5 w-5"}),"Payment Details"]})}),e.jsxs(n,{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(j,{className:"h-5 w-5 text-green-600"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Amount"}),e.jsxs("div",{className:"font-semibold text-green-800",children:["MWK ",parseFloat(s.amount).toLocaleString()]})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(f,{className:"h-5 w-5 text-green-600"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Fee Type"}),e.jsx("div",{className:"font-semibold text-green-800 capitalize",children:s.fee_type})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(h,{className:"h-5 w-5 text-green-600"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Payment Method"}),e.jsx("div",{className:"font-semibold text-green-800",children:s.payment_method==="ctechpay_card"?"Card Payment":s.payment_method})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(u,{className:"h-5 w-5 text-green-600"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Date & Time"}),e.jsx("div",{className:"font-semibold text-green-800",children:new Date(s.paid_at||s.created_at).toLocaleString()})]})]})]}),s.ctechpay_order_reference&&e.jsxs("div",{className:"pt-4 border-t border-green-200",children:[e.jsx("div",{className:"text-sm text-gray-600 mb-2",children:"Transaction Details"}),e.jsxs("div",{className:"space-y-2 text-sm",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Order Reference:"}),e.jsx("span",{className:"font-mono",children:s.ctechpay_order_reference})]}),s.ctechpay_transaction_id&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Transaction ID:"}),e.jsx("span",{className:"font-mono",children:s.ctechpay_transaction_id})]}),s.ctechpay_card_holder_name&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Card Holder:"}),e.jsx("span",{children:s.ctechpay_card_holder_name})]})]})]})]})]}),e.jsxs(i,{className:"mb-6",children:[e.jsx(c,{children:e.jsx(d,{children:"What's Next?"})}),e.jsx(n,{children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5",children:e.jsx("span",{className:"text-blue-600 text-sm font-semibold",children:"1"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:"Confirmation Email"}),e.jsx("div",{className:"text-sm text-gray-600",children:"You will receive a confirmation email shortly with your payment receipt."})]})]}),e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5",children:e.jsx("span",{className:"text-blue-600 text-sm font-semibold",children:"2"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:"Membership Status Updated"}),e.jsx("div",{className:"text-sm text-gray-600",children:"Your membership status has been automatically updated to reflect this payment."})]})]}),e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5",children:e.jsx("span",{className:"text-blue-600 text-sm font-semibold",children:"3"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:"Access Your Dashboard"}),e.jsx("div",{className:"text-sm text-gray-600",children:"You can now access all membership features and services."})]})]})]})})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[e.jsx(m,{asChild:!0,variant:"outline",children:e.jsx(t,{href:r("dashboard"),children:"Return to Dashboard"})}),e.jsx(m,{asChild:!0,className:"bg-green-600 hover:bg-green-700",children:e.jsx(t,{href:r("my.membership"),children:"View My Membership"})})]}),e.jsx("div",{className:"mt-8 text-center text-sm text-gray-500",children:a?e.jsxs("p",{children:["Need help? Contact ",a.first_name," ",a.last_name," at"," ",e.jsx("a",{href:`mailto:${a.email}`,className:"text-blue-600 hover:underline",children:a.email})," ","or call ",a.phone_number]}):e.jsx("p",{children:"Need help? Contact MOAM support for assistance."})})]})}):e.jsx(x,{title:"Payment Not Found",breadcrumbs:l,children:e.jsxs("div",{className:"max-w-2xl mx-auto py-8 px-4 text-center",children:[e.jsx("h1",{className:"text-2xl font-bold text-red-600 mb-4",children:"Payment Not Found"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"The payment information could not be loaded."}),e.jsx(t,{href:r("dashboard"),className:"text-blue-600 hover:underline",children:"Return to Dashboard"})]})})}export{M as default};
