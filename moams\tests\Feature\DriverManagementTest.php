<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Driver;
use App\Models\DriverClearanceRequest;
use App\Models\DriverEmploymentHistory;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Inertia\Testing\AssertableInertia;

class DriverManagementTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->createRoles();
    }

    private function createRoles()
    {
        $roles = ['association clerk', 'minibus owner', 'system admin'];
        foreach ($roles as $role) {
            \Spatie\Permission\Models\Role::firstOrCreate(['name' => $role]);
        }
    }

    public function test_association_clerk_can_view_drivers_index()
    {
        $clerk = User::factory()->create();
        $clerk->assignRole('association clerk');

        $response = $this->actingAs($clerk)->get('/drivers');

        $response->assertStatus(200);
        $response->assertInertia(
            fn(AssertableInertia $page) =>
            $page->component('driverManagement/index-driver')
                ->has('drivers')
                ->has('userRole')
        );
    }

    public function test_minibus_owner_can_view_their_drivers()
    {
        $owner = User::factory()->create();
        $owner->assignRole('minibus owner');

        $driver = Driver::factory()->create([
            'owner_id' => $owner->id,
        ]);

        $response = $this->actingAs($owner)->get('/drivers');

        $response->assertStatus(200);
        $response->assertInertia(
            fn(AssertableInertia $page) =>
            $page->component('driverManagement/index-driver')
                ->has('drivers')
                ->has('userRole')
        );
    }

    public function test_association_clerk_can_create_driver()
    {
        $clerk = User::factory()->create([
            'email_verified_at' => now(),
        ]);
        $clerk->assignRole('association clerk');

        $owner = User::factory()->create();
        $owner->assignRole('minibus owner');

        $driverData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'phone_number' => '**********',
            'license_number' => 'AB123456',
            'district' => 'Lilongwe',
            'village_town' => 'Area 47',
            'owner_id' => $owner->id,
        ];

        $response = $this->actingAs($clerk)
            ->from('/drivers/create')
            ->post('/drivers', $driverData);

        if ($response->status() === 302 && $response->getTargetUrl() === 'http://localhost:8000/drivers/create') {
            dump('Validation errors: ' . json_encode(session('errors')));
            dump('Session error: ' . session('error'));
        }

        $response->assertRedirect('/drivers');

        // Check that the driver was created with the correct data (license number will be hashed)
        $this->assertDatabaseHas('drivers', [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'phone_number' => '**********',
            'owner_id' => $owner->id,
        ]);

        // Verify that the license number was hashed and stored
        $driver = Driver::where('first_name', 'John')
            ->where('last_name', 'Doe')
            ->where('phone_number', '**********')
            ->first();

        $this->assertNotNull($driver);
        $this->assertTrue($driver->verifyLicenseNumber('AB123456'));
    }

    public function test_association_clerk_can_access_create_form()
    {
        $clerk = User::factory()->create([
            'email_verified_at' => now(),
        ]);
        $clerk->assignRole('association clerk');

        $response = $this->actingAs($clerk)->get('/drivers/create');

        $response->assertStatus(200);
        $response->assertInertia(
            fn(AssertableInertia $page) =>
            $page->component('driverManagement/create-driver')
                ->has('userRole')
        );
    }

    public function test_minibus_owner_can_request_driver_clearance()
    {
        $owner = User::factory()->create();
        $owner->assignRole('minibus owner');

        $driver = Driver::factory()->create([
            'owner_id' => $owner->id,
        ]);

        $clearanceData = [
            'reason' => 'Driver is no longer working for us due to relocation.',
        ];

        $response = $this->actingAs($owner)->post("/drivers/{$driver->id}/request-clearance", $clearanceData);

        $response->assertRedirect('/drivers');
        $this->assertDatabaseHas('driver_clearance_requests', [
            'driver_id' => $driver->id,
            'owner_id' => $owner->id,
            'status' => 'pending',
            'reason' => 'Driver is no longer working for us due to relocation.',
        ]);
    }

    public function test_association_clerk_can_process_clearance_request()
    {
        $clerk = User::factory()->create();
        $clerk->assignRole('association clerk');

        $owner = User::factory()->create();
        $owner->assignRole('minibus owner');

        $driver = Driver::factory()->create([
            'owner_id' => $owner->id,
        ]);

        $clearanceRequest = DriverClearanceRequest::create([
            'driver_id' => $driver->id,
            'owner_id' => $owner->id,
            'status' => 'pending',
            'reason' => 'Test clearance request',
        ]);

        $processData = [
            'action' => 'approve',
            'admin_notes' => 'Approved after review',
        ];

        $response = $this->actingAs($clerk)->patch("/drivers/clearance-requests/{$clearanceRequest->id}/process", $processData);

        $response->assertRedirect('/drivers/clearance-requests');

        $this->assertDatabaseHas('driver_clearance_requests', [
            'id' => $clearanceRequest->id,
            'status' => 'approved',
            'processed_by' => $clerk->id,
        ]);

        $this->assertDatabaseHas('drivers', [
            'id' => $driver->id,
            'archived' => true,
        ]);

        $this->assertDatabaseHas('driver_employment_histories', [
            'driver_id' => $driver->id,
            'previous_owner_id' => $owner->id,
            'employment_change_type' => 'cleared',
            'status' => 'completed',
        ]);
    }

    public function test_minibus_owner_cannot_access_other_owners_drivers()
    {
        $owner1 = User::factory()->create();
        $owner1->assignRole('minibus owner');

        $owner2 = User::factory()->create();
        $owner2->assignRole('minibus owner');

        $driver = Driver::factory()->create([
            'owner_id' => $owner1->id,
        ]);

        $response = $this->actingAs($owner2)->get("/drivers/{$driver->id}");

        $response->assertStatus(403);
    }

    public function test_association_clerk_can_view_all_drivers()
    {
        $clerk = User::factory()->create();
        $clerk->assignRole('association clerk');

        $owner1 = User::factory()->create();
        $owner1->assignRole('minibus owner');

        $owner2 = User::factory()->create();
        $owner2->assignRole('minibus owner');

        Driver::factory()->create(['owner_id' => $owner1->id]);
        Driver::factory()->create(['owner_id' => $owner2->id]);

        $response = $this->actingAs($clerk)->get('/drivers');

        $response->assertStatus(200);
        $response->assertInertia(
            fn(AssertableInertia $page) =>
            $page->component('driverManagement/index-driver')
                ->has('drivers')
                ->has('userRole')
        );
    }

    public function test_driver_employment_history_is_tracked()
    {
        $clerk = User::factory()->create([
            'email_verified_at' => now(),
        ]);
        $clerk->assignRole('association clerk');

        $owner1 = User::factory()->create([
            'email_verified_at' => now(),
        ]);
        $owner1->assignRole('minibus owner');

        $owner2 = User::factory()->create([
            'email_verified_at' => now(),
        ]);
        $owner2->assignRole('minibus owner');

        $driver = Driver::factory()->create([
            'owner_id' => $owner1->id,
        ]);

        DriverEmploymentHistory::create([
            'driver_id' => $driver->id,
            'previous_owner_id' => $owner1->id,
            'new_owner_id' => $owner2->id,
            'employment_change_type' => 'rehired',
            'status' => 'completed',
            'employment_start_date' => now(),
        ]);

        $response = $this->actingAs($clerk)->get("/drivers/{$driver->id}/history");

        $response->assertStatus(200);
        $response->assertInertia(
            fn(AssertableInertia $page) =>
            $page->component('driverManagement/history-driver')
                ->has('history')
        );
    }
}
