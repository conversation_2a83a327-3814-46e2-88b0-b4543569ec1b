<?php

namespace App\Http\Controllers;

use App\Models\Payment;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Barryvdh\DomPDF\Facade\Pdf;

class PaymentAnalyticsController extends Controller
{
    /**
     * Display payment analytics dashboard
     */
    public function index(Request $request)
    {
        $user = auth()->user();

        // Only admins and clerks can access analytics
        if (!$user->hasAnyRole(['system admin', 'association clerk'])) {
            abort(403, 'Access denied. Only administrators and clerks can view payment analytics.');
        }

        $period = $request->get('period', 'monthly');
        $year = (int) $request->get('year', now()->year);
        $month = (int) $request->get('month', now()->month);

        // Get summary statistics
        $summary = $this->getSummaryStatistics($period, $year, $month);

        // Get trends data
        $trends = $this->getTrends($period, $year, $month);

        // Get fee type breakdown
        $feeTypeBreakdown = $this->getFeeTypeBreakdown($period, $year, $month);

        // Get payment method breakdown
        $paymentMethodBreakdown = $this->getPaymentMethodBreakdown($period, $year, $month);

        // Get top paying members
        $topPayingMembers = $this->getTopPayingMembers($period, $year, $month);

        return Inertia::render('paymentManagement/analytics', [
            'summary' => $summary ?? [],
            'trends' => $trends ?? [],
            'feeTypeBreakdown' => $feeTypeBreakdown ?? [],
            'paymentMethodBreakdown' => $paymentMethodBreakdown ?? [],
            'topPayingMembers' => $topPayingMembers ?? [],
            'selectedPeriod' => $period,
            'selectedYear' => $year,
            'selectedMonth' => $month,
            'userRoles' => $user->roles->pluck('name')->toArray(),
        ]);
    }

    /**
     * Generate PDF financial report
     */
    public function generateReport(Request $request)
    {
        $user = auth()->user();

        if (!$user->hasAnyRole(['system admin', 'association clerk'])) {
            abort(403, 'Access denied.');
        }

        $period = $request->get('period', 'monthly');
        $year = (int) $request->get('year', now()->year);
        $month = (int) $request->get('month', now()->month);

        // Get data for the report
        $summary = $this->getSummaryStatistics($period, $year, $month);
        $data = $this->getDetailedData($period, $year, $month);
        $paymentMethods = $this->getPaymentMethodBreakdown($period, $year, $month);
        $topMembers = $this->getTopPayingMembers($period, $year, $month);

        // Ensure data is not empty - add dummy row if needed
        if ($data->isEmpty()) {
            $data = collect([]);
        }

        $reportData = [
            'title' => $this->getReportTitle($period, $year, $month),
            'period' => $period,
            'year' => $year,
            'month' => $month,
            'monthName' => $period === 'monthly' ? Carbon::create($year, max(1, min(12, (int) $month)), 1)->format('F') : null,
            'summary' => $summary,
            'data' => $data,
            'payment_methods' => $paymentMethods ?? collect([]),
            'top_members' => $topMembers ?? collect([]),
            'company_info' => [
                'name' => 'Minibus Owners Association of Malawi',
                'address' => 'Lilongwe, Malawi',
                'phone' => '+265 1 234 567',
                'email' => '<EMAIL>'
            ],
            'generated_by' => $user->first_name . ' ' . $user->last_name,
            'generated_at' => now()->format('F j, Y \a\t g:i A'),
        ];

        try {
            // Add some debugging info
            \Log::info('Generating PDF with data:', [
                'period' => $period,
                'year' => $year,
                'month' => $month,
                'summary' => $summary,
                'data_count' => count($data)
            ]);

            $pdf = Pdf::loadView('reports.financial-report', $reportData);

            $filename = 'financial-report-' . $period . '-' . $year .
                ($period === 'monthly' ? '-' . str_pad($month, 2, '0', STR_PAD_LEFT) : '') . '.pdf';

            return $pdf->download($filename);
        } catch (\Exception $e) {
            \Log::error('PDF generation failed: ' . $e->getMessage(), [
                'period' => $period,
                'year' => $year,
                'month' => $month,
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['error' => 'Failed to generate PDF report: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Get summary statistics
     */
    private function getSummaryStatistics($period, $year, $month)
    {
        $query = Payment::where('status', 'fulfilled');

        // Apply date filters based on period
        if ($period === 'monthly') {
            $query->whereYear('paid_at', $year)->whereMonth('paid_at', $month);
        } elseif ($period === 'yearly') {
            $query->whereYear('paid_at', $year);
        }

        $payments = $query->get();

        return [
            'total_revenue' => $payments->sum('amount') ?? 0,
            'total_payments' => $payments->count() ?? 0,
            'registration_revenue' => $payments->where('fee_type', 'registration')->sum('amount') ?? 0,
            'affiliation_revenue' => $payments->where('fee_type', 'affiliation')->sum('amount') ?? 0,
            'average_payment' => $payments->count() > 0 ? ($payments->avg('amount') ?? 0) : 0,
        ];
    }

    /**
     * Get trends data
     */
    private function getTrends($period, $year, $month)
    {
        $query = Payment::where('status', 'fulfilled');

        if ($period === 'monthly') {
            // Get daily trends for the month
            $startDate = Carbon::create($year, $month, 1)->startOfMonth();
            $endDate = $startDate->copy()->endOfMonth();

            return $query->whereBetween('paid_at', [$startDate, $endDate])
                ->selectRaw('DATE(paid_at) as date, COUNT(*) as count, SUM(amount) as total')
                ->groupBy('date')
                ->orderBy('date')
                ->get();
        } else {
            // Get monthly trends for the year
            return $query->whereYear('paid_at', $year)
                ->selectRaw('MONTH(paid_at) as month, COUNT(*) as count, SUM(amount) as total')
                ->groupBy('month')
                ->orderBy('month')
                ->get();
        }
    }

    /**
     * Get fee type breakdown
     */
    private function getFeeTypeBreakdown($period, $year, $month)
    {
        $query = Payment::where('status', 'fulfilled');

        if ($period === 'monthly') {
            $query->whereYear('paid_at', $year)->whereMonth('paid_at', $month);
        } elseif ($period === 'yearly') {
            $query->whereYear('paid_at', $year);
        }

        return $query->selectRaw('fee_type, COUNT(*) as count, SUM(amount) as total_paid')
            ->groupBy('fee_type')
            ->get();
    }

    /**
     * Get payment method breakdown
     */
    private function getPaymentMethodBreakdown($period, $year, $month)
    {
        $query = Payment::where('status', 'fulfilled');

        if ($period === 'monthly') {
            $query->whereYear('paid_at', $year)->whereMonth('paid_at', $month);
        } elseif ($period === 'yearly') {
            $query->whereYear('paid_at', $year);
        }

        return $query->selectRaw('payment_method, COUNT(*) as count, SUM(amount) as total_amount')
            ->groupBy('payment_method')
            ->get();
    }

    /**
     * Get top paying members
     */
    private function getTopPayingMembers($period, $year, $month)
    {
        $query = Payment::where('status', 'fulfilled')
            ->with('user:id,first_name,last_name,email');

        if ($period === 'monthly') {
            $query->whereYear('paid_at', $year)->whereMonth('paid_at', $month);
        } elseif ($period === 'yearly') {
            $query->whereYear('paid_at', $year);
        }

        return $query->selectRaw('user_id, COUNT(*) as payment_count, SUM(amount) as total_amount')
            ->groupBy('user_id')
            ->orderBy('total_amount', 'desc')
            ->limit(10)
            ->get();
    }

    /**
     * Get detailed data for reports
     */
    private function getDetailedData($period, $year, $month)
    {
        $query = Payment::where('status', 'fulfilled');

        if ($period === 'daily') {
            $startDate = Carbon::create($year, $month, 1)->startOfMonth();
            $endDate = $startDate->copy()->endOfMonth();

            return $query->whereBetween('paid_at', [$startDate, $endDate])
                ->selectRaw('
                    DATE(paid_at) as date,
                    COUNT(*) as total_payments,
                    SUM(amount) as total_amount,
                    SUM(CASE WHEN fee_type = "registration" THEN amount ELSE 0 END) as registration_amount,
                    SUM(CASE WHEN fee_type = "affiliation" THEN amount ELSE 0 END) as affiliation_amount
                ')
                ->groupBy('date')
                ->orderBy('date')
                ->get();
        } elseif ($period === 'monthly') {
            return $query->whereYear('paid_at', $year)
                ->selectRaw('
                    MONTH(paid_at) as month,
                    COUNT(*) as total_payments,
                    SUM(amount) as total_amount,
                    SUM(CASE WHEN fee_type = "registration" THEN amount ELSE 0 END) as registration_amount,
                    SUM(CASE WHEN fee_type = "affiliation" THEN amount ELSE 0 END) as affiliation_amount
                ')
                ->groupBy('month')
                ->orderBy('month')
                ->get();
        } else {
            return $query->selectRaw('
                    YEAR(paid_at) as year,
                    COUNT(*) as total_payments,
                    SUM(amount) as total_amount,
                    SUM(CASE WHEN fee_type = "registration" THEN amount ELSE 0 END) as registration_amount,
                    SUM(CASE WHEN fee_type = "affiliation" THEN amount ELSE 0 END) as affiliation_amount
                ')
                ->groupBy('year')
                ->orderBy('year')
                ->get();
        }
    }

    /**
     * Get report title based on period
     */
    private function getReportTitle($period, $year, $month)
    {
        if ($period === 'monthly') {
            // Ensure valid month and year
            $validYear = max(2020, min(2030, (int) $year));
            $validMonth = max(1, min(12, (int) $month));
            return 'Financial Report - ' . Carbon::create($validYear, $validMonth, 1)->format('F Y');
        } elseif ($period === 'yearly') {
            return 'Financial Report - ' . $year;
        } else {
            return 'Financial Report - ' . ucfirst($period);
        }
    }
}
