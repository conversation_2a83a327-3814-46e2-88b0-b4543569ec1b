<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('minibus_transfer_requests', function (Blueprint $table) {
            $table->id();
            $table->foreignId('minibus_id')->constrained('minibuses')->onDelete('cascade');
            $table->foreignId('owner_id')->constrained('users')->onDelete('cascade');
            $table->string('transfer_type');
            $table->enum('status', ['pending', 'transferred'])->default('pending');
            $table->text('reason')->nullable();
            $table->string('ownership_transfer_certificate'); // path to uploaded file
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('minibus_transfer_requests');
    }
};