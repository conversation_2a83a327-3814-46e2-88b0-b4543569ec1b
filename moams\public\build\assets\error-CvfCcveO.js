import{i as b,r as a,j as e,H as S,C as M,a as D,b as L,c as E,B as l,L as y,A as n,s as c,h as Z,R as p,t as K}from"./app-DL-qYY5V.js";import{A as T,h as U}from"./app-layout-YqstQnqE.js";import{C as g}from"./clock-4mJquAMZ.js";import{C as z}from"./circle-alert-B36J8eZz.js";import{S as j}from"./shield-B3ISjw25.js";import{A as Q}from"./arrow-left-DCW23wrL.js";import"./index-1tPPImJd.js";import"./index-BTzg1GwG.js";import"./app-logo-icon-BnXlkpcX.js";import"./bus-CKTBKI0T.js";import"./dollar-sign-Cx0-nQIX.js";import"./users-DNGXY-sJ.js";import"./clipboard-list-CxRumT8v.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ee=[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]],F=b("Database",ee);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const se=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"m14.5 12.5-5 5",key:"b62r18"}],["path",{d:"m9.5 12.5 5 5",key:"1rk7el"}]],H=b("FileX",se);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const te=[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]],G=b("Wifi",te);function we({status:m=500,title:N="Something went wrong",description:B="An unexpected error occurred. Please try again later or contact support if the problem persists.",error_type:t="general",recoverable:$=!1,retryable:i=!1,error_id:k=null,timestamp:C=null}){const[x,V]=a.useState(!1),[d,W]=a.useState(""),[o,_]=a.useState(0),[u,v]=a.useState(!1),[R,P]=a.useState(0),q=()=>{switch(t){case"payment":case"payment_redirect":return e.jsx(U,{className:"w-16 h-16 text-red-500"});case"file_upload":return e.jsx(H,{className:"w-16 h-16 text-red-500"});case"database":return e.jsx(F,{className:"w-16 h-16 text-red-500"});case"network":return e.jsx(G,{className:"w-16 h-16 text-red-500"});case"unauthorized":return e.jsx(j,{className:"w-16 h-16 text-yellow-500"});case"forbidden":return e.jsx(j,{className:"w-16 h-16 text-red-500"});case"csrf":return e.jsx(g,{className:"w-16 h-16 text-orange-500"});case"throttle":return e.jsx(g,{className:"w-16 h-16 text-blue-500"});case"not_found":return e.jsx(z,{className:"w-16 h-16 text-gray-500"});default:return e.jsx(Z,{className:"w-16 h-16 text-red-500"})}},X=()=>{switch(t){case"unauthorized":return"text-yellow-600";case"csrf":case"throttle":return"text-orange-600";case"not_found":return"text-gray-600";default:return"text-red-600"}},A=async()=>{if(!(!i||u)){v(!0),_(s=>s+1);try{await new Promise(s=>setTimeout(s,1e3)),window.location.reload()}catch(s){console.error("Retry failed:",s),v(!1)}}};a.useEffect(()=>{if(i&&o===0&&["database","network","csrf"].includes(t)){P(10);const r=setInterval(()=>{P(h=>h<=1?(clearInterval(r),A(),0):h-1)},1e3);return()=>clearInterval(r)}},[i,o,t]),a.useEffect(()=>{if(m===404||t==="payment_redirect"){const s=window.location.href,r=new URLSearchParams(window.location.search),h=r.get("tx_ref"),O=r.get("status");if(h||O||s.includes("paychangu")||s.includes("ctechpay")||s.includes("/payments/")||s.includes("127.0.0.1")&&s.includes(":8000"),isPaychanguUrl){V(!0);let Y="http://127.0.0.1:8000",f="/payments/paychangu/return";const w=window.location.pathname;w.includes("/payments/")?f=w:w.includes("paychangu")&&(f="/paychangu-redirect");let I=Y+f;window.location.search&&(I+=window.location.search),W(I)}}},[m,t]),a.useEffect(()=>{if(x&&d){const s=setTimeout(()=>{window.location.href=d},3e3);return()=>clearTimeout(s)}},[x,d]);const J=()=>{const s=[];return i&&s.push(e.jsx(l,{onClick:A,disabled:u,className:"w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3",children:u?e.jsxs(e.Fragment,{children:[e.jsx(p,{className:"w-4 h-4 mr-2 animate-spin"}),"Retrying..."]}):e.jsxs(e.Fragment,{children:[e.jsx(p,{className:"w-4 h-4 mr-2"}),"Try Again ",o>0&&`(${o})`]})},"retry")),$&&s.push(e.jsxs(l,{onClick:()=>window.history.back(),variant:"outline",className:"w-full",children:[e.jsx(Q,{className:"w-4 h-4 mr-2"}),"Go Back"]},"back")),t==="unauthorized"&&s.push(e.jsx(y,{href:"/login",children:e.jsxs(l,{className:"w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3",children:[e.jsx(j,{className:"w-4 h-4 mr-2"}),"Login"]})},"login")),t==="csrf"&&s.push(e.jsxs(l,{onClick:()=>window.location.reload(),className:"w-full bg-orange-600 hover:bg-orange-700 text-white font-semibold py-3",children:[e.jsx(p,{className:"w-4 h-4 mr-2"}),"Refresh Page"]},"refresh")),s.push(e.jsx(y,{href:"/dashboard",children:e.jsxs(l,{variant:"outline",className:"w-full",children:[e.jsx(K,{className:"w-4 h-4 mr-2"}),"Go to Dashboard"]})},"dashboard")),s};return x?e.jsxs(T,{children:[e.jsx(S,{title:"Payment Redirect - Page Not Found"}),e.jsx("div",{className:"flex flex-col items-center justify-center min-h-[60vh] p-8",children:e.jsxs(M,{className:"max-w-lg w-full text-center",children:[e.jsxs(D,{children:[e.jsx(L,{className:"text-4xl font-bold text-orange-600 mb-2",children:"404"}),e.jsx("h2",{className:"text-2xl font-semibold mb-2",children:"Payment Redirect Issue"})]}),e.jsxs(E,{children:[e.jsx("p",{className:"text-gray-600 mb-4",children:"It looks like you're being redirected from a payment page, but the URL is missing some information."}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Don't worry! We can fix this for you."}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(l,{onClick:()=>window.location.href=d,className:"w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3",children:"🔗 Click Here to Continue to Payment Results"}),e.jsx("p",{className:"text-sm text-gray-500",children:"You will be automatically redirected in a few seconds..."}),e.jsx("div",{className:"pt-4 border-t",children:e.jsx(y,{href:"/dashboard",children:e.jsx(l,{variant:"outline",className:"w-full",children:"Or Go to Dashboard"})})})]})]})]})})]}):e.jsxs(T,{children:[e.jsx(S,{title:N}),e.jsx("div",{className:"flex flex-col items-center justify-center min-h-[60vh] p-8",children:e.jsxs(M,{className:"max-w-2xl w-full",children:[e.jsxs(D,{className:"text-center",children:[e.jsx("div",{className:"flex justify-center mb-4",children:q()}),e.jsx(L,{className:`text-4xl font-bold mb-2 ${X()}`,children:m}),e.jsx("h2",{className:"text-2xl font-semibold mb-2 text-gray-800",children:N})]}),e.jsxs(E,{className:"space-y-6",children:[e.jsx("p",{className:"text-gray-600 text-center text-lg",children:B}),R>0&&e.jsxs(n,{className:"border-blue-200 bg-blue-50",children:[e.jsx(g,{className:"h-4 w-4 text-blue-600"}),e.jsxs(c,{className:"text-blue-800",children:["Automatically retrying in ",R," seconds..."]})]}),k&&e.jsxs("div",{className:"text-center",children:[e.jsxs("p",{className:"text-sm text-gray-500",children:["Error ID: ",e.jsx("code",{className:"bg-gray-100 px-2 py-1 rounded text-xs",children:k})]}),C&&e.jsx("p",{className:"text-xs text-gray-400 mt-1",children:new Date(C).toLocaleString()})]}),e.jsx("div",{className:"space-y-3",children:J().map((s,r)=>e.jsx("div",{children:s},r))}),t==="database"&&e.jsxs(n,{className:"border-yellow-200 bg-yellow-50",children:[e.jsx(F,{className:"h-4 w-4 text-yellow-600"}),e.jsx(c,{className:"text-yellow-800",children:"This appears to be a temporary database issue. The system will automatically retry shortly."})]}),t==="network"&&e.jsxs(n,{className:"border-blue-200 bg-blue-50",children:[e.jsx(G,{className:"h-4 w-4 text-blue-600"}),e.jsx(c,{className:"text-blue-800",children:"Please check your internet connection and try again."})]}),t==="file_upload"&&e.jsxs(n,{className:"border-orange-200 bg-orange-50",children:[e.jsx(H,{className:"h-4 w-4 text-orange-600"}),e.jsx(c,{className:"text-orange-800",children:"Make sure your file is under 5MB and in a supported format (PDF, JPG, PNG)."})]}),t==="payment"&&e.jsxs(n,{className:"border-red-200 bg-red-50",children:[e.jsx(U,{className:"h-4 w-4 text-red-600"}),e.jsx(c,{className:"text-red-800",children:"If you were charged, please contact support with your transaction details."})]}),o>2&&e.jsxs(n,{className:"border-gray-200 bg-gray-50",children:[e.jsx(z,{className:"h-4 w-4 text-gray-600"}),e.jsx(c,{className:"text-gray-800",children:"If the problem persists, please contact <NAME_EMAIL> or try again later."})]})]})]})})]})}export{we as default};
