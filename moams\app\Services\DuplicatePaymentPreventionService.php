<?php

namespace App\Services;

use App\Models\Payment;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class DuplicatePaymentPreventionService
{
    /**
     * Default duplicate check window (in hours)
     */
    private $duplicateCheckWindow = 24;

    /**
     * Check if a payment would be a duplicate
     */
    public function isDuplicatePayment(int $userId, string $feeType, float $amount): array
    {
        $checkWindow = Carbon::now()->subHours($this->duplicateCheckWindow);
        
        // Look for recent successful payments for the same user and fee type
        $recentPayment = Payment::where('user_id', $userId)
            ->where('fee_type', $feeType)
            ->where('status', 'fulfilled')
            ->where('created_at', '>=', $checkWindow)
            ->orderBy('created_at', 'desc')
            ->first();

        if ($recentPayment) {
            return [
                'is_duplicate' => true,
                'existing_payment' => $recentPayment,
                'message' => $this->getDuplicateMessage($recentPayment),
                'check_window_hours' => $this->duplicateCheckWindow,
            ];
        }

        // Also check for pending payments that might be in progress
        $pendingPayment = Payment::where('user_id', $userId)
            ->where('fee_type', $feeType)
            ->where('status', 'pending')
            ->where('created_at', '>=', $checkWindow)
            ->orderBy('created_at', 'desc')
            ->first();

        if ($pendingPayment) {
            return [
                'is_duplicate' => true,
                'existing_payment' => $pendingPayment,
                'message' => $this->getPendingPaymentMessage($pendingPayment),
                'check_window_hours' => $this->duplicateCheckWindow,
            ];
        }

        return [
            'is_duplicate' => false,
            'message' => 'No duplicate payment detected',
        ];
    }

    /**
     * Check if user has valid membership that doesn't need renewal
     */
    public function hasValidMembership(int $userId, string $feeType): array
    {
        $user = User::with('memberships')->find($userId);
        
        if (!$user) {
            return [
                'has_valid_membership' => false,
                'message' => 'User not found',
            ];
        }

        // Check for valid membership based on fee type
        if ($feeType === 'registration') {
            $validMembership = $user->memberships()
                ->where('type', 'Registration')
                ->where('status', 'Registered')
                ->first();

            if ($validMembership) {
                return [
                    'has_valid_membership' => true,
                    'membership' => $validMembership,
                    'message' => 'User already has a valid registration. Registration fees are only paid once.',
                ];
            }
        } elseif ($feeType === 'affiliation') {
            $validMembership = $user->memberships()
                ->where('type', 'Affiliation')
                ->where('status', 'Affiliation fee paid')
                ->where('end_date', '>', Carbon::now())
                ->first();

            if ($validMembership) {
                return [
                    'has_valid_membership' => true,
                    'membership' => $validMembership,
                    'message' => "User already has a valid affiliation membership until {$validMembership->end_date->format('F j, Y')}.",
                ];
            }
        }

        return [
            'has_valid_membership' => false,
            'message' => 'No valid membership found - payment can proceed',
        ];
    }

    /**
     * Generate transaction hash for duplicate detection
     */
    public function generateTransactionHash(int $userId, string $feeType, float $amount, string $date = null): string
    {
        $date = $date ?: Carbon::now()->format('Y-m-d');
        
        return hash('sha256', implode('|', [
            $userId,
            $feeType,
            number_format($amount, 2),
            $date
        ]));
    }

    /**
     * Validate payment request for duplicates
     */
    public function validatePaymentRequest(array $paymentData): array
    {
        $userId = $paymentData['user_id'];
        $feeType = $paymentData['fee_type'];
        $amount = $paymentData['amount'];

        // Check for duplicate payments
        $duplicateCheck = $this->isDuplicatePayment($userId, $feeType, $amount);
        
        if ($duplicateCheck['is_duplicate']) {
            Log::warning('Duplicate payment attempt blocked', [
                'user_id' => $userId,
                'fee_type' => $feeType,
                'amount' => $amount,
                'existing_payment_id' => $duplicateCheck['existing_payment']->id,
                'check_window_hours' => $duplicateCheck['check_window_hours']
            ]);

            return [
                'valid' => false,
                'error_type' => 'duplicate_payment',
                'message' => $duplicateCheck['message'],
                'existing_payment' => $duplicateCheck['existing_payment'],
            ];
        }

        // Check for valid existing membership
        $membershipCheck = $this->hasValidMembership($userId, $feeType);
        
        if ($membershipCheck['has_valid_membership']) {
            Log::warning('Payment attempt blocked - valid membership exists', [
                'user_id' => $userId,
                'fee_type' => $feeType,
                'membership_id' => $membershipCheck['membership']->id,
                'membership_status' => $membershipCheck['membership']->status
            ]);

            return [
                'valid' => false,
                'error_type' => 'valid_membership_exists',
                'message' => $membershipCheck['message'],
                'existing_membership' => $membershipCheck['membership'],
            ];
        }

        // Generate transaction hash for this payment
        $transactionHash = $this->generateTransactionHash($userId, $feeType, $amount);

        return [
            'valid' => true,
            'transaction_hash' => $transactionHash,
            'message' => 'Payment validation passed',
        ];
    }

    /**
     * Clean up old pending payments for retry scenarios
     */
    public function cleanupOldPendingPayments(int $userId, string $feeType): int
    {
        $cutoffTime = Carbon::now()->subHours(2); // Clean up payments older than 2 hours
        
        $deletedCount = Payment::where('user_id', $userId)
            ->where('fee_type', $feeType)
            ->where('status', 'pending')
            ->where('created_at', '<', $cutoffTime)
            ->delete();

        if ($deletedCount > 0) {
            Log::info('Cleaned up old pending payments', [
                'user_id' => $userId,
                'fee_type' => $feeType,
                'deleted_count' => $deletedCount
            ]);
        }

        return $deletedCount;
    }

    /**
     * Get duplicate message for successful payment
     */
    private function getDuplicateMessage(Payment $payment): string
    {
        $timeAgo = $payment->created_at->diffForHumans();
        $feeType = ucfirst($payment->fee_type);
        
        return "You already made a successful {$feeType} payment {$timeAgo}. " .
               "Receipt number: {$payment->receipt_number}. " .
               "If you believe this is an error, please contact support.";
    }

    /**
     * Get message for pending payment
     */
    private function getPendingPaymentMessage(Payment $payment): string
    {
        $timeAgo = $payment->created_at->diffForHumans();
        $feeType = ucfirst($payment->fee_type);
        
        return "You have a {$feeType} payment that is still being processed (started {$timeAgo}). " .
               "Please wait for the current payment to complete before starting a new one. " .
               "Order reference: {$payment->order_reference}";
    }

    /**
     * Set custom duplicate check window
     */
    public function setDuplicateCheckWindow(int $hours): void
    {
        $this->duplicateCheckWindow = $hours;
    }

    /**
     * Get current duplicate check window
     */
    public function getDuplicateCheckWindow(): int
    {
        return $this->duplicateCheckWindow;
    }

    /**
     * Force allow duplicate payment (for admin override)
     */
    public function createDuplicateOverride(int $userId, string $feeType, string $reason): array
    {
        Log::info('Duplicate payment override created', [
            'user_id' => $userId,
            'fee_type' => $feeType,
            'reason' => $reason,
            'created_by' => auth()->id()
        ]);

        return [
            'override_created' => true,
            'message' => 'Duplicate payment override created',
            'reason' => $reason,
        ];
    }

    /**
     * Get payment statistics for duplicate prevention monitoring
     */
    public function getDuplicatePreventionStats(): array
    {
        $last24Hours = Carbon::now()->subDay();
        
        return [
            'total_payments_last_24h' => Payment::where('created_at', '>=', $last24Hours)->count(),
            'successful_payments_last_24h' => Payment::where('created_at', '>=', $last24Hours)
                ->where('status', 'fulfilled')->count(),
            'pending_payments_last_24h' => Payment::where('created_at', '>=', $last24Hours)
                ->where('status', 'pending')->count(),
            'failed_payments_last_24h' => Payment::where('created_at', '>=', $last24Hours)
                ->where('status', 'failed')->count(),
            'duplicate_check_window_hours' => $this->duplicateCheckWindow,
        ];
    }
}
