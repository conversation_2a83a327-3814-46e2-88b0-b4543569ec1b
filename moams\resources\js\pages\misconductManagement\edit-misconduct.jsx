import { <PERSON>, Link, useForm } from '@inertiajs/react';
import { ChevronLeft, AlertTriangle, Save, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import InputError from '@/components/input-error';
import { useState, useCallback } from 'react';

// Driver Combobox Component
function DriverCombobox({ value, onChange, drivers, placeholder }) {
    const [isOpen, setIsOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');

    const filteredDrivers = drivers.filter(driver =>
        `${driver.first_name} ${driver.last_name}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
        driver.phone_number?.includes(searchTerm)
    );

    const selectedDriver = drivers.find(d => d.id === value?.id);

    return (
        <div className="relative">
            <Input
                placeholder={placeholder}
                value={selectedDriver ? `${selectedDriver.first_name} ${selectedDriver.last_name}` : searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onFocus={() => setIsOpen(true)}
                onBlur={() => setTimeout(() => setIsOpen(false), 200)}
            />
            {isOpen && (
                <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
                    {filteredDrivers.length === 0 ? (
                        <div className="px-3 py-2 text-gray-500">No drivers found</div>
                    ) : (
                        filteredDrivers.map((driver) => (
                            <div
                                key={driver.id}
                                className="px-3 py-2 hover:bg-gray-100 cursor-pointer"
                                onClick={() => {
                                    onChange(driver);
                                    setSearchTerm('');
                                    setIsOpen(false);
                                }}
                            >
                                <div className="font-medium">{driver.first_name} {driver.last_name}</div>
                                <div className="text-sm text-gray-500">{driver.phone_number}</div>
                            </div>
                        ))
                    )}
                </div>
            )}
        </div>
    );
}

export default function EditMisconduct({ misconduct, drivers }) {
    const formatDateForInput = (dateString) => {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toISOString().split('T')[0];
    };

    const [selectedDriver, setSelectedDriver] = useState(
        drivers.find(d => d.id === misconduct.offender_id) || null
    );

    const { data, setData, post, processing, errors } = useForm({
        name: misconduct.name,
        description: misconduct.description || '',
        offense_date: formatDateForInput(misconduct.offense_date),
        severity: misconduct.severity || 'medium',
        evidence_file: null,
        offender_type: 'driver',
        offender_id: misconduct.offender_id,
    });

    const misconductTypes = [
        'Reckless Driving',
        'Speeding',
        'Overloading',
        'Route Deviation',
        'Poor Customer Service',
        'Vehicle Maintenance Issues',
        'Late Arrival',
        'Unauthorized Stops',
        'Inappropriate Behavior',
        'Other'
    ];

    const handleSubmit = (e) => {
        e.preventDefault();
        post(`/misconducts/${misconduct.id}`, {
            forceFormData: true,
            data: { ...data, _method: 'PUT' },
        });
    };

    const handleDriverChange = (driver) => {
        setSelectedDriver(driver);
        setData('offender_id', driver?.id || '');
    };

    const breadcrumbs = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Misconduct Management', href: '/misconducts' },
        { title: 'Edit Misconduct', href: `/misconducts/${misconduct.id}/edit` },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Edit Driver Misconduct" />

            <div className="container mx-auto px-4 py-8 max-w-2xl">
                {/* Header */}
                <div className="flex items-center gap-4 mb-6">
                    <Link
                        href="/misconducts"
                        className="flex items-center px-3 py-1 rounded hover:bg-gray-100 border border-gray-200"
                    >
                        <ChevronLeft className="h-4 w-4 mr-1" /> Back
                    </Link>
                    <div>
                        <p className="text-gray-600">Update misconduct incident details</p>
                    </div>
                </div>

                {/* Form */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <AlertTriangle className="h-5 w-5 text-indigo-600" />
                            Update Misconduct Details
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            {/* Misconduct Type */}
                            <div>
                                <Label htmlFor="name">Misconduct Type *</Label>
                                <Select value={data.name} onValueChange={(value) => setData('name', value)}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select misconduct type" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {misconductTypes.map(type => (
                                            <SelectItem key={type} value={type}>{type}</SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                <InputError message={errors.name} className="mt-2" />
                            </div>

                            {/* Driver Selection */}
                            <div>
                                <Label htmlFor="offender_id">Select Driver *</Label>
                                <DriverCombobox
                                    value={selectedDriver}
                                    onChange={handleDriverChange}
                                    drivers={drivers}
                                    placeholder="Search and select a driver..."
                                />
                                <InputError message={errors.offender_id} className="mt-2" />
                            </div>

                            {/* Offense Date */}
                            <div>
                                <Label htmlFor="offense_date">Offense Date *</Label>
                                <Input
                                    id="offense_date"
                                    type="date"
                                    value={data.offense_date}
                                    onChange={e => setData('offense_date', e.target.value)}
                                    max={new Date().toISOString().split('T')[0]}
                                    required
                                />
                                <InputError message={errors.offense_date} className="mt-2" />
                            </div>

                            {/* Severity Level */}
                            <div>
                                <Label htmlFor="severity">Severity Level *</Label>
                                <Select value={data.severity} onValueChange={(value) => setData('severity', value)}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select severity level" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="low">
                                            <div className="flex items-center gap-2">
                                                <div className="w-3 h-3 rounded-full bg-green-500"></div>
                                                Low (5 points deducted)
                                            </div>
                                        </SelectItem>
                                        <SelectItem value="medium">
                                            <div className="flex items-center gap-2">
                                                <div className="w-3 h-3 rounded-full bg-orange-500"></div>
                                                Medium (10 points deducted)
                                            </div>
                                        </SelectItem>
                                        <SelectItem value="high">
                                            <div className="flex items-center gap-2">
                                                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                                                High (20 points deducted)
                                            </div>
                                        </SelectItem>
                                    </SelectContent>
                                </Select>
                                <p className="text-xs text-gray-500 mt-1">
                                    Points will be adjusted based on severity changes.
                                </p>
                                <InputError message={errors.severity} className="mt-2" />
                            </div>

                            {/* Description */}
                            <div>
                                <Label htmlFor="description">Description</Label>
                                <Textarea
                                    id="description"
                                    value={data.description}
                                    onChange={e => setData('description', e.target.value)}
                                    placeholder="Provide detailed description of the misconduct incident..."
                                    rows={4}
                                    className="resize-none"
                                />
                                <p className="text-xs text-gray-500 mt-1">
                                    Include specific details about what happened and any relevant circumstances.
                                </p>
                                <InputError message={errors.description} className="mt-2" />
                            </div>

                            {/* Evidence Upload */}
                            <div>
                                <Label htmlFor="evidence_file">Evidence (Optional)</Label>
                                {misconduct.evidence_file && (
                                    <p className="text-sm text-gray-600 mb-2">
                                        Current file: {misconduct.evidence_file.split('/').pop()}
                                    </p>
                                )}
                                <Input
                                    id="evidence_file"
                                    type="file"
                                    accept=".pdf,.jpg,.jpeg,.png"
                                    onChange={(e) => setData('evidence_file', e.target.files[0])}
                                />
                                <p className="text-xs text-gray-500 mt-1">
                                    Upload new evidence to replace existing file (PDF, JPG, JPEG, PNG - Max 5MB)
                                </p>
                                <InputError message={errors.evidence_file} className="mt-2" />
                            </div>

                            {/* Form Actions */}
                            <div className="flex gap-4 pt-4">
                                <Button type="submit" disabled={processing} className="flex-1">
                                    <Save className="h-4 w-4 mr-2" />
                                    {processing ? 'Updating...' : 'Update Misconduct'}
                                </Button>
                                <Link href="/misconducts" className="flex-1">
                                    <Button type="button" variant="outline" className="w-full">
                                        <X className="h-4 w-4 mr-2" />
                                        Cancel
                                    </Button>
                                </Link>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
