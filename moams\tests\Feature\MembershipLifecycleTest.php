<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Membership;
use App\Models\Payment;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Artisan;
use Tests\TestCase;
use Carbon\Carbon;

class MembershipLifecycleTest extends TestCase
{
    use RefreshDatabase;

    public function test_minibus_owner_registration_and_membership_lifecycle()
    {
        // Ensure roles and permissions are seeded
        $this->seed();
        // 1. Create a minibus owner
        $joiningDate = Carbon::parse('2022-07-01');
        $user = User::factory()->create([
            'joining_date' => $joiningDate,
        ]);
        $user->assignRole('minibus owner');
        // Manually create the first registration membership for the test
        $joiningDate = $user->joining_date;
        $user->memberships()->create([
            'start_date' => $joiningDate,
            'end_date' => $joiningDate->copy()->addYear()->subDay(),
            'type' => 'Registration',
            'status' => 'Unregistered',
        ]);

        // Simulate controller logic for first membership
        $this->assertDatabaseHas('memberships', [
            'user_id' => $user->id,
            'type' => 'Registration',
            'status' => 'Unregistered',
        ]);
        $registration = Membership::where('user_id', $user->id)->where('type', 'Registration')->first();

        // 2. Pay registration fee
        $payment = Payment::create([
            'user_id' => $user->id,
            'membership_id' => $registration->id,
            'amount' => 100,
            'fee_type' => 'registration',
            'status' => 'fulfilled',
            'payment_method' => 'cash',
            'paid_at' => now(),
        ]);
        // Simulate controller logic: update membership status
        $registration->update(['status' => 'Registered']);
        $registration->refresh();
        $this->assertEquals('Registered', $registration->status);

        // 3. Simulate annual renewal (1 year later)
        Carbon::setTestNow($joiningDate->copy()->addYear()->addDay());
        Artisan::call('memberships:renewal');
        $affiliation = Membership::where('user_id', $user->id)->where('type', 'Affiliation')->first();
        $this->assertNotNull($affiliation);
        $this->assertEquals('Need affiliation fee', $affiliation->status);

        // 4. Pay affiliation fee
        $affPay = Payment::create([
            'user_id' => $user->id,
            'membership_id' => $affiliation->id,
            'amount' => 50,
            'fee_type' => 'affiliation',
            'status' => 'fulfilled',
            'payment_method' => 'cash',
            'paid_at' => now(),
        ]);
        // Simulate controller logic: update membership status
        $affiliation->update(['status' => 'Affiliation fee paid']);
        $affiliation->refresh();
        $this->assertEquals('Affiliation fee paid', $affiliation->status);

        // 5. Prevent duplicate payment for same year
        $duplicate = Payment::make([
            'user_id' => $user->id,
            'membership_id' => $affiliation->id,
            'amount' => 50,
            'fee_type' => 'affiliation',
            'status' => 'fulfilled',
            'payment_method' => 'cash',
            'paid_at' => now(),
        ]);
        $this->assertFalse($duplicate->save()); // Should not save due to validation

        // 6. Simulate another year, no payment made
        Carbon::setTestNow($joiningDate->copy()->addYears(2)->addDay());
        Artisan::call('memberships:renewal');
        $secondAff = Membership::where('user_id', $user->id)->where('type', 'Affiliation')->orderByDesc('start_date')->first();
        $this->assertEquals('Need affiliation fee', $secondAff->status);
        // After renewal, previous year should be marked as not paid if not paid
        $affiliation->refresh();
        $this->assertEquals('Affiliation fee paid', $affiliation->status); // Already paid
        // Now, don't pay for $secondAff, simulate another year
        Carbon::setTestNow($joiningDate->copy()->addYears(3)->addDay());
        Artisan::call('memberships:renewal');
        $secondAff->refresh();
        $this->assertEquals('Affiliation fee not paid', $secondAff->status);

        // 7. Clerk pays for past unpaid year
        $latePay = Payment::create([
            'user_id' => $user->id,
            'membership_id' => $secondAff->id,
            'amount' => 50,
            'fee_type' => 'affiliation',
            'status' => 'fulfilled',
            'payment_method' => 'cash',
            'paid_at' => now(),
        ]);
        $secondAff->refresh();
        $this->assertEquals('Affiliation fee paid', $secondAff->status);
    }
}