<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Http\Controllers\RegisteredUserController;
use App\Http\Controllers\MembershipController;

Route::middleware(['auth', 'verified', 'role:association clerk|association manager'])->group(function () {
    Route::get('/membership-management', [MembershipController::class, 'index'])->name('membership.management');
    // Move create routes above the {user} route
    Route::get('/membership-management/create', [RegisteredUserController::class, 'create'])->name('membership.management.create');
    Route::post('/membership-management/create', [RegisteredUserController::class, 'storeUser'])->name('membership.management.store');
    Route::get('/membership-management/{user}', function ($userId, \Illuminate\Http\Request $request) {
        $user = \App\Models\User::findOrFail($userId);

        // Build memberships query with pagination
        $membershipsQuery = $user->memberships()->with('payments');

        // Apply tab filter
        $tab = $request->get('tab', 'all');
        if ($tab === 'paid') {
            $membershipsQuery->whereIn('status', ['Registered', 'Affiliation fee paid']);
        } elseif ($tab === 'unpaid') {
            $membershipsQuery->whereIn('status', ['Unregistered', 'Need affiliation fee', 'Affiliation fee not paid']);
        }

        // Order by start date (newest first)
        $membershipsQuery->orderBy('start_date', 'desc');

        // Paginate memberships
        $memberships = $membershipsQuery->paginate(10)->withQueryString();

        // Get unpaid memberships count for warning display
        $unpaidCount = $user->memberships()
            ->whereIn('status', ['Unregistered', 'Need affiliation fee', 'Affiliation fee not paid'])
            ->count();

        return Inertia::render('MembershipManagement/summary-membership', [
            'user' => $user,
            'memberships' => $memberships,
            'unpaidCount' => $unpaidCount,
            'currentFees' => [
                'registration' => \App\Models\FeeSetting::getCurrentFeeAmount('registration'),
                'affiliation' => \App\Models\FeeSetting::getCurrentFeeAmount('affiliation'),
            ],
            'filters' => [
                'tab' => $tab,
            ],
        ]);
    })->name('membership.management.summary');
    Route::get('/membership-management/{user}/edit', [RegisteredUserController::class, 'edit'])->name('membership.management.edit');
    Route::patch('/membership-management/{user}', [RegisteredUserController::class, 'update'])->name('membership.management.update');
    Route::delete('/memberships/{membership}', [MembershipController::class, 'destroy'])->name('memberships.destroy');
});

// Add route for minibus owners to view their own memberships using the same summary page
Route::middleware(['auth', 'verified', 'role:minibus owner'])->group(function () {
    Route::get('/my-membership', function (\Illuminate\Http\Request $request) {
        $user = auth()->user();

        // Clear any model cache and force fresh data
        \Illuminate\Support\Facades\Cache::forget("user_memberships_{$user->id}");

        // Build memberships query - for minibus owners, only show unpaid memberships
        $membershipsQuery = $user->memberships()->with('payments')
            ->whereIn('status', ['Unregistered', 'Need affiliation fee', 'Affiliation fee not paid'])
            ->orderBy('start_date', 'desc');

        // Paginate memberships
        $memberships = $membershipsQuery->paginate(10)->withQueryString();

        // Log the current membership statuses for debugging
        \Log::info('Loading my-membership page', [
            'user_id' => $user->id,
            'total_memberships' => $memberships->total(),
            'current_page' => $memberships->currentPage(),
        ]);

        // Get unpaid memberships count for warning display
        $unpaidCount = $user->memberships()
            ->whereIn('status', ['Unregistered', 'Need affiliation fee', 'Affiliation fee not paid'])
            ->count();

        return Inertia::render('MembershipManagement/summary-membership', [
            'user' => $user,
            'memberships' => $memberships,
            'unpaidCount' => $unpaidCount,
            'currentFees' => [
                'registration' => \App\Models\FeeSetting::getCurrentFeeAmount('registration'),
                'affiliation' => \App\Models\FeeSetting::getCurrentFeeAmount('affiliation'),
            ],
            'timestamp' => now()->timestamp, // Force cache busting
            'flash' => [
                'payment_success' => session('payment_success')
            ]
        ]);
    })->name('my.membership');
});