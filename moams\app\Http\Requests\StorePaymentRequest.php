<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StorePaymentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'user_id' => ['required', 'exists:users,id'],
            'membership_id' => [
                'required',
                'exists:memberships,id',
                function ($attribute, $value, $fail) {
                    $membership = \App\Models\Membership::find($value);
                    if (!$membership) {
                        return $fail('Selected membership does not exist.');
                    }
                    if ($membership->user_id != $this->user_id) {
                        return $fail('Selected membership does not belong to the user.');
                    }
                    if (!in_array($membership->status, ['Unregistered', 'Need affiliation fee', 'Affiliation fee not paid'])) {
                        return $fail('Selected membership is not unpaid.');
                    }
                    $existing = $membership->payments()->where('fee_type', $this->fee_type)->where('status', 'fulfilled')->exists();
                    if ($existing) {
                        return $fail('A fulfilled payment already exists for this membership and fee type.');
                    }
                    if (
                        ($this->fee_type === 'registration' && $membership->type !== 'Registration') ||
                        ($this->fee_type === 'affiliation' && $membership->type !== 'Affiliation')
                    ) {
                        return $fail('Fee type does not match membership type. Allowed values: Registration, Affiliation.');
                    }
                }
            ],
            'amount' => [
                'required',
                'numeric',
                'min:0',
                function ($attribute, $value, $fail) {
                    $feeType = $this->fee_type;
                    $activeFee = \App\Models\FeeSetting::where('fee_type', $feeType)
                        ->where('is_active', true)
                        ->orderBy('effective_from', 'desc')
                        ->first();
                    if ($activeFee && $value < $activeFee->amount) {
                        return $fail('The payment amount cannot be less than the current active fee amount (MK ' . number_format((float) $value, 2) . ').');
                    }
                }
            ],
            'fee_type' => ['required', 'in:registration,affiliation'],
            'payment_method' => ['required', 'in:cash,mobile_money,bank_transfer,ctechpay_card'],
        ];
    }
}
