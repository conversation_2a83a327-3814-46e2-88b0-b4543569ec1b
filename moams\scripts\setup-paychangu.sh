#!/bin/bash

# Paychangu Integration Setup Script
# This script helps set up the Paychangu payment gateway integration

echo "🚀 Setting up Paychangu Payment Gateway Integration..."

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ .env file not found. Please create one first."
    exit 1
fi

# Check if required environment variables are set
echo "📋 Checking environment variables..."

if ! grep -q "PAYCHANGU_PUBLIC_KEY" .env; then
    echo "⚠️  PAYCHANGU_PUBLIC_KEY not found in .env"
    echo "Please add: PAYCHANGU_PUBLIC_KEY=your_public_key"
fi

if ! grep -q "PAYCHANGU_PRIVATE_KEY" .env; then
    echo "⚠️  PAYCHANGU_PRIVATE_KEY not found in .env"
    echo "Please add: PAYCHANGU_PRIVATE_KEY=your_private_key"
fi

if ! grep -q "PAYCHANGU_ENVIRONMENT" .env; then
    echo "⚠️  PAYCHANGU_ENVIRONMENT not found in .env"
    echo "Please add: PAYCHANGU_ENVIRONMENT=test (or live for production)"
fi

# Run migrations
echo "🗄️  Running database migrations..."
php artisan migrate --force

if [ $? -eq 0 ]; then
    echo "✅ Database migrations completed successfully"
else
    echo "❌ Database migrations failed"
    exit 1
fi

# Clear caches
echo "🧹 Clearing application caches..."
php artisan config:clear
php artisan cache:clear
php artisan route:clear

# Run tests
echo "🧪 Running Paychangu integration tests..."
php artisan test --filter=PaychanguIntegrationTest

if [ $? -eq 0 ]; then
    echo "✅ Tests passed successfully"
else
    echo "⚠️  Some tests failed - please check the output above"
fi

# Check if npm/yarn is available for frontend build
if command -v npm &> /dev/null; then
    echo "📦 Building frontend assets with npm..."
    npm run build
elif command -v yarn &> /dev/null; then
    echo "📦 Building frontend assets with yarn..."
    yarn build
else
    echo "⚠️  npm/yarn not found - please build frontend assets manually"
fi

echo ""
echo "🎉 Paychangu integration setup completed!"
echo ""
echo "📝 Next steps:"
echo "1. Update your .env file with actual Paychangu credentials"
echo "2. Configure webhook URLs in your Paychangu dashboard:"
echo "   - Webhook URL: https://yourdomain.com/payments/paychangu/webhook"
echo "3. Test the payment flow in your application"
echo "4. Monitor logs for any issues"
echo ""
echo "📚 Documentation: See PAYCHANGU_INTEGRATION.md for detailed information"
