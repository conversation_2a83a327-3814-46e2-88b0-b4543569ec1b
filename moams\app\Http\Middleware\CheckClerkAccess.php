<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckClerkAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated
        if (!auth()->check()) {
            // Redirect to login with intended URL
            return redirect()->route('login')->with('message', 'Please log in to access this page.');
        }

        $user = auth()->user();
        
        // Check if user has clerk or manager role
        if (!$user->hasAny<PERSON>ole(['association clerk', 'association manager'])) {
            // If user is logged in but doesn't have the right role
            if ($request->expectsJson()) {
                return response()->json([
                    'message' => 'Access denied. This page is only accessible to association clerks and managers.',
                    'redirect' => route('dashboard')
                ], 403);
            }
            
            return redirect()->route('dashboard')
                ->with('error', 'Access denied. This page is only accessible to association clerks and managers.');
        }

        return $next($request);
    }
}
