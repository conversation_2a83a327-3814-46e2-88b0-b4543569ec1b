import React, { useEffect, useState } from 'react';
import { Link, router } from '@inertiajs/react';
import { navigateToLogicalParent } from '@/utils/navigation';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Bus, Shuffle, History as HistoryIcon } from 'lucide-react';

export default function ShowMinibus({ minibus, userRole, history = [] }) {
    if (!minibus) {
        return (
            <AppLayout>
                <div className="container mx-auto px-4 py-8 text-center">
                    <h1 className="text-2xl font-bold mb-4">Minibus Not Found</h1>
                    <p className="text-gray-600">The minibus you are looking for does not exist or has been removed.</p>
                    <Link href="/minibuses">
                        <Button variant="outline" className="mt-6">Back to Minibuses</Button>
                    </Link>
                </div>
            </AppLayout>
        );
    }
    const breadcrumbs = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: userRole === 'minibus owner' ? 'My Minibuses' : 'Minibus Management', href: '/minibuses' },
        { title: minibus.number_plate, href: `/minibuses/${minibus.id}` },
    ];

    // If user is minibus owner and minibus is archived, don't render anything while redirecting
    if (userRole === 'minibus owner' && minibus.archived) {
        return null;
    }

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <div className="p-2 sm:p-4 md:p-8">
                <div className="flex flex-col sm:flex-row gap-2 sm:gap-4 items-center mb-4 sm:mb-6">
                    <div className="flex items-center gap-2">
                        <Button variant="outline" onClick={() => navigateToLogicalParent(breadcrumbs)} className="w-full sm:w-auto">
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Back
                        </Button>
                        <span className="text-muted-foreground text-base font-medium">
                            View detailed information about this minibus and its ownership history.
                        </span>
                    </div>
                </div>
                <div className="w-full mx-auto">
                    <Card className="mb-2 sm:mb-6">
                        <div className="p-2 sm:p-6">
                            <h3 className="text-base sm:text-lg md:text-xl font-semibold mb-3">
                                Minibus Information
                            </h3>
                            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                                <div className="mb-2 text-sm sm:text-base">
                                    <strong>Number Plate:</strong> {minibus.number_plate}
                                </div>
                                <div className="mb-2 text-sm sm:text-base">
                                    <strong>Make:</strong> {minibus.make || '-'}
                                </div>
                                <div className="mb-2 text-sm sm:text-base">
                                    <strong>Model:</strong> {minibus.model || '-'}
                                </div>
                                <div className="mb-2 text-sm sm:text-base">
                                    <strong>Year of Make:</strong> {minibus.year_of_make || '-'}
                                </div>
                                <div className="mb-2 text-sm sm:text-base">
                                    <strong>Main Colour:</strong> {minibus.main_colour || '-'}
                                </div>
                                <div className="mb-2 text-sm sm:text-base">
                                    <strong>Route:</strong> {minibus.route?.name || '-'}
                                </div>
                                {/* Owner field only for non-minibus owners */}
                                {userRole !== 'minibus owner' && (
                                    <div className="mb-2 text-sm sm:text-base">
                                        <strong>Owner:</strong> {minibus.archived ? 'Non-member' : (minibus.minibusOwner ? `${minibus.minibusOwner.first_name} ${minibus.minibusOwner.last_name}` : 'No owner assigned')}
                                    </div>
                                )}
                                {minibus.archived && (
                                    <div className="mb-2 text-sm sm:text-base text-amber-600">
                                        <strong>Status:</strong> Archived (Transferred to Non-Member)
                                    </div>
                                )}
                            </div>
                            <div className="mt-4 flex justify-end gap-2">
                                {userRole === 'minibus owner' && (
                                    <Link href={route('minibuses.transfer.request', minibus.id)}>
                                        <Button variant="outline" size="sm">
                                            <Shuffle className="h-4 w-4 mr-2" />
                                            Request Transfer
                                        </Button>
                                    </Link>
                                )}
                                {userRole === 'association clerk' && !minibus.archived && (
                                    <Link href={route('minibuses.transfer', minibus.id)}>
                                        <Button variant="default" size="sm" className="bg-blue-600 hover:bg-blue-700">
                                            <Shuffle className="h-4 w-4 mr-2" />
                                            Transfer Ownership
                                        </Button>
                                    </Link>
                                )}
                            </div>
                        </div>
                    </Card>
                </div>
                {userRole !== 'minibus owner' && (
                    <div className="w-full mx-auto">
                        <div className="flex justify-end mb-4">
                            <Link href={route('minibuses.history', minibus.id)}>
                                <Button variant="outline">
                                    <HistoryIcon className="h-4 w-4 mr-2" />
                                    View Detailed History
                                </Button>
                            </Link>
                        </div>
                        <Card>
                            <div className="p-2 sm:p-4">
                                <h3 className="text-base sm:text-lg md:text-2xl font-semibold mb-5 text-center">Ownership History</h3>
                                <div className="overflow-x-auto w-full">
                                    <table className="w-full bg-white border text-xs sm:text-sm md:text-base">
                                        <thead>
                                            <tr className="bg-gray-50">
                                                <th className="py-2 px-2 sm:px-3 md:px-6 border text-left font-semibold text-xs sm:text-sm">Previous Owner</th>
                                                <th className="py-2 px-2 sm:px-3 md:px-6 border text-left font-semibold text-xs sm:text-sm">New Owner</th>
                                                <th className="py-2 px-2 sm:px-3 md:px-6 border text-left font-semibold text-xs sm:text-sm">Transferred At</th>
                                                <th className="py-2 px-2 sm:px-3 md:px-6 border text-left font-semibold text-xs sm:text-sm">Transfer Type</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {history.length > 0 ? (
                                                history.map((entry, idx) => (
                                                    <tr key={idx} className="hover:bg-gray-50">
                                                        <td className="py-2 px-2 sm:px-3 md:px-6 border">
                                                            <div className="flex flex-col">
                                                                <span className="font-medium text-xs sm:text-sm">{entry.previous_owner?.first_name} {entry.previous_owner?.last_name}</span>
                                                                <span className="text-gray-500 text-xs">{entry.previous_owner?.email}</span>
                                                            </div>
                                                        </td>
                                                        <td className="py-2 px-2 sm:px-3 md:px-6 border">
                                                            <div className="flex flex-col">
                                                                {entry.transfer_type === 'external' ? (
                                                                    <span className="font-medium text-xs sm:text-sm text-gray-400">Non-member</span>
                                                                ) : (
                                                                    <>
                                                                        <span className="font-medium text-xs sm:text-sm">{entry.new_owner?.first_name} {entry.new_owner?.last_name}</span>
                                                                        <span className="text-gray-500 text-xs">{entry.new_owner?.email}</span>
                                                                    </>
                                                                )}
                                                            </div>
                                                        </td>
                                                        <td className="py-2 px-2 sm:px-3 md:px-6 border">
                                                            <div className="flex flex-col">
                                                                <span className="font-medium text-xs sm:text-sm">{new Date(entry.created_at).toLocaleDateString()}</span>
                                                                <span className="text-gray-500 text-xs">{new Date(entry.created_at).toLocaleTimeString()}</span>
                                                            </div>
                                                        </td>
                                                        <td className="py-2 px-2 sm:px-3 md:px-6 border">
                                                            <span className="font-medium text-xs sm:text-sm capitalize">{entry.transfer_type}</span>
                                                        </td>
                                                    </tr>
                                                ))
                                            ) : (
                                                <tr>
                                                    <td colSpan="4" className="py-6 sm:py-8 px-3 sm:px-6 text-center text-gray-500 text-sm sm:text-base">
                                                        No ownership history available
                                                    </td>
                                                </tr>
                                            )}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </Card>
                    </div>
                )}
            </div>
        </AppLayout>
    );
}
