# Error Handling System Documentation

This document describes the comprehensive error handling system implemented for the MOAMS application.

## Overview

The error handling system provides:
- Graceful error handling for both backend and frontend errors
- User-friendly error messages that hide technical details
- Comprehensive error logging and monitoring
- Automatic retry mechanisms for recoverable errors
- Error recovery strategies including circuit breakers and fallbacks

## Components

### 1. Backend Error Handling

#### Exception Handler (`app/Exceptions/Handler.php`)
- Enhanced to provide user-friendly error messages
- Categorizes errors by type (payment, file upload, database, etc.)
- Includes error recovery information (retryable, recoverable)
- Generates unique error IDs for tracking

#### Custom Exception Classes
- `PaymentException` - For payment-related errors
- `FileUploadException` - For file upload errors  
- `BusinessLogicException` - For business rule violations

#### Error Monitoring Service (`app/Services/ErrorMonitoringService.php`)
- Comprehensive error logging with context
- Error frequency tracking
- Error statistics and monitoring

#### Error Recovery Service (`app/Services/ErrorRecoveryService.php`)
- Retry mechanisms with exponential backoff
- Circuit breaker pattern for external services
- Fallback mechanisms
- Database transaction retries

### 2. Frontend Error Handling

#### Error Boundary (`resources/js/components/ErrorBoundary.jsx`)
- Catches JavaScript errors in React components
- Provides user-friendly error UI
- Logs errors to backend monitoring service
- Includes retry and recovery options

#### Error Handler Hook (`resources/js/hooks/useErrorHandler.js`)
- Custom hook for error handling in functional components
- Automatic error logging
- Retry mechanisms
- User-friendly error messages

#### Higher-Order Component (`resources/js/components/withErrorBoundary.jsx`)
- Wraps components with error boundaries
- Easy to use for protecting individual components

## Usage Examples

### Backend Usage

#### Using the HandlesErrors Trait in Controllers

```php
use App\Traits\HandlesErrors;

class MyController extends Controller
{
    use HandlesErrors;
    
    public function store(Request $request)
    {
        return $this->executeWithErrorHandling(
            operation: function() use ($request) {
                // Your operation here
                return Model::create($request->validated());
            },
            successMessage: 'Record created successfully',
            errorMessage: 'Failed to create record'
        );
    }
    
    public function uploadFile(Request $request)
    {
        return $this->executeFileOperation(
            fileOperation: function() use ($request) {
                return $request->file('upload')->store('uploads');
            },
            successMessage: 'File uploaded successfully',
            errorMessage: 'File upload failed'
        );
    }
}
```

#### Using Custom Exceptions

```php
// Payment error
throw PaymentException::initiationFailed('ctechpay', 'Invalid API key');

// File upload error
throw FileUploadException::fileTooLarge('document.pdf', 10485760, 5242880);

// Business logic error
throw BusinessLogicException::duplicateRecord('driver', ['license' => 'ABC123']);
```

#### Using Error Recovery Service

```php
use App\Services\ErrorRecoveryService;

// Retry with exponential backoff
$result = ErrorRecoveryService::retry(function() {
    return $this->callExternalAPI();
}, maxAttempts: 3);

// Circuit breaker for external service
$result = ErrorRecoveryService::circuitBreaker('payment_gateway', function() {
    return $this->processPayment();
});

// With fallback mechanism
$result = ErrorRecoveryService::withFallback(
    primary: function() { return $this->getPrimaryData(); },
    fallback: function() { return $this->getCachedData(); }
);
```

### Frontend Usage

#### Using Error Boundary

```jsx
import ErrorBoundary from '@/components/ErrorBoundary';

function MyComponent() {
    return (
        <ErrorBoundary>
            <SomeComponentThatMightError />
        </ErrorBoundary>
    );
}
```

#### Using withErrorBoundary HOC

```jsx
import withErrorBoundary from '@/components/withErrorBoundary';

const MyComponent = () => {
    // Component that might throw errors
    return <div>My Component</div>;
};

export default withErrorBoundary(MyComponent);
```

#### Using useErrorHandler Hook

```jsx
import { useErrorHandler } from '@/hooks/useErrorHandler';

function MyComponent() {
    const { executeAsync, handleError, error, clearError } = useErrorHandler();
    
    const handleSubmit = async () => {
        try {
            await executeAsync(async () => {
                const response = await fetch('/api/data');
                if (!response.ok) throw new Error('Failed to fetch');
                return response.json();
            });
        } catch (error) {
            // Error is automatically logged and handled
            console.error('Operation failed:', error);
        }
    };
    
    return (
        <div>
            {error && (
                <div className="error">
                    {error.message}
                    <button onClick={clearError}>Dismiss</button>
                </div>
            )}
            <button onClick={handleSubmit}>Submit</button>
        </div>
    );
}
```

## Error Monitoring

### Accessing Error Monitoring Dashboard
- Available at `/admin/error-monitoring` for admin users
- Shows error statistics, recent errors, and trends
- Provides error frequency analysis

### API Endpoints
- `GET /api/errors/javascript/stats` - JavaScript error statistics
- `GET /api/errors/javascript/recent` - Recent JavaScript errors
- `POST /api/errors/javascript` - Log JavaScript error
- `DELETE /api/errors/javascript` - Clear JavaScript error data

### Testing Error Handling
In local environment, you can test different error types:
- `/admin/error-monitoring/test?type=payment`
- `/admin/error-monitoring/test?type=file_upload`
- `/admin/error-monitoring/test?type=database`

## Configuration

### Environment Variables
- `APP_DEBUG` - Controls whether technical error details are shown
- `LOG_LEVEL` - Controls logging verbosity

### Error Types
The system recognizes these error types:
- `payment` - Payment processing errors
- `file_upload` - File upload errors
- `database` - Database connection/query errors
- `network` - Network connectivity errors
- `validation` - Input validation errors
- `unauthorized` - Authentication errors
- `forbidden` - Authorization errors
- `not_found` - Resource not found errors
- `business_logic` - Business rule violations

## Best Practices

1. **Always use user-friendly error messages** - Never expose technical details to end users
2. **Log errors with context** - Include relevant information for debugging
3. **Implement retry logic for transient errors** - Network issues, database timeouts, etc.
4. **Use circuit breakers for external services** - Prevent cascading failures
5. **Provide recovery options** - Give users ways to resolve or work around errors
6. **Monitor error frequency** - Set up alerts for high error rates
7. **Test error scenarios** - Regularly test error handling paths

## Troubleshooting

### Common Issues
1. **Error boundary not catching errors** - Make sure the error boundary wraps the problematic component
2. **JavaScript errors not being logged** - Check CSRF token and network connectivity
3. **Circuit breaker stuck open** - Use the reset endpoint or wait for timeout
4. **High error frequency alerts** - Check recent deployments or external service status

### Debugging
- Check browser console for JavaScript errors
- Review Laravel logs for backend errors
- Use error monitoring dashboard for trends
- Check error IDs in user reports for specific incidents
