import{j as e,C as r,a,b as l,h as x,c as d,B as i,L as c,T as n,R as h}from"./app-DL-qYY5V.js";import{A as o}from"./app-layout-YqstQnqE.js";import{C as j}from"./circle-x-DliR3-rL.js";import"./index-1tPPImJd.js";import"./index-BTzg1GwG.js";import"./app-logo-icon-BnXlkpcX.js";import"./bus-CKTBKI0T.js";import"./dollar-sign-Cx0-nQIX.js";import"./users-DNGXY-sJ.js";import"./clipboard-list-CxRumT8v.js";function C({payment:s,associationClerk:t}){const m=[{title:"Dashboard",href:"/dashboard"},{title:"My Membership",href:"/my-membership"},{title:"Payment Failed",href:"#"}];return e.jsx(o,{title:"Payment Failed",breadcrumbs:m,children:e.jsxs("div",{className:"max-w-2xl mx-auto py-8 px-4",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("div",{className:"flex justify-center mb-4",children:e.jsx("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center",children:e.jsx(j,{className:"h-10 w-10 text-red-600"})})}),e.jsx("h1",{className:"text-3xl font-bold text-red-700 mb-2",children:"Payment Failed"}),e.jsx("p",{className:"text-gray-600",children:"We were unable to process your payment"})]}),e.jsxs(r,{className:"mb-6 border-red-200 bg-red-50",children:[e.jsx(a,{children:e.jsxs(l,{className:"flex items-center gap-2 text-red-800",children:[e.jsx(x,{className:"h-5 w-5"}),"Payment Details"]})}),e.jsxs(d,{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Amount"}),e.jsxs("div",{className:"font-semibold text-red-800",children:["MWK ",parseFloat(s.amount).toLocaleString()]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Fee Type"}),e.jsx("div",{className:"font-semibold text-red-800 capitalize",children:s.fee_type})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Payment Method"}),e.jsx("div",{className:"font-semibold text-red-800",children:s.payment_method==="ctechpay_card"?"Card Payment":s.payment_method})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Attempted At"}),e.jsx("div",{className:"font-semibold text-red-800",children:new Date(s.created_at).toLocaleString()})]})]}),s.ctechpay_error_message&&e.jsxs("div",{className:"pt-4 border-t border-red-200",children:[e.jsx("div",{className:"text-sm text-gray-600 mb-2",children:"Error Details"}),e.jsx("div",{className:"bg-red-100 border border-red-200 rounded-lg p-3",children:e.jsx("p",{className:"text-red-800 text-sm",children:s.ctechpay_error_message})})]}),s.ctechpay_order_reference&&e.jsxs("div",{className:"pt-4 border-t border-red-200",children:[e.jsx("div",{className:"text-sm text-gray-600 mb-2",children:"Reference"}),e.jsx("div",{className:"font-mono text-sm text-red-800",children:s.ctechpay_order_reference})]})]})]}),e.jsxs(r,{className:"mb-6",children:[e.jsx(a,{children:e.jsx(l,{children:"Common Reasons for Payment Failure"})}),e.jsx(d,{children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full mt-2 flex-shrink-0"}),e.jsxs("div",{className:"text-sm text-gray-700",children:[e.jsx("strong",{children:"Insufficient Funds:"})," Your account or card doesn't have enough balance"]})]}),e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full mt-2 flex-shrink-0"}),e.jsxs("div",{className:"text-sm text-gray-700",children:[e.jsx("strong",{children:"Card Declined:"})," Your bank or card issuer declined the transaction"]})]}),e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full mt-2 flex-shrink-0"}),e.jsxs("div",{className:"text-sm text-gray-700",children:[e.jsx("strong",{children:"Network Issues:"})," Temporary connectivity problems"]})]}),e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full mt-2 flex-shrink-0"}),e.jsxs("div",{className:"text-sm text-gray-700",children:[e.jsx("strong",{children:"Incorrect Details:"})," Wrong card number, expiry date, or CVV"]})]})]})})]}),e.jsxs(r,{className:"mb-6",children:[e.jsx(a,{children:e.jsx(l,{children:"What to Do Next"})}),e.jsx(d,{children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5",children:e.jsx("span",{className:"text-blue-600 text-sm font-semibold",children:"1"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:"Check Your Account"}),e.jsx("div",{className:"text-sm text-gray-600",children:"Ensure you have sufficient funds and your payment method is active"})]})]}),e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5",children:e.jsx("span",{className:"text-blue-600 text-sm font-semibold",children:"2"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:"Try Again"}),e.jsx("div",{className:"text-sm text-gray-600",children:"You can retry the payment with the same or different payment method"})]})]}),e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5",children:e.jsx("span",{className:"text-blue-600 text-sm font-semibold",children:"3"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:"Contact Support"}),e.jsx("div",{className:"text-sm text-gray-600",children:"If the problem persists, contact our support team for assistance"})]})]})]})})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[e.jsx(i,{asChild:!0,variant:"outline",children:e.jsx(c,{href:n("dashboard"),children:"Return to Dashboard"})}),e.jsx(i,{asChild:!0,className:"bg-blue-600 hover:bg-blue-700",children:e.jsxs(c,{href:n("my.membership"),children:[e.jsx(h,{className:"h-4 w-4 mr-2"}),"Try Again"]})})]}),e.jsx("div",{className:"mt-8 text-center text-sm text-gray-500",children:t?e.jsxs("p",{children:["Need help? Contact ",t.first_name," ",t.last_name," at"," ",e.jsx("a",{href:`mailto:${t.email}`,className:"text-blue-600 hover:underline",children:t.email})," ","or call ",t.phone_number]}):e.jsx("p",{children:"Need help? Contact MOAM support for assistance with your payment."})})]})})}export{C as default};
