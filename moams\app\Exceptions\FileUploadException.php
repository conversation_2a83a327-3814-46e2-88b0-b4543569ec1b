<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class FileUploadException extends Exception
{
    protected $fileName;
    protected $fileSize;
    protected $fileType;
    protected $errorCode;
    protected $userMessage;

    public function __construct(
        string $message = 'File upload failed',
        string $userMessage = 'There was an issue uploading your file. Please check the file size and format, then try again.',
        string $fileName = null,
        int $fileSize = null,
        string $fileType = null,
        string $errorCode = null,
        int $code = 0,
        \Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);

        $this->userMessage = $userMessage;
        $this->fileName = $fileName;
        $this->fileSize = $fileSize;
        $this->fileType = $fileType;
        $this->errorCode = $errorCode;
    }

    /**
     * Get the user-friendly message
     */
    public function getUserMessage(): string
    {
        return $this->userMessage;
    }

    /**
     * Get the file name
     */
    public function getFileName(): ?string
    {
        return $this->fileName;
    }

    /**
     * Get the file size
     */
    public function getFileSize(): ?int
    {
        return $this->fileSize;
    }

    /**
     * Get the file type
     */
    public function getFileType(): ?string
    {
        return $this->fileType;
    }

    /**
     * Get the error code
     */
    public function getErrorCode(): ?string
    {
        return $this->errorCode;
    }

    /**
     * Render the exception into an HTTP response.
     */
    public function render(Request $request): Response|\Illuminate\Http\JsonResponse
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'File Upload Error',
                'message' => $this->getUserMessage(),
                'file_name' => $this->getFileName(),
                'file_size' => $this->getFileSize(),
                'file_type' => $this->getFileType(),
                'error_code' => $this->getErrorCode(),
            ], 422);
        }

        return response()->view('errors.file-upload', [
            'message' => $this->getUserMessage(),
            'fileName' => $this->getFileName(),
            'fileSize' => $this->getFileSize(),
            'fileType' => $this->getFileType(),
        ], 422);
    }

    /**
     * Report the exception.
     */
    public function report(): void
    {
        \Log::error('File Upload Exception', [
            'message' => $this->getMessage(),
            'user_message' => $this->getUserMessage(),
            'file_name' => $this->getFileName(),
            'file_size' => $this->getFileSize(),
            'file_type' => $this->getFileType(),
            'error_code' => $this->getErrorCode(),
            'user_id' => auth()->id(),
            'trace' => $this->getTraceAsString(),
        ]);
    }

    /**
     * Create a file too large exception
     */
    public static function fileTooLarge(
        string $fileName,
        int $fileSize,
        int $maxSize
    ): self {
        $maxSizeMB = round($maxSize / 1024 / 1024, 1);
        $fileSizeMB = round($fileSize / 1024 / 1024, 1);

        return new self(
            "File {$fileName} is too large ({$fileSizeMB}MB). Maximum allowed size is {$maxSizeMB}MB.",
            "The file you selected is too large ({$fileSizeMB}MB). Please choose a file smaller than {$maxSizeMB}MB.",
            $fileName,
            $fileSize,
            null,
            'FILE_TOO_LARGE'
        );
    }

    /**
     * Create an invalid file type exception
     */
    public static function invalidFileType(
        string $fileName,
        string $fileType,
        array $allowedTypes
    ): self {
        $allowedTypesStr = implode(', ', $allowedTypes);

        return new self(
            "File {$fileName} has invalid type {$fileType}. Allowed types: {$allowedTypesStr}",
            "The file type '{$fileType}' is not allowed. Please upload a file with one of these formats: {$allowedTypesStr}",
            $fileName,
            null,
            $fileType,
            'INVALID_FILE_TYPE'
        );
    }

    /**
     * Create a storage failure exception
     */
    public static function storageFailed(
        string $fileName,
        string $reason = 'Storage operation failed'
    ): self {
        return new self(
            "Failed to store file {$fileName}: {$reason}",
            'Unable to save your file. Please try again or contact support if the problem persists.',
            $fileName,
            null,
            null,
            'STORAGE_FAILED'
        );
    }

    /**
     * Create a corrupted file exception
     */
    public static function corruptedFile(
        string $fileName
    ): self {
        return new self(
            "File {$fileName} appears to be corrupted or invalid",
            'The file you uploaded appears to be corrupted or invalid. Please try uploading a different file.',
            $fileName,
            null,
            null,
            'CORRUPTED_FILE'
        );
    }

    /**
     * Create a virus detected exception
     */
    public static function virusDetected(
        string $fileName
    ): self {
        return new self(
            "Virus detected in file {$fileName}",
            'The file you uploaded failed security checks. Please scan your file for viruses and try again.',
            $fileName,
            null,
            null,
            'VIRUS_DETECTED'
        );
    }
}
