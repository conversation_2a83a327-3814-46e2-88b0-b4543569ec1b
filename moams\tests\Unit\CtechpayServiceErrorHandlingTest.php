<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\CtechpayService;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Config;

class CtechpayServiceErrorHandlingTest extends TestCase
{
    protected CtechpayService $ctechpayService;

    protected function setUp(): void
    {
        parent::setUp();

        // Set up test configuration
        Config::set('services.ctechpay.api_url', 'https://api-sandbox.ctechpay.com');
        Config::set('services.ctechpay.token', 'test-token');
        Config::set('services.ctechpay.registration', 'TEST123');
        Config::set('services.ctechpay.environment', 'sandbox');
        Config::set('services.ctechpay.card_payment_endpoint', '/?endpoint=order');

        $this->ctechpayService = new CtechpayService();
    }

    /** @test */
    public function it_handles_dns_resolution_errors_gracefully()
    {
        // Mock HTTP to throw a cURL DNS error
        Http::fake(function () {
            throw new \Exception('cURL error 6: Could not resolve host: api-sandbox.ctechpay.com');
        });

        $paymentData = [
            'amount' => 1000,
            'redirect_url' => 'https://example.com/success',
            'cancel_url' => 'https://example.com/cancel',
        ];

        $result = $this->ctechpayService->createCardPayment($paymentData);

        $this->assertFalse($result['success']);
        $this->assertStringContainsString('Unable to connect to payment service', $result['error']);
        $this->assertStringNotContainsString('cURL error 6', $result['error']);
    }

    /** @test */
    public function it_handles_timeout_errors_gracefully()
    {
        Http::fake(function () {
            throw new \Exception('cURL error 28: Operation timed out');
        });

        $paymentData = [
            'amount' => 1000,
            'redirect_url' => 'https://example.com/success',
            'cancel_url' => 'https://example.com/cancel',
        ];

        $result = $this->ctechpayService->createCardPayment($paymentData);

        $this->assertFalse($result['success']);
        $this->assertStringContainsString('Payment request timed out', $result['error']);
    }

    /** @test */
    public function it_handles_http_400_errors_gracefully()
    {
        Http::fake([
            '*' => Http::response('Invalid request parameters', 400)
        ]);

        $paymentData = [
            'amount' => 1000,
            'redirect_url' => 'https://example.com/success',
            'cancel_url' => 'https://example.com/cancel',
        ];

        $result = $this->ctechpayService->createCardPayment($paymentData);

        $this->assertFalse($result['success']);
        $this->assertStringContainsString('Invalid payment information', $result['error']);
    }

    /** @test */
    public function it_handles_http_401_errors_gracefully()
    {
        Http::fake([
            '*' => Http::response('Unauthorized', 401)
        ]);

        $paymentData = [
            'amount' => 1000,
            'redirect_url' => 'https://example.com/success',
            'cancel_url' => 'https://example.com/cancel',
        ];

        $result = $this->ctechpayService->createCardPayment($paymentData);

        $this->assertFalse($result['success']);
        $this->assertStringContainsString('authentication failed', $result['error']);
    }

    /** @test */
    public function it_handles_http_500_errors_gracefully()
    {
        Http::fake([
            '*' => Http::response('Internal Server Error', 500)
        ]);

        $paymentData = [
            'amount' => 1000,
            'redirect_url' => 'https://example.com/success',
            'cancel_url' => 'https://example.com/cancel',
        ];

        $result = $this->ctechpayService->createCardPayment($paymentData);

        $this->assertFalse($result['success']);
        $this->assertStringContainsString('temporarily unavailable', $result['error']);
    }

    /** @test */
    public function it_handles_successful_payment_creation()
    {
        Http::fake([
            '*' => Http::response([
                'order_reference' => 'TEST123456',
                'payment_page_URL' => 'https://payment.ctechpay.com/pay/TEST123456',
                'status' => 'PENDING'
            ], 200)
        ]);

        $paymentData = [
            'amount' => 1000,
            'redirect_url' => 'https://example.com/success',
            'cancel_url' => 'https://example.com/cancel',
        ];

        $result = $this->ctechpayService->createCardPayment($paymentData);

        $this->assertTrue($result['success']);
        $this->assertEquals('TEST123456', $result['order_reference']);
        $this->assertEquals('https://payment.ctechpay.com/pay/TEST123456', $result['payment_page_url']);
    }

    /** @test */
    public function it_handles_ssl_connection_errors_gracefully()
    {
        Http::fake(function () {
            throw new \Exception('cURL error 35: SSL connect error');
        });

        $paymentData = [
            'amount' => 1000,
            'redirect_url' => 'https://example.com/success',
            'cancel_url' => 'https://example.com/cancel',
        ];

        $result = $this->ctechpayService->createCardPayment($paymentData);

        $this->assertFalse($result['success']);
        $this->assertStringContainsString('Secure connection to payment service failed', $result['error']);
    }

    /** @test */
    public function it_handles_connection_refused_errors_gracefully()
    {
        Http::fake(function () {
            throw new \Exception('cURL error 7: Failed to connect to api-sandbox.ctechpay.com');
        });

        $paymentData = [
            'amount' => 1000,
            'redirect_url' => 'https://example.com/success',
            'cancel_url' => 'https://example.com/cancel',
        ];

        $result = $this->ctechpayService->createCardPayment($paymentData);

        $this->assertFalse($result['success']);
        $this->assertStringContainsString('Payment service is unreachable', $result['error']);
    }
}
