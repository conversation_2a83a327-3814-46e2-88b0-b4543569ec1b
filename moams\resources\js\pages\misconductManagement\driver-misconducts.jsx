import AppLayout from '@/layouts/app-layout';
import { Card, CardHeader, CardTitle, CardContent } from '../../components/ui/card';

import { Button } from '../../components/ui/button';
import { Link, usePage } from '@inertiajs/react';
import { navigateToLogicalParent } from '@/utils/navigation';
import { ChevronLeft, AlertTriangle, Plus } from 'lucide-react';
import Pagination from '@/components/ui/pagination';
import { route } from 'ziggy-js';

export default function DriverMisconducts({ driver, misconducts }) {
    const { userRoles } = usePage().props;
    const isMinibusOwner = userRoles && userRoles.includes('minibus owner');
    const canCreate = isMinibusOwner;

    // Use all misconducts since pagination is handled server-side
    const displayedMisconducts = misconducts?.data || [];

    const breadcrumbs = [
        { title: 'Misconduct Management', href: route('misconducts.index') },
        { title: `${driver?.first_name || 'Unknown'} ${driver?.last_name || 'Driver'}`, href: route('drivers.misconducts', driver?.id) },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <div className="w-full max-w-full overflow-hidden">
                <div className="container mx-auto px-4 py-8">

                    {/* Header section */}
                    <div className="flex flex-col gap-4 mb-6">
                        <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
                            <button
                                className="flex items-center px-3 py-1 rounded hover:bg-gray-100 border border-gray-200 w-fit"
                                onClick={() => navigateToLogicalParent(breadcrumbs)}
                            >
                                <ChevronLeft className="h-4 w-4 mr-1" /> Back
                            </button>
                            <div className="flex-1 sm:flex-none">
                                <span className="text-muted-foreground text-sm sm:text-base font-medium break-words">
                                    Misconduct history for {driver.first_name} {driver.last_name}
                                </span>
                            </div>
                        </div>

                        {/* Action buttons row */}
                        <div className="flex flex-wrap items-center gap-2 justify-start sm:justify-end">
                            {canCreate && (
                                <Link href={route('misconducts.index')}>
                                    <Button className="w-fit flex items-center gap-2 bg-red-600 hover:bg-red-700 text-white px-4 py-2">
                                        <Plus className="h-4 w-4" />
                                        Report New Misconduct
                                    </Button>
                                </Link>
                            )}
                        </div>
                    </div>

                    {/* Misconduct Summary */}
                    <div className="mb-6">
                        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <h3 className="text-lg font-medium text-green-900">Misconduct Summary</h3>
                                    <p className="text-green-700">
                                        Total misconducts: {misconducts.total} |
                                        Showing: {displayedMisconducts.length} of {misconducts.total}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Misconducts Table Container - Fixed width that fits screen */}
                    <div
                        className="w-full border-2 border-blue-200 rounded-lg"
                        style={{
                            maxWidth: 'calc(100vw - 2rem)',
                            overflow: 'hidden'
                        }}
                    >
                        <Card className="w-full border-0 shadow-none">
                            <CardHeader className="px-0">
                                <CardTitle className="flex items-center gap-2">
                                    <AlertTriangle className="h-5 w-5" />
                                    Driver Misconducts ({displayedMisconducts.length})
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="p-0">
                                {displayedMisconducts.length === 0 ? (
                                    <div className="text-center py-8 px-6">
                                        <AlertTriangle className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                                        <h3 className="text-lg font-medium mb-2">No misconducts found for this driver</h3>
                                        <p className="text-muted-foreground mb-4">
                                            This driver has no recorded misconducts.
                                        </p>
                                    </div>
                                ) : (
                                    <div className="overflow-x-auto mb-8">
                                        <table className="min-w-full w-full bg-white border border-gray-200 rounded-lg">
                                            <thead>
                                                <tr className="bg-gray-100 text-gray-700">
                                                    <th className="px-4 py-3 text-left font-medium">Misconduct Type</th>
                                                    <th className="px-4 py-3 text-left font-medium">Date</th>
                                                    <th className="px-4 py-3 text-left font-medium">Description</th>
                                                    <th className="px-4 py-3 text-left font-medium">Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {displayedMisconducts.map(misconduct => (
                                                    <tr key={misconduct.id} className="border-b hover:bg-gray-50">
                                                        <td className="px-4 py-3 font-medium">
                                                            {misconduct.name}
                                                        </td>
                                                        <td className="px-4 py-3">
                                                            {new Date(misconduct.offense_date).toLocaleDateString()}
                                                        </td>
                                                        <td className="px-4 py-3 max-w-24">
                                                            <div className="truncate" title={misconduct.description || 'N/A'}>
                                                                {misconduct.description || 'N/A'}
                                                            </div>
                                                        </td>
                                                        <td className="px-4 py-3">
                                                            <div className="flex items-center gap-2">
                                                                <Link href={route('misconducts.show', misconduct.id)}>
                                                                    <Button variant="outline" size="sm" className="text-blue-700 border-blue-500 hover:bg-blue-50">
                                                                        View Details
                                                                    </Button>
                                                                </Link>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                ))}
                                            </tbody>
                                        </table>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>

                    {/* Pagination */}
                    {misconducts.total > 0 && misconducts.total > misconducts.per_page && (
                        <div className="mt-6 w-full">
                            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 px-4 py-4 border-t bg-gray-50/50 rounded-b-lg">
                                <div className="text-sm text-gray-700 text-center sm:text-left">
                                    Showing {misconducts.from} to {misconducts.to} of {misconducts.total} results
                                </div>
                                {misconducts.last_page > 1 && (
                                    <Pagination data={misconducts} />
                                )}
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </AppLayout>
    );
}