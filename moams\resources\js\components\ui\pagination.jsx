import React from 'react';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { router } from '@inertiajs/react';

export default function Pagination({ data }) {
    // Don't show pagination if no data or no results at all
    if (!data || data.total === 0) {
        return null;
    }

    // Only show pagination if there are multiple pages
    if (data.last_page <= 1) {
        return null;
    }

    return (
        <div className="flex items-center justify-between px-6 py-4 border-t">
            <div className="text-sm text-gray-700">
                Showing {data.from} to {data.to} of {data.total} results
            </div>
            <div className="flex items-center space-x-2">
                <Button
                    variant="outline"
                    size="sm"
                    onClick={() => router.get(data.prev_page_url)}
                    disabled={!data.prev_page_url}
                    className="flex items-center gap-1"
                >
                    <ChevronLeft className="h-4 w-4" />
                    Previous
                </Button>

                <span className="text-sm text-gray-600">
                    Page {data.current_page} of {data.last_page}
                </span>

                <Button
                    variant="outline"
                    size="sm"
                    onClick={() => router.get(data.next_page_url)}
                    disabled={!data.next_page_url}
                    className="flex items-center gap-1"
                >
                    Next
                    <ChevronRight className="h-4 w-4" />
                </Button>
            </div>
        </div>
    );
}
