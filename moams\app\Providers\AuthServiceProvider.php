<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;
use App\Models\Minibus;
use App\Models\Driver;
use App\Policies\MinibusPolicy;
use App\Policies\DriverPolicy;
use App\Models\Misconduct;
use App\Policies\MisconductPolicy;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        Minibus::class => MinibusPolicy::class,
        Driver::class => DriverPolicy::class,
        Misconduct::class => MisconductPolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        $this->registerPolicies();
    }
}