<!DOCTYPE html>
<html>
<head>
    <title>Test Payment Direct</title>
</head>
<body>
    <h1>Test Payment Direct</h1>
    <button onclick="testPayment()">Test Payment</button>
    <div id="result"></div>

    <script>
        async function testPayment() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing payment...';

            try {
                const response = await fetch('http://127.0.0.1:8000/test-payment-initiate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: 1,
                        membership_id: 1,
                        fee_type: 'registration',
                        amount: 2500
                    })
                });

                console.log('Response status:', response.status);
                const data = await response.json();
                console.log('Response data:', data);

                resultDiv.innerHTML = `
                    <h3>Response Status: ${response.status}</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;

                if (data.payment_page_url) {
                    resultDiv.innerHTML += `<p><a href="${data.payment_page_url}" target="_blank">Open Payment Page</a></p>`;
                }

            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = `<h3>Error:</h3><pre>${error.message}</pre>`;
            }
        }
    </script>
</body>
</html>
