import { useState, useCallback } from 'react';
import { router } from '@inertiajs/react';

/**
 * Custom hook for handling errors in functional components
 */
export const useErrorHandler = () => {
    const [error, setError] = useState(null);
    const [isRetrying, setIsRetrying] = useState(false);
    const [retryCount, setRetryCount] = useState(0);

    /**
     * Log error to monitoring service
     */
    const logError = useCallback(async (error, context = {}) => {
        const errorId = `hook_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        try {
            const errorData = {
                error_id: errorId,
                type: 'javascript_hook_error',
                message: error.message,
                stack: error.stack,
                url: window.location.href,
                user_agent: navigator.userAgent,
                timestamp: new Date().toISOString(),
                context,
            };

            await fetch('/api/errors/javascript', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'),
                },
                body: JSON.stringify(errorData),
            });

            return errorId;
        } catch (logError) {
            console.error('Failed to log error:', logError);
            return errorId;
        }
    }, []);

    /**
     * Handle error with automatic logging
     */
    const handleError = useCallback(async (error, context = {}) => {
        console.error('Error handled by useErrorHandler:', error);
        
        const errorId = await logError(error, context);
        
        setError({
            ...error,
            errorId,
            context,
            timestamp: new Date().toISOString(),
        });

        return errorId;
    }, [logError]);

    /**
     * Clear error state
     */
    const clearError = useCallback(() => {
        setError(null);
        setRetryCount(0);
    }, []);

    /**
     * Retry a failed operation
     */
    const retry = useCallback(async (operation, maxRetries = 3) => {
        if (retryCount >= maxRetries) {
            throw new Error(`Maximum retry attempts (${maxRetries}) exceeded`);
        }

        setIsRetrying(true);
        setRetryCount(prev => prev + 1);

        try {
            const result = await operation();
            clearError();
            return result;
        } catch (error) {
            await handleError(error, { 
                operation: 'retry',
                attempt: retryCount + 1,
                maxRetries 
            });
            throw error;
        } finally {
            setIsRetrying(false);
        }
    }, [retryCount, handleError, clearError]);

    /**
     * Execute an async operation with error handling
     */
    const executeAsync = useCallback(async (operation, context = {}) => {
        try {
            clearError();
            return await operation();
        } catch (error) {
            await handleError(error, { ...context, operation: 'executeAsync' });
            throw error;
        }
    }, [handleError, clearError]);

    /**
     * Execute an async operation with retry logic
     */
    const executeWithRetry = useCallback(async (operation, maxRetries = 3, context = {}) => {
        let lastError;
        
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                clearError();
                return await operation();
            } catch (error) {
                lastError = error;
                
                if (attempt === maxRetries) {
                    await handleError(error, { 
                        ...context, 
                        operation: 'executeWithRetry',
                        finalAttempt: true,
                        totalAttempts: attempt 
                    });
                    throw error;
                } else {
                    console.warn(`Attempt ${attempt} failed, retrying...`, error);
                    // Wait before retrying (exponential backoff)
                    await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt - 1) * 1000));
                }
            }
        }
        
        throw lastError;
    }, [handleError, clearError]);

    /**
     * Handle Inertia form errors
     */
    const handleInertiaError = useCallback((error) => {
        if (error.response?.status === 422) {
            // Validation errors - don't log these as they're user errors
            return;
        }
        
        handleError(new Error(error.message || 'Inertia request failed'), {
            type: 'inertia_error',
            status: error.response?.status,
            url: error.config?.url,
        });
    }, [handleError]);

    /**
     * Show user-friendly error message
     */
    const showErrorMessage = useCallback((message, type = 'error') => {
        // You can integrate this with your notification system
        // For now, we'll use a simple alert or integrate with Inertia flash messages
        if (typeof window !== 'undefined') {
            // If you have a toast notification system, use it here
            console.error(`${type.toUpperCase()}: ${message}`);
            
            // Or redirect with flash message
            router.reload({
                data: { flash: { [type]: message } },
                preserveScroll: true,
            });
        }
    }, []);

    /**
     * Get user-friendly error message
     */
    const getUserFriendlyMessage = useCallback((error) => {
        if (typeof error === 'string') return error;
        
        // Network errors
        if (error.name === 'NetworkError' || error.message.includes('fetch')) {
            return 'Network connection error. Please check your internet connection and try again.';
        }
        
        // Timeout errors
        if (error.name === 'TimeoutError' || error.message.includes('timeout')) {
            return 'The request timed out. Please try again.';
        }
        
        // Permission errors
        if (error.message.includes('permission') || error.message.includes('unauthorized')) {
            return 'You do not have permission to perform this action.';
        }
        
        // Validation errors
        if (error.message.includes('validation') || error.message.includes('invalid')) {
            return 'Please check your input and try again.';
        }
        
        // Generic fallback
        return 'An unexpected error occurred. Please try again or contact support if the problem persists.';
    }, []);

    return {
        error,
        isRetrying,
        retryCount,
        handleError,
        clearError,
        retry,
        executeAsync,
        executeWithRetry,
        handleInertiaError,
        showErrorMessage,
        getUserFriendlyMessage,
        hasError: !!error,
    };
};

export default useErrorHandler;
