import React, { useCallback, useState } from 'react';
import { useF<PERSON>, <PERSON> } from '@inertiajs/react';
import { navigateToLogicalParent } from '@/utils/navigation';
import { ArrowLeft, LoaderCircle } from 'lucide-react';
import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import InputError from '@/components/input-error';
import { MinibusOwnerCombobox } from '@/components/ui/minibus-owner-combobox';
import { RouteCombobox } from '@/components/ui/route-combobox';

export default function EditMinibus({ minibus = {}, minibusOwners = [], userRole }) {
    const owners = minibusOwners || [];

    if (!minibus || !minibus.id) {
        return (
            <AppLayout>
                <div className="p-8">
                    <div className="text-center">
                        <h2 className="text-xl font-semibold text-red-600">Error Loading Minibus</h2>
                        <p className="mt-2">Unable to load minibus details. Please try again.</p>
                        <Button variant="outline" onClick={() => window.history.back()} className="w-full sm:w-auto">
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Back
                        </Button>
                    </div>
                </div>
            </AppLayout>
        );
    }

    const breadcrumbs = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: userRole === 'minibus owner' ? 'My Minibuses' : 'Minibus Management', href: '/minibuses' },
        { title: `Edit ${minibus.number_plate}`, href: `/minibuses/${minibus.id}/edit` },
    ];

    const [selectedOwner, setSelectedOwner] = useState(minibusOwners.find(o => String(o.id) === String(minibus.owner_id)) || null);
    const [selectedRoute, setSelectedRoute] = useState(null);
    const { data, setData, put, processing, errors } = useForm({
        number_plate: minibus.number_plate || '',
        make: minibus.make || '',
        model: minibus.model || '',
        year_of_make: minibus.year_of_make || '',
        main_colour: minibus.main_colour || '',
        assigned_route: minibus.assigned_route || '',
        owner_id: minibus.owner_id || '',
        route_id: minibus.route_id || '',
    });

    const handleChange = (e) => {
        const { id, value } = e.target;
        setData(id, value);
    };

    const submit = (e) => {
        e.preventDefault();
        put(route('minibuses.update', minibus.id));
    };

    const fetchOwners = useCallback(async (search) => {
        const res = await fetch(`/api/minibus-owners?search=${encodeURIComponent(search)}`);
        return (await res.json()).filter(owner => owner.id !== null && owner.id !== undefined && owner.id !== "");
    }, []);

    const fetchRoutes = useCallback(async (search) => {
        const res = await fetch(`/api/vehicle-routes?search=${encodeURIComponent(search)}`);
        return await res.json();
    }, []);

    const years = Array.from({ length: 45 }, (_, i) => new Date().getFullYear() - i);

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <div className="p-8">
                {/* Header */}
                <div className='flex justify-between items-center mb-8'>
                    <div className="flex items-center space-x-4">
                        <Button variant="outline" onClick={() => navigateToLogicalParent(breadcrumbs)} className="w-full sm:w-auto">
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Back
                        </Button>
                        <div>
                            <p className="text-gray-600 dark:text-gray-400 mt-1">
                                Edit minibus details for the minibus owner
                            </p>
                        </div>
                    </div>
                </div>
                <div className="max-w-2xl">
                    <form className="space-y-6" onSubmit={submit}>
                        <div>
                            <Label htmlFor="number_plate">Number Plate</Label>
                            <Input
                                id="number_plate"
                                type="text"
                                value={data.number_plate}
                                onChange={handleChange}
                                disabled={processing}
                            />
                            <InputError message={errors.number_plate} className="mt-1" />
                        </div>
                        <div>
                            <Label htmlFor="make">Make</Label>
                            <Input
                                id="make"
                                type="text"
                                value={data.make}
                                onChange={handleChange}
                                disabled={processing}
                                placeholder="e.g. Toyota"
                            />
                            <InputError message={errors.make} className="mt-1" />
                        </div>
                        <div>
                            <Label htmlFor="model">Model</Label>
                            <Input
                                id="model"
                                type="text"
                                value={data.model}
                                onChange={handleChange}
                                disabled={processing}
                                placeholder="e.g. Hiace"
                            />
                            <InputError message={errors.model} className="mt-1" />
                        </div>
                        <div>
                            <Label htmlFor="year_of_make">Year of Make</Label>
                            <select
                                id="year_of_make"
                                value={data.year_of_make}
                                onChange={handleChange}
                                disabled={processing}
                                className="w-full border rounded px-3 py-2"
                            >
                                <option value="">Select year</option>
                                {years.map((year) => (
                                    <option key={year} value={year}>{year}</option>
                                ))}
                            </select>
                            <InputError message={errors.year_of_make} className="mt-1" />
                        </div>
                        <div>
                            <Label htmlFor="main_colour">Main Colour</Label>
                            <Input
                                id="main_colour"
                                type="text"
                                value={data.main_colour}
                                onChange={handleChange}
                                disabled={processing}
                                placeholder="e.g. White/Blue"
                            />
                            <InputError message={errors.main_colour} className="mt-1" />
                        </div>
                        <div>
                            <Label htmlFor="route_id">Assigned Route</Label>
                            <RouteCombobox
                                value={selectedRoute}
                                onChange={route => {
                                    setSelectedRoute(route);
                                    setData('route_id', route?.id || '');
                                }}
                                fetchRoutes={fetchRoutes}
                                placeholder="Select route..."
                            />
                            <InputError message={errors.route_id} className="mt-1" />
                        </div>
                        {userRole === 'association clerk' && minibus.archived && (
                            <div>
                                <Label htmlFor="owner_id">Owner *</Label>
                                <MinibusOwnerCombobox
                                    value={selectedOwner || null}
                                    onChange={owner => {
                                        setSelectedOwner(owner);
                                        setData('owner_id', owner?.id || '');
                                    }}
                                    fetchOwners={fetchOwners}
                                    placeholder="Select minibus owner..."
                                />
                                <p className="mt-2 text-sm text-amber-600">
                                    This minibus was previously archived. Please select a new owner to complete the unarchiving process.
                                </p>
                                {errors.owner_id && (
                                    <p className="text-sm text-red-500">{errors.owner_id}</p>
                                )}
                            </div>
                        )}
                        <div className="flex space-x-4 pt-6">
                            <Button
                                type="submit"
                                className="bg-blue-600 hover:bg-blue-700"
                                disabled={processing}
                            >
                                {processing && <LoaderCircle className="h-4 w-4 animate-spin mr-2" />}
                                Save changes
                            </Button>
                            <Link href="/minibuses">
                                <Button type="button" variant="outline">
                                    Cancel
                                </Button>
                            </Link>
                        </div>
                    </form>
                </div>
            </div>
        </AppLayout>
    );
}

