import { NavFooter } from '@/components/nav-footer';
import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { Link, usePage } from '@inertiajs/react';
import { BookUser, Waves, MessageCircleQuestion, House, CreditCard, Shield, Users, Bus, UserCheck, AlertTriangle, DollarSign, Map, Activity } from 'lucide-react';
import AppLogo from './app-logo';

const mainNavItems = [
    {
        title: 'Dashboard',
        url: '/dashboard',
        icon: House,
    },/*
    {
        title: 'Membership',
        url: '/membership',
        icon: BookUser,
    },
    {
        title: 'Payments',
        url: '/payments',
        icon: CreditCard,
    },
    {
        title: 'Complain<PERSON>',
        url: '/complaints',
        icon: MessageCircleQuestion,
    },
    {
        title: 'Offenses',
        url: '/offenses',
        icon: Waves,
    },*/
    {
        title: 'Minibus Management',
        url: '/minibuses',
        icon: Bus,
        role: ['association clerk', 'minibus owner'],
    },
    {
        title: 'Route Management',
        url: '/routes',
        icon: Map,
        role: ['association clerk', 'association manager'],
    },
    {
        title: 'Membership Management',
        url: '/membership-management',
        icon: BookUser,
        role: ['association clerk'],
    },
    {
        title: 'Fee Management',
        url: '/fee-settings',
        icon: DollarSign,
        role: ['association manager'],
    },
    {
        title: 'My Membership',
        url: '/my-membership',
        icon: BookUser,
        role: ['minibus owner'],
    },
    {
        title: 'Driver Management',
        url: '/drivers',
        icon: UserCheck,
        role: ['association clerk', 'minibus owner'],
    },
    {
        title: 'Misconduct Management',
        url: '/misconducts',
        icon: AlertTriangle,
        role: ['association clerk', 'association manager', 'minibus owner'],
    },
    {
        title: 'User Management',
        url: '/admin/users',
        icon: Users,
        role: 'system admin',
    },
    /*{
        title: 'Error Monitoring',
        url: '/admin/error-monitoring',
        icon: Activity,
        role: 'system admin',
    },/*
    {
        title: 'Role Management',
        url: '/users',
        icon: Shield,
        role: 'system admin',
    },*/
];

export function AppSidebar() {
    const { auth } = usePage().props;
    const pageProps = usePage().props;
    const page = usePage();

    // Get roles from pageProps first (more reliable), then fallback to auth user
    let roles = [];

    if (pageProps.userRoles && Array.isArray(pageProps.userRoles)) {
        roles = pageProps.userRoles;
    } else if (auth?.user?.roles) {
        // If roles is an array of role objects, extract the names
        if (Array.isArray(auth.user.roles)) {
            roles = auth.user.roles.map(role => typeof role === 'string' ? role : role.name);
        }
    }

    const isSystemAdmin = roles.includes('system admin');

    // Filter nav items based on user role
    const filteredNavItems = mainNavItems
        .filter(item => {
            if (item.role) {
                if (Array.isArray(item.role)) {
                    return item.role.some(r => roles.includes(r));
                }
                return roles.includes(item.role);
            }
            return true;
        })
        .map(item => {
            // Change label for minibus owners only
            if (item.url === '/minibuses' && roles.includes('minibus owner')) {
                return { ...item, title: 'My Minibuses' };
            }
            if (item.url === '/drivers' && roles.includes('minibus owner')) {
                return { ...item, title: 'My Drivers' };
            }
            return item;
        });

    return (
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu >
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href="/dashboard" prefetch>
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>
            <hr />
            <SidebarContent>
                <NavMain items={filteredNavItems} />
            </SidebarContent>

            <SidebarFooter>
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}
