import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { User, Bus, Users, AlertTriangle, ClipboardList, <PERSON>geCheck, DollarSign } from 'lucide-react';

export default function ClerkDashboard({ user = {}, stats = {}, misconducts = 0, clearanceRequests = 0, transferRequests = 0, currentFees = {} }) {
    return (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 p-4">
            {/* Personal Details */}
            <Card className="shadow-md">
                <CardHeader>
                    <CardTitle className="flex items-center gap-2"><User className="h-5 w-5 text-blue-600" /> Personal Details</CardTitle>
                </CardHeader>
                <CardContent>
                    <p className="text-gray-500 mb-2">Your basic profile information.</p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div><span className="font-semibold">First Name:</span> {user.first_name || 'N/A'}</div>
                        <div><span className="font-semibold">Last Name:</span> {user.last_name || 'N/A'}</div>
                        <div><span className="font-semibold">Gender:</span> {user.gender || 'N/A'}</div>
                        <div><span className="font-semibold">District:</span> {user.district || 'N/A'}</div>
                    </div>
                </CardContent>
            </Card>

            {/* Minibus Statistics */}
            <Card className="shadow-md">
                <CardHeader>
                    <CardTitle className="flex items-center gap-2"><Bus className="h-5 w-5 text-blue-600" /> Minibus Statistics</CardTitle>
                </CardHeader>
                <CardContent>
                    <p className="text-gray-500 mb-2">Overview of all registered minibuses.</p>
                    <div className="flex gap-8">
                        <div className="text-center"><div className="text-xl font-bold">{stats.minibuses?.all ?? 0}</div><div className="text-gray-600">All</div></div>
                        <div className="text-center"><div className="text-xl font-bold text-green-600">{stats.minibuses?.active ?? 0}</div><div className="text-gray-600">Active</div></div>
                        <div className="text-center"><div className="text-xl font-bold text-red-600">{stats.minibuses?.inactive ?? 0}</div><div className="text-gray-600">Inactive</div></div>
                    </div>
                </CardContent>
            </Card>

            {/* Driver Statistics */}
            <Card className="shadow-md">
                <CardHeader>
                    <CardTitle className="flex items-center gap-2"><Users className="h-5 w-5 text-blue-600" /> Driver Statistics</CardTitle>
                </CardHeader>
                <CardContent>
                    <p className="text-gray-500 mb-2">Summary of all drivers in the system.</p>
                    <div className="flex gap-8">
                        <div className="text-center"><div className="text-xl font-bold">{stats.drivers?.all ?? 0}</div><div className="text-gray-600">All</div></div>
                        <div className="text-center"><div className="text-xl font-bold text-green-600">{stats.drivers?.active ?? 0}</div><div className="text-gray-600">Active</div></div>
                        <div className="text-center"><div className="text-xl font-bold text-red-600">{stats.drivers?.inactive ?? 0}</div><div className="text-gray-600">Inactive</div></div>
                    </div>
                </CardContent>
            </Card>

            {/* Members & Memberships */}
            <Card className="shadow-md">
                <CardHeader>
                    <CardTitle className="flex items-center gap-2"><BadgeCheck className="h-5 w-5 text-blue-600" /> Members & Memberships</CardTitle>
                </CardHeader>
                <CardContent>
                    <p className="text-gray-500 mb-2">Membership status and unpaid memberships.</p>
                    <div className="flex gap-8">
                        <div className="text-center"><div className="text-xl font-bold">{stats.members ?? 0}</div><div className="text-gray-600">All Members</div></div>
                        <div className="text-center"><div className="text-xl font-bold text-yellow-600">{stats.memberships?.unpaid ?? 0}</div><div className="text-gray-600">Unpaid memberships</div></div>
                    </div>
                </CardContent>
            </Card>

            {/* Misconducts */}
            <Card className="shadow-md">
                <CardHeader>
                    <CardTitle className="flex items-center gap-2"><AlertTriangle className="h-5 w-5 text-yellow-600" /> Misconducts</CardTitle>
                </CardHeader>
                <CardContent>
                    <p className="text-gray-500 mb-2">Reported misconducts in the association.</p>
                    <div className="text-center"><span className="text-xl font-bold text-yellow-600">{misconducts ?? 0}</span></div>
                </CardContent>
            </Card>

            {/* Pending Requests */}
            <Card className="shadow-md">
                <CardHeader>
                    <CardTitle className="flex items-center gap-2"><ClipboardList className="h-5 w-5 text-blue-600" /> Pending Requests</CardTitle>
                </CardHeader>
                <CardContent>
                    <p className="text-gray-500 mb-2">Requests awaiting action.</p>
                    <div className="flex gap-8">
                        <div className="text-center"><div className="text-xl font-bold text-blue-600">{clearanceRequests ?? 0}</div><div className="text-gray-600">Clearance</div></div>
                        <div className="text-center"><div className="text-xl font-bold text-blue-600">{transferRequests ?? 0}</div><div className="text-gray-600">Transfer</div></div>
                    </div>
                </CardContent>
            </Card>

            {/* Current Fees */}
            {/* <Card className="shadow-md">
                <CardHeader>
                    <CardTitle className="flex items-center gap-2"><DollarSign className="h-5 w-5 text-green-600" /> Current Fees</CardTitle>
                </CardHeader>
                <CardContent>
                    <p className="text-gray-500 mb-2">Below are the current registration and affiliation fees set by the association.</p>
                    <div className="flex gap-8">
                        <div className="text-center">
                            <div className="text-xl font-bold text-green-700">{currentFees?.registration ? `MK ${currentFees.registration}` : 'N/A'}</div>
                            <div className="text-gray-600">Registration Fee</div>
                        </div>
                        <div className="text-center">
                            <div className="text-xl font-bold text-green-700">{currentFees?.affiliation ? `MK ${currentFees.affiliation}` : 'N/A'}</div>
                            <div className="text-gray-600">Affiliation Fee</div>
                        </div>
                    </div>
                </CardContent>
            </Card>*/}
        </div>
    );
} 