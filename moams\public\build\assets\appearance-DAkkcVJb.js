import{i as c,r as m,j as e,g as i,x as u,B as h,L as f,H as g}from"./app-DL-qYY5V.js";import{H as y}from"./heading-small-DGiEkx99.js";import{A as v}from"./app-layout-YqstQnqE.js";import{P as j}from"./index-BTzg1GwG.js";import"./index-1tPPImJd.js";import"./app-logo-icon-BnXlkpcX.js";import"./bus-CKTBKI0T.js";import"./dollar-sign-Cx0-nQIX.js";import"./users-DNGXY-sJ.js";import"./clipboard-list-CxRumT8v.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const k=[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]],N=c("Monitor",k);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const b=[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]],w=c("Moon",b);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const A=[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]],M=c("Sun",A);var S="Separator",p="horizontal",_=["horizontal","vertical"],x=m.forwardRef((a,t)=>{const{decorative:r,orientation:s=p,...o}=a,n=z(s)?s:p,l=r?{role:"none"}:{"aria-orientation":n==="vertical"?n:void 0,role:"separator"};return e.jsx(j.div,{"data-orientation":n,...l,...o,ref:t})});x.displayName=S;function z(a){return _.includes(a)}var L=x;function O({className:a,orientation:t="horizontal",decorative:r=!0,...s}){return e.jsx(L,{"data-slot":"separator-root",decorative:r,orientation:t,className:i("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",a),...s})}function P({className:a="",...t}){const{appearance:r,updateAppearance:s}=u(),o=[{value:"light",icon:M,label:"Light"},{value:"dark",icon:w,label:"Dark"},{value:"system",icon:N,label:"System"}];return e.jsx("div",{className:i("inline-flex gap-1 rounded-lg bg-neutral-100 p-1 dark:bg-neutral-800",a),...t,children:o.map(({value:n,icon:d,label:l})=>e.jsxs("button",{onClick:()=>s(n),className:i("flex items-center rounded-md px-3.5 py-1.5 transition-colors",r===n?"bg-white shadow-xs dark:bg-neutral-700 dark:text-neutral-100":"text-neutral-500 hover:bg-neutral-200/60 hover:text-black dark:text-neutral-400 dark:hover:bg-neutral-700/60"),children:[e.jsx(d,{className:"-ml-1 h-4 w-4"}),e.jsx("span",{className:"ml-1.5 text-sm",children:l})]},n))})}function T({title:a,description:t}){return e.jsxs("div",{className:"mb-8 space-y-0.5",children:[e.jsx("h2",{className:"text-xl font-semibold tracking-tight",children:a}),t&&e.jsx("p",{className:"text-muted-foreground text-sm",children:t})]})}const E=[{title:"Password",url:"/settings/password",icon:null}];function I({children:a}){if(typeof window>"u")return null;const t=window.location.pathname;return e.jsxs("div",{className:"px-4 py-6",children:[e.jsx(T,{title:"Settings",description:"Manage your profile and account settings"}),e.jsxs("div",{className:"flex flex-col space-y-8 lg:flex-row lg:space-y-0 lg:space-x-12",children:[e.jsx("aside",{className:"w-full max-w-xl lg:w-48",children:e.jsx("nav",{className:"flex flex-col space-y-1 space-x-0",children:E.map(r=>e.jsx(h,{size:"sm",variant:"ghost",asChild:!0,className:i("w-full justify-start",{"bg-muted":t===r.url}),children:e.jsx(f,{href:r.url,prefetch:!0,children:r.title})},r.url))})}),e.jsx(O,{className:"my-6 md:hidden"}),e.jsx("div",{className:"flex-1 md:max-w-2xl",children:e.jsx("section",{className:"max-w-xl space-y-12",children:a})})]})]})}const H=[{title:"Appearance settings",href:"/settings/appearance"}];function G(){return e.jsxs(v,{breadcrumbs:H,children:[e.jsx(g,{title:"Appearance settings"}),e.jsx(I,{children:e.jsxs("div",{className:"space-y-6",children:[e.jsx(y,{title:"Appearance settings",description:"Update your account's appearance settings"}),e.jsx(P,{})]})})]})}export{G as default};
