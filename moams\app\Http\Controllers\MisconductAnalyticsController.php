<?php

namespace App\Http\Controllers;

use App\Models\Misconduct;
use App\Models\Driver;
use App\Models\User;
use App\Models\VehicleRoute;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Barryvdh\DomPDF\Facade\Pdf;

class MisconductAnalyticsController extends Controller
{
    /**
     * Display misconduct analytics dashboard
     */
    public function index(Request $request)
    {
        $user = auth()->user();

        // Only admins and clerks can access analytics
        if (!$user->hasAnyRole(['system admin', 'association clerk', 'association manager'])) {
            abort(403, 'Access denied. Only administrators and clerks can view analytics.');
        }

        $viewType = $request->get('view', 'monthly');
        $month = $request->get('month', now()->format('Y-m'));

        if ($viewType === 'alltime') {
            // All-time statistics
            $startDate = null;
            $endDate = null;
            $monthlyStats = $this->getAllTimeStatistics();
        } else {
            // Monthly statistics
            $startDate = Carbon::parse($month . '-01')->startOfMonth();
            $endDate = $startDate->copy()->endOfMonth();
            $monthlyStats = $this->getMonthlyStatistics($startDate, $endDate);
        }

        // Misconduct trends (last 6 months)
        $trends = $this->getMisconductTrends();

        // Top offending drivers
        $topOffenders = $this->getTopOffendingDrivers($startDate, $endDate);

        // Misconduct by severity
        $severityBreakdown = $this->getSeverityBreakdown($startDate, $endDate);

        // Route-based statistics
        $routeStats = $this->getRouteStatistics($startDate, $endDate);

        // Resolution breakdown
        $resolutionBreakdown = $this->getResolutionBreakdown($startDate, $endDate);

        return Inertia::render('misconductManagement/analytics', [
            'monthlyStats' => $monthlyStats,
            'trends' => $trends,
            'topOffenders' => $topOffenders,
            'severityBreakdown' => $severityBreakdown,
            'resolutionBreakdown' => $resolutionBreakdown,
            'routeStats' => $routeStats,
            'selectedMonth' => $month,
            'viewType' => $viewType,
            'userRoles' => $user->roles->pluck('name')->toArray(),
        ]);
    }

    /**
     * Generate PDF report
     */
    public function generateReport(Request $request)
    {
        $user = auth()->user();

        if (!$user->hasAnyRole(['system admin', 'association clerk', 'association manager'])) {
            abort(403, 'Access denied.');
        }

        $viewType = $request->get('view', 'monthly');
        $month = $request->get('month', now()->format('Y-m'));

        if ($viewType === 'alltime') {
            $startDate = null;
            $endDate = null;
            $monthlyStats = $this->getAllTimeStatistics();
            $reportTitle = 'All-Time';
            $filename = 'misconduct-report-alltime.pdf';
        } else {
            $startDate = Carbon::parse($month . '-01')->startOfMonth();
            $endDate = $startDate->copy()->endOfMonth();
            $monthlyStats = $this->getMonthlyStatistics($startDate, $endDate);
            $reportTitle = $startDate->format('F Y');
            $filename = 'misconduct-report-' . $month . '.pdf';
        }

        $data = [
            'monthlyStats' => $monthlyStats,
            'trends' => $this->getMisconductTrends(),
            'topOffenders' => $this->getTopOffendingDrivers($startDate, $endDate),
            'severityBreakdown' => $this->getSeverityBreakdown($startDate, $endDate),
            'resolutionBreakdown' => $this->getResolutionBreakdown($startDate, $endDate),
            'routeStats' => $this->getRouteStatistics($startDate, $endDate),
            'month' => $reportTitle,
            'viewType' => $viewType,
            'generatedBy' => $user->first_name . ' ' . $user->last_name,
            'generatedAt' => now()->format('F j, Y \a\t g:i A'),
            'logoBase64' => null, // Skip logo to avoid GD dependency
        ];

        $pdf = Pdf::loadView('reports.misconduct-analytics', $data);

        return $pdf->download($filename);
    }

    private function getMonthlyStatistics($startDate, $endDate)
    {
        $totalMisconducts = Misconduct::whereBetween('offense_date', [$startDate, $endDate])->count();

        $severityCounts = Misconduct::whereBetween('offense_date', [$startDate, $endDate])
            ->select('severity', DB::raw('count(*) as count'))
            ->groupBy('severity')
            ->pluck('count', 'severity')
            ->toArray();

        $resolutionCounts = Misconduct::whereBetween('offense_date', [$startDate, $endDate])
            ->select('resolution_status', DB::raw('count(*) as count'))
            ->groupBy('resolution_status')
            ->pluck('count', 'resolution_status')
            ->toArray();

        $uniqueDrivers = Misconduct::whereBetween('offense_date', [$startDate, $endDate])
            ->distinct('offender_id')
            ->count();

        $averageTrustScore = Driver::whereHas('misconducts', function ($query) use ($startDate, $endDate) {
            $query->whereBetween('offense_date', [$startDate, $endDate]);
        })->avg('trust_score');

        $resolutionRate = $totalMisconducts > 0
            ? round(($resolutionCounts['resolved'] ?? 0) / $totalMisconducts * 100, 1)
            : 0;

        return [
            'total_misconducts' => $totalMisconducts,
            'severity_counts' => $severityCounts,
            'resolution_counts' => $resolutionCounts,
            'resolution_rate' => $resolutionRate,
            'unique_drivers' => $uniqueDrivers,
            'average_trust_score' => round($averageTrustScore, 1),
            'low_trust_drivers' => Driver::where('trust_score', '<', 60)->count(),
        ];
    }

    private function getAllTimeStatistics()
    {
        $totalMisconducts = Misconduct::count();

        $severityCounts = Misconduct::select('severity', DB::raw('count(*) as count'))
            ->groupBy('severity')
            ->pluck('count', 'severity')
            ->toArray();

        $resolutionCounts = Misconduct::select('resolution_status', DB::raw('count(*) as count'))
            ->groupBy('resolution_status')
            ->pluck('count', 'resolution_status')
            ->toArray();

        $uniqueDrivers = Misconduct::distinct('offender_id')->count();

        $averageTrustScore = Driver::whereHas('misconducts')->avg('trust_score');

        $resolutionRate = $totalMisconducts > 0
            ? round(($resolutionCounts['resolved'] ?? 0) / $totalMisconducts * 100, 1)
            : 0;

        return [
            'total_misconducts' => $totalMisconducts,
            'severity_counts' => $severityCounts,
            'resolution_counts' => $resolutionCounts,
            'resolution_rate' => $resolutionRate,
            'unique_drivers' => $uniqueDrivers,
            'average_trust_score' => round($averageTrustScore, 1),
            'low_trust_drivers' => Driver::where('trust_score', '<', 60)->count(),
        ];
    }

    private function getMisconductTrends()
    {
        $trends = [];
        for ($i = 5; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $startDate = $date->copy()->startOfMonth();
            $endDate = $date->copy()->endOfMonth();

            $count = Misconduct::whereBetween('offense_date', [$startDate, $endDate])->count();

            $trends[] = [
                'month' => $date->format('M Y'),
                'count' => $count,
            ];
        }

        return $trends;
    }

    private function getTopOffendingDrivers($startDate, $endDate)
    {
        return Driver::withCount([
            'misconducts' => function ($query) use ($startDate, $endDate) {
                if ($startDate && $endDate) {
                    $query->whereBetween('offense_date', [$startDate, $endDate]);
                }
            }
        ])
            ->with('minibus')
            ->having('misconducts_count', '>', 0)
            ->orderBy('misconducts_count', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($driver) {
                return [
                    'name' => $driver->full_name,
                    'trust_score' => $driver->trust_score,
                    'misconduct_count' => $driver->misconducts_count,
                    'minibus' => $driver->minibus ? $driver->minibus->number_plate : 'N/A',
                ];
            });
    }

    private function getSeverityBreakdown($startDate, $endDate)
    {
        $query = Misconduct::query();

        if ($startDate && $endDate) {
            $query->whereBetween('offense_date', [$startDate, $endDate]);
        }

        return $query->select('severity', DB::raw('count(*) as count'))
            ->groupBy('severity')
            ->get()
            ->map(function ($item) {
                return [
                    'severity' => ucfirst($item->severity),
                    'count' => $item->count,
                    'color' => match ($item->severity) {
                        'low' => 'green',
                        'medium' => 'orange',
                        'high' => 'red',
                        default => 'gray'
                    }
                ];
            });
    }

    private function getRouteStatistics($startDate, $endDate)
    {
        $query = Misconduct::query();

        if ($startDate && $endDate) {
            $query->whereBetween('offense_date', [$startDate, $endDate]);
        }

        return $query->join('drivers', 'misconducts.offender_id', '=', 'drivers.id')
            ->join('minibuses', 'drivers.minibus_id', '=', 'minibuses.id')
            ->join('vehicle_routes', 'minibuses.route_id', '=', 'vehicle_routes.id')
            ->select('vehicle_routes.name as route_name', DB::raw('count(*) as misconduct_count'))
            ->groupBy('vehicle_routes.id', 'vehicle_routes.name')
            ->orderBy('misconduct_count', 'desc')
            ->limit(10)
            ->get();
    }

    private function getResolutionBreakdown($startDate, $endDate)
    {
        $query = Misconduct::query();

        if ($startDate && $endDate) {
            $query->whereBetween('offense_date', [$startDate, $endDate]);
        }

        return $query->select('resolution_status', DB::raw('count(*) as count'))
            ->groupBy('resolution_status')
            ->get()
            ->map(function ($item) {
                return [
                    'status' => ucfirst($item->resolution_status),
                    'count' => $item->count,
                    'color' => $item->resolution_status === 'resolved' ? 'green' : 'orange'
                ];
            });
    }
}
